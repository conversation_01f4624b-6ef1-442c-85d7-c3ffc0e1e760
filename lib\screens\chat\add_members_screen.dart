import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/message_controller.dart';
import '../../controllers/user_controller.dart';
import '../../models/chat_group_model.dart';
import '../../models/user_model.dart';

class AddMembersScreen extends StatefulWidget {
  final ChatGroup chatGroup;

  const AddMembersScreen({
    super.key,
    required this.chatGroup,
  });

  @override
  State<AddMembersScreen> createState() => _AddMembersScreenState();
}

class _AddMembersScreenState extends State<AddMembersScreen> {
  final _messageController = Get.find<MessageController>();
  final _userController = Get.find<UserController>();
  final _authController = Get.find<AuthController>();

  final _searchController = TextEditingController();
  final _selectedUsers = <String>[].obs;
  final _isLoading = false.obs;

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadUsers() async {
    _isLoading.value = true;
    try {
      await _userController.loadAllUsers();

      // Load group members to exclude them from the list
      await _messageController.loadGroupMembers(widget.chatGroup.id);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load users: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  List<User> _getFilteredUsers() {
    final currentUserId = _authController.currentUser.value?.id ?? '';
    final existingMemberIds = _messageController.groupMembers
        .map((member) => member.userId)
        .toList();

    // Filter out users who are already members and the current user
    final filteredUsers = _userController.users
        .where((user) =>
            !existingMemberIds.contains(user.id) &&
            user.id != currentUserId)
        .toList();

    // Apply search filter if needed
    if (_searchController.text.isNotEmpty) {
      return filteredUsers
          .where((user) =>
              user.name.toLowerCase().contains(_searchController.text.toLowerCase()) ||
              user.email.toLowerCase().contains(_searchController.text.toLowerCase()))
          .toList();
    }

    return filteredUsers;
  }

  Future<void> _addSelectedMembers() async {
    if (_selectedUsers.isEmpty) {
      Get.snackbar(
        'Error',
        'Please select at least one user to add',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    _isLoading.value = true;
    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        Get.snackbar(
          'Error',
          'User not logged in',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      final result = await _messageController.addMembersToGroup(
        widget.chatGroup.id,
        _selectedUsers,
        currentUser.id,
      );

      if (result) {
        Get.back();
        Get.snackbar(
          'Success',
          'Members added successfully',
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        Get.snackbar(
          'Error',
          'Failed to add members: ${_messageController.error.value}',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to add members: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Members'),
        actions: [
          Obx(() => _isLoading.value
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    ),
                  ),
                )
              : TextButton(
                  onPressed: _addSelectedMembers,
                  child: Text(
                    'Add',
                    style: AppStyles.titleSmall.copyWith(
                      color: Colors.white,
                    ),
                  ),
                )),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search users...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {});
              },
            ),
          ),

          // Selected users count
          Obx(() => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    Text(
                      'Selected: ${_selectedUsers.length}',
                      style: AppStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    if (_selectedUsers.isNotEmpty)
                      TextButton(
                        onPressed: () {
                          _selectedUsers.clear();
                        },
                        child: const Text('Clear All'),
                      ),
                  ],
                ),
              )),

          // User list
          Expanded(
            child: Obx(() {
              if (_isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              final users = _getFilteredUsers();

              if (users.isEmpty) {
                return const Center(
                  child: Text('No users available to add'),
                );
              }

              return ListView.builder(
                itemCount: users.length,
                itemBuilder: (context, index) {
                  final user = users[index];
                  return Obx(() => CheckboxListTile(
                        value: _selectedUsers.contains(user.id),
                        onChanged: (selected) {
                          if (selected == true) {
                            _selectedUsers.add(user.id);
                          } else {
                            _selectedUsers.remove(user.id);
                          }
                        },
                        title: Text(user.name),
                        subtitle: Text(user.email),
                        secondary: CircleAvatar(
                          backgroundColor: AppColors.accent,
                          backgroundImage: user.profileImage != null
                              ? NetworkImage(user.profileImage!)
                              : null,
                          child: user.profileImage == null
                              ? Text(
                                  user.name.substring(0, 1).toUpperCase(),
                                  style: const TextStyle(color: Colors.white),
                                )
                              : null,
                        ),
                      ));
                },
              );
            }),
          ),
        ],
      ),
    );
  }
}
