﻿using System;
using System.Collections.Generic;

namespace webApi.Models;

public partial class ArchiveDocument
{
    public int Id { get; set; }

    public string Title { get; set; } = null!;

    public string? Description { get; set; }

    public string FilePath { get; set; } = null!;

    public string FileName { get; set; } = null!;

    public long FileSize { get; set; }

    public string FileType { get; set; } = null!;

    public int? CategoryId { get; set; }

    public string? Metadata { get; set; }

    public int UploadedBy { get; set; }

    public long UploadedAt { get; set; }

    public int CreatedBy { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public bool IsDeleted { get; set; }

    public virtual ArchiveCategory? Category { get; set; }

    public virtual User UploadedByNavigation { get; set; } = null!;

    public virtual User CreatedByNavigation { get; set; } = null!;

    public virtual ICollection<ArchiveTag> Tags { get; set; } = new List<ArchiveTag>();
}
