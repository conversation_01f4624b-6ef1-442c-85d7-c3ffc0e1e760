import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/database_management_controller.dart';
import '../../database/database_helper.dart';
import '../../services/database_management_service.dart';
import 'database_management_screen.dart';

/// تبويب إدارة قاعدة البيانات
///
/// توفر واجهة للتحكم في جداول قاعدة البيانات المختلفة
class DatabaseManagementTab extends StatefulWidget {
  const DatabaseManagementTab({super.key});

  @override
  State<DatabaseManagementTab> createState() => _DatabaseManagementTabState();
}

class _DatabaseManagementTabState extends State<DatabaseManagementTab> {
  @override
  void initState() {
    super.initState();

    // إنشاء المتحكم إذا لم يكن موجودًا
    if (!Get.isRegistered<DatabaseManagementController>()) {
      final databaseHelper = Get.find<DatabaseHelper>();
      final databaseManagementService = DatabaseManagementService(databaseHelper);
      Get.put(DatabaseManagementController(databaseManagementService));
    }
  }

  @override
  Widget build(BuildContext context) {
    return const DatabaseManagementScreen();
  }
}
