﻿using System;
using System.Collections.Generic;

namespace webApi.Models;

public partial class DashboardWidget
{
    public int Id { get; set; }

    public int DashboardId { get; set; }

    public string WidgetType { get; set; } = null!;

    public string Title { get; set; } = null!;

    public string DataSource { get; set; } = null!;

    public string? Query { get; set; }

    public int PositionX { get; set; }

    public int PositionY { get; set; }

    public int Width { get; set; }

    public int Height { get; set; }

    public string? Settings { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public bool IsDeleted { get; set; }

    public string? Configuration { get; set; }

    public string? Position { get; set; }

    public string? Size { get; set; }

    public int? OrderIndex { get; set; }

    public bool IsVisible { get; set; }

    public int? RefreshInterval { get; set; }

    public virtual Dashboard Dashboard { get; set; } = null!;
}
