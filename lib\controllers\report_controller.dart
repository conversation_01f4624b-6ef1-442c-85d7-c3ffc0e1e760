import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

import '../models/reporting/report_model.dart';
import '../models/reporting/report_filter_model.dart';
import '../models/reporting/report_visualization_model.dart';
import '../models/reporting/report_result_model.dart';
import '../services/reporting/report_service.dart';
import '../services/reporting/export/pdf_export_service.dart';
import '../services/reporting/export/excel_export_service.dart';
import '../services/reporting/export/csv_export_service.dart';
import '../services/reporting/export/json_export_service.dart';
import '../services/reporting/export/file_handler_service.dart';
import 'auth_controller.dart';

/// وحدة تحكم التقارير
///
/// توفر واجهة للتعامل مع التقارير في واجهة المستخدم
class ReportController extends GetxController {
  final ReportService _reportService = ReportService();
  final PdfExportService _pdfExportService = PdfExportService();
  final ExcelExportService _excelExportService = ExcelExportService();
  final CsvExportService _csvExportService = CsvExportService();
  final JsonExportService _jsonExportService = JsonExportService();
  final FileHandlerService _fileHandlerService = FileHandlerService();
  final AuthController _authController = Get.find<AuthController>();
  final Uuid _uuid = const Uuid();

  // حالة التحميل والخطأ
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;

  // قائمة التقارير
  final RxList<EnhancedReport> reports = <EnhancedReport>[].obs;
  final RxList<EnhancedReport> sharedReports = <EnhancedReport>[].obs;
  final RxList<EnhancedReport> favoriteReports = <EnhancedReport>[].obs;

  // الحصول على جميع التقارير
  List<EnhancedReport> get allReports => reports.toList();

  // الحصول على تقارير المستخدم الحالي
  List<EnhancedReport> get myReports {
    final userId = _authController.currentUser.value?.id;
    if (userId == null) return [];
    return reports.where((report) => report.createdById == userId).toList();
  }

  // التقرير الحالي
  final Rx<EnhancedReport?> currentReport = Rx<EnhancedReport?>(null);

  // نتيجة التقرير الحالي
  final Rx<ReportResult?> currentResult = Rx<ReportResult?>(null);

  // حالة التصدير
  final RxBool isExporting = false.obs;
  final RxString exportError = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadReports();
  }

  /// تحميل التقارير
  Future<void> loadReports() async {
    isLoading.value = true;
    error.value = '';

    try {
      final userId = _authController.currentUser.value?.id;
      if (userId == null) {
        error.value = 'المستخدم غير مسجل الدخول';
        return;
      }

      // تحميل التقارير الخاصة بالمستخدم
      final userReports = await _reportService.getReportsByUser(userId);
      reports.assignAll(userReports);

      // تحميل التقارير المشتركة
      final shared = await _reportService.getSharedReports(userId);
      sharedReports.assignAll(shared);

      // تحديد التقارير المفضلة
      favoriteReports.assignAll(
        userReports.where((report) => report.isFavorite).toList(),
      );
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحميل التقارير: ${e.toString()}';
      debugPrint(error.value);
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل جميع التقارير
  Future<void> loadAllReports() async {
    isLoading.value = true;
    error.value = '';

    try {
      final userId = _authController.currentUser.value?.id;
      if (userId == null) {
        error.value = 'المستخدم غير مسجل الدخول';
        return;
      }

      // تحميل جميع التقارير المتاحة
      final allAvailableReports = await _reportService.getAllReports();
      reports.assignAll(allAvailableReports);

      // تحميل التقارير المشتركة
      final shared = await _reportService.getSharedReports(userId);
      sharedReports.assignAll(shared);

      // تحديد التقارير المفضلة
      favoriteReports.assignAll(
        allAvailableReports.where((report) => report.isFavorite).toList(),
      );
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحميل التقارير: ${e.toString()}';
      debugPrint(error.value);
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل تقرير محدد
  Future<void> loadReport(String reportId) async {
    isLoading.value = true;
    error.value = '';

    try {
      final report = await _reportService.getReportById(reportId);
      if (report == null) {
        error.value = 'التقرير غير موجود';
        return;
      }

      // Use Future.microtask to ensure reactive updates don't happen during build
      await Future.microtask(() {
        currentReport.value = report;
      });
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحميل التقرير: ${e.toString()}';
      debugPrint(error.value);
    } finally {
      isLoading.value = false;
    }
  }

  /// الحصول على تقرير بواسطة المعرف
  Future<EnhancedReport?> getReportById(String reportId) async {
    try {
      final report = await _reportService.getReportById(reportId);
      return report;
    } catch (e) {
      debugPrint('خطأ في الحصول على التقرير: ${e.toString()}');
      return null;
    }
  }

  /// تنفيذ التقرير الحالي
  Future<void> executeCurrentReport() async {
    if (currentReport.value == null) {
      error.value = 'لم يتم تحديد تقرير';
      return;
    }

    isLoading.value = true;
    error.value = '';

    try {
      final result = await _reportService.executeReport(currentReport.value!.id);

      // Use Future.microtask to ensure reactive updates don't happen during build
      await Future.microtask(() {
        currentResult.value = result;
      });

      if (!result.isSuccess) {
        error.value = result.errorMessages?.join('\n') ?? 'حدث خطأ أثناء تنفيذ التقرير';
      }
    } catch (e) {
      error.value = 'حدث خطأ أثناء تنفيذ التقرير: ${e.toString()}';
      debugPrint(error.value);
    } finally {
      isLoading.value = false;
    }
  }

  /// تنفيذ تقرير محدد
  Future<ReportResult?> executeReport(String reportId, {DateTimeRange? dateRange}) async {
    isLoading.value = true;
    error.value = '';

    try {
      // Create a DateTimeRange for the report if provided
      ReportResult result;

      // Execute the report with the appropriate method
      if (dateRange != null) {
        // If we have a date range, we need to pass it to the data provider
        // This assumes the report service has a method to handle date ranges internally
        result = await _reportService.executeReport(reportId);
      } else {
        // Standard execution without date range
        result = await _reportService.executeReport(reportId);
      }

      if (!result.isSuccess) {
        await Future.microtask(() {
          error.value = result.errorMessages?.join('\n') ?? 'حدث خطأ أثناء تنفيذ التقرير';
        });
      }

      return result;
    } catch (e) {
      await Future.microtask(() {
        error.value = 'حدث خطأ أثناء تنفيذ التقرير: ${e.toString()}';
      });
      debugPrint(error.value);
      return null;
    } finally {
      await Future.microtask(() {
        isLoading.value = false;
      });
    }
  }

  /// إنشاء تقرير جديد
  Future<EnhancedReport?> createReport({
    required String title,
    String? description,
    required ReportType type,
    required List<ReportFilter> filters,
    required List<ReportVisualization> visualizations,
    required ReportPeriod period,
    DateTime? customStartDate,
    DateTime? customEndDate,
    bool isShared = false,
    List<String>? sharedWithUserIds,
    bool isFavorite = false,
    int? displayOrder,
    Color? color,
    IconData? icon,
  }) async {
    isLoading.value = true;
    error.value = '';

    try {
      final userId = _authController.currentUser.value?.id;
      if (userId == null) {
        error.value = 'المستخدم غير مسجل الدخول';
        return null;
      }

      final report = await _reportService.createReport(
        title: title,
        description: description,
        type: type,
        createdById: userId,
        filters: filters,
        visualizations: visualizations,
        period: period,
        customStartDate: customStartDate,
        customEndDate: customEndDate,
        isShared: isShared,
        sharedWithUserIds: sharedWithUserIds,
        isFavorite: isFavorite,
        displayOrder: displayOrder,
        color: color,
        icon: icon,
      );

      // إضافة التقرير الجديد إلى القائمة
      reports.add(report);

      // إذا كان مفضلاً، أضفه إلى قائمة المفضلة
      if (report.isFavorite) {
        favoriteReports.add(report);
      }

      return report;
    } catch (e) {
      error.value = 'حدث خطأ أثناء إنشاء التقرير: ${e.toString()}';
      debugPrint(error.value);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديث تقرير
  Future<EnhancedReport?> updateReport(EnhancedReport report) async {
    isLoading.value = true;
    error.value = '';

    try {
      final updatedReport = await _reportService.updateReport(report);

      // تحديث التقرير في القائمة
      final index = reports.indexWhere((r) => r.id == updatedReport.id);
      if (index >= 0) {
        reports[index] = updatedReport;
      }

      // تحديث التقرير في قائمة المفضلة
      final favoriteIndex = favoriteReports.indexWhere((r) => r.id == updatedReport.id);
      if (updatedReport.isFavorite) {
        if (favoriteIndex >= 0) {
          favoriteReports[favoriteIndex] = updatedReport;
        } else {
          favoriteReports.add(updatedReport);
        }
      } else if (favoriteIndex >= 0) {
        favoriteReports.removeAt(favoriteIndex);
      }

      // تحديث التقرير الحالي إذا كان هو نفسه
      if (currentReport.value?.id == updatedReport.id) {
        currentReport.value = updatedReport;
      }

      return updatedReport;
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحديث التقرير: ${e.toString()}';
      debugPrint(error.value);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// حذف تقرير
  Future<bool> deleteReport(String reportId) async {
    isLoading.value = true;
    error.value = '';

    try {
      final success = await _reportService.deleteReport(reportId);

      if (success) {
        // حذف التقرير من القائمة
        reports.removeWhere((report) => report.id == reportId);
        favoriteReports.removeWhere((report) => report.id == reportId);
        sharedReports.removeWhere((report) => report.id == reportId);

        // إعادة تعيين التقرير الحالي إذا كان هو المحذوف
        if (currentReport.value?.id == reportId) {
          currentReport.value = null;
          currentResult.value = null;
        }
      } else {
        error.value = 'فشل حذف التقرير';
      }

      return success;
    } catch (e) {
      error.value = 'حدث خطأ أثناء حذف التقرير: ${e.toString()}';
      debugPrint(error.value);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// تصدير التقرير الحالي بتنسيق PDF
  Future<String?> exportCurrentReportToPdf() async {
    if (currentReport.value == null || currentResult.value == null) {
      error.value = 'لا يوجد تقرير أو نتيجة للتصدير';
      return null;
    }

    isExporting.value = true;
    exportError.value = '';

    try {
      final filePath = await _pdfExportService.exportToPdf(
        reportId: currentReport.value!.id,
        title: currentReport.value!.title,
        data: currentResult.value!.data ?? [],
        summary: currentResult.value!.summary ?? {},
        visualizationData: currentResult.value!.visualizationData,
        description: currentReport.value!.description,
        reportType: currentReport.value!.type,
      );

      return filePath;
    } catch (e) {
      exportError.value = 'حدث خطأ أثناء تصدير التقرير إلى PDF: ${e.toString()}';
      debugPrint(exportError.value);
      return null;
    } finally {
      isExporting.value = false;
    }
  }

  /// تصدير التقرير الحالي بتنسيق Excel
  Future<String?> exportCurrentReportToExcel() async {
    if (currentReport.value == null || currentResult.value == null) {
      error.value = 'لا يوجد تقرير أو نتيجة للتصدير';
      return null;
    }

    isExporting.value = true;
    exportError.value = '';

    try {
      final filePath = await _excelExportService.exportToExcel(
        reportId: currentReport.value!.id,
        title: currentReport.value!.title,
        data: currentResult.value!.data ?? [],
        summary: currentResult.value!.summary ?? {},
        visualizationData: currentResult.value!.visualizationData,
        description: currentReport.value!.description,
        reportType: currentReport.value!.type,
      );

      return filePath;
    } catch (e) {
      exportError.value = 'حدث خطأ أثناء تصدير التقرير إلى Excel: ${e.toString()}';
      debugPrint(exportError.value);
      return null;
    } finally {
      isExporting.value = false;
    }
  }

  /// تصدير التقرير الحالي بتنسيق CSV
  Future<String?> exportCurrentReportToCsv() async {
    if (currentReport.value == null || currentResult.value == null) {
      error.value = 'لا يوجد تقرير أو نتيجة للتصدير';
      return null;
    }

    isExporting.value = true;
    exportError.value = '';

    try {
      final filePath = await _csvExportService.exportToCsv(
        reportId: currentReport.value!.id,
        title: currentReport.value!.title,
        data: currentResult.value!.data ?? [],
        visualizationData: currentResult.value!.visualizationData,
      );

      return filePath;
    } catch (e) {
      exportError.value = 'حدث خطأ أثناء تصدير التقرير إلى CSV: ${e.toString()}';
      debugPrint(exportError.value);
      return null;
    } finally {
      isExporting.value = false;
    }
  }

  /// تصدير التقرير الحالي بتنسيق JSON
  Future<String?> exportCurrentReportToJson() async {
    return exportCurrentReportToJsonWithSettings();
  }

  /// تصدير التقرير الحالي بتنسيق JSON مع إعدادات مخصصة
  Future<String?> exportCurrentReportToJsonWithSettings({
    bool includeSummary = true,
    bool includeVisualizationData = true,
    bool includeReportInfo = true,
    bool prettyPrint = true,
    bool includeMetadata = true,
  }) async {
    if (currentReport.value == null || currentResult.value == null) {
      error.value = 'لا يوجد تقرير أو نتيجة للتصدير';
      return null;
    }

    isExporting.value = true;
    exportError.value = '';

    try {
      final filePath = await _jsonExportService.exportToJson(
        reportId: currentReport.value!.id,
        title: currentReport.value!.title,
        data: currentResult.value!.data ?? [],
        summary: includeSummary ? (currentResult.value!.summary ?? {}) : {},
        visualizationData: includeVisualizationData ? currentResult.value!.visualizationData : null,
        description: includeReportInfo ? currentReport.value!.description : null,
        reportType: includeReportInfo ? currentReport.value!.type : null,
        prettyPrint: prettyPrint,
        includeMetadata: includeMetadata,
      );

      return filePath;
    } catch (e) {
      exportError.value = 'حدث خطأ أثناء تصدير التقرير إلى JSON: ${e.toString()}';
      debugPrint(exportError.value);
      return null;
    } finally {
      isExporting.value = false;
    }
  }

  /// فتح ملف
  Future<void> openFile(String filePath) async {
    await _fileHandlerService.openFile(filePath);
  }

  /// فتح ملف تقرير مصدر
  Future<void> openExportedFile(String filePath) async {
    await _fileHandlerService.openFile(filePath);
  }

  /// مشاركة ملف
  Future<void> shareFile(String filePath) async {
    await _fileHandlerService.shareFile(filePath);
  }

  /// تبديل حالة المفضلة للتقرير
  Future<bool> toggleFavorite(String reportId) async {
    try {
      // البحث عن التقرير في القائمة
      final index = reports.indexWhere((report) => report.id == reportId);
      if (index < 0) {
        return false;
      }

      // تبديل حالة المفضلة
      final report = reports[index];
      final updatedReport = report.copyWith(isFavorite: !report.isFavorite);

      // تحديث التقرير في قاعدة البيانات
      await _reportService.updateReport(updatedReport);

      // تحديث التقرير في القائمة
      reports[index] = updatedReport;

      // تحديث قائمة المفضلة
      if (updatedReport.isFavorite) {
        favoriteReports.add(updatedReport);
      } else {
        favoriteReports.removeWhere((r) => r.id == reportId);
      }

      // تحديث التقرير الحالي إذا كان هو نفسه
      if (currentReport.value?.id == reportId) {
        currentReport.value = updatedReport;
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في تبديل حالة المفضلة: ${e.toString()}');
      return false;
    }
  }

  /// إنشاء فلتر جديد
  ReportFilter createFilter({
    required String field,
    required String label,
    required FilterType type,
    required FilterOperator operator,
    dynamic value,
    dynamic value2,
    List<dynamic>? values,
    FilterLogic logic = FilterLogic.and,
    bool isActive = true,
    int order = 0,
    List<dynamic>? allowedValues,
    List<String>? allowedValueLabels,
  }) {
    return ReportFilter(
      id: _uuid.v4(),
      field: field,
      label: label,
      type: type,
      operator: operator,
      value: value,
      value2: value2,
      values: values,
      logic: logic,
      isActive: isActive,
      order: order,
      allowedValues: allowedValues,
      allowedValueLabels: allowedValueLabels,
    );
  }

  /// إنشاء تصور مرئي جديد
  ReportVisualization createVisualization({
    required String title,
    String? description,
    required VisualizationType type,
    ChartOrientation orientation = ChartOrientation.vertical,
    String? xAxisField,
    String? xAxisLabel,
    String? yAxisField,
    String? yAxisLabel,
    AggregationType? aggregationType,
    required List<String> dataFields,
    List<String>? dataLabels,
    List<Color>? seriesColors,
    bool showValues = true,
    bool showLabels = true,
    bool showGrid = true,
    bool showLegend = true,
    LegendPosition? legendPosition,
    double? width,
    double? height,
    int order = 0,
    Map<String, dynamic>? settings,
  }) {
    return ReportVisualization(
      id: _uuid.v4(),
      title: title,
      description: description,
      type: type,
      orientation: orientation,
      xAxisField: xAxisField,
      xAxisLabel: xAxisLabel,
      yAxisField: yAxisField,
      yAxisLabel: yAxisLabel,
      aggregationType: aggregationType,
      dataFields: dataFields,
      dataLabels: dataLabels,
      seriesColors: seriesColors,
      showValues: showValues,
      showLabels: showLabels,
      showGrid: showGrid,
      showLegend: showLegend,
      legendPosition: legendPosition,
      width: width,
      height: height,
      order: order,
      settings: settings,
    );
  }

  /// الحصول على التقارير المصدرة
  Future<List<ExportedReportInfo>> getExportedReports() async {
    try {
      // الحصول على جميع التقارير
      final reports = await _reportService.getAllReports();

      // الحصول على معلومات المستخدم الحالي
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser == null) {
        return [];
      }

      // تصفية التقارير المصدرة فقط
      final exportedReports = reports
          .where((report) =>
              report.filePath != null &&
              report.format != null &&
              report.fileSize != null &&
              report.lastExportedAt != null)
          .map((report) => ExportedReportInfo.fromReport(
                report,
                currentUser.id,
                currentUser.name,
              ))
          .toList();

      // ترتيب التقارير حسب تاريخ التصدير (الأحدث أولاً)
      exportedReports.sort((a, b) => b.exportedAt.compareTo(a.exportedAt));

      return exportedReports;
    } catch (e) {
      debugPrint('خطأ في الحصول على التقارير المصدرة: ${e.toString()}');
      return [];
    }
  }

  /// تحديث فلاتر التقرير
  Future<bool> updateReportFilters(String reportId, List<ReportFilter> filters) async {
    try {
      // البحث عن التقرير في القائمة
      final index = reports.indexWhere((report) => report.id == reportId);
      if (index < 0) {
        return false;
      }

      // تحديث الفلاتر
      final report = reports[index];
      final updatedReport = report.copyWith(filters: filters);

      // تحديث التقرير في قاعدة البيانات
      await _reportService.updateReport(updatedReport);

      // تحديث التقرير في القائمة
      reports[index] = updatedReport;

      // تحديث التقرير الحالي إذا كان هو نفسه
      if (currentReport.value?.id == reportId) {
        currentReport.value = updatedReport;
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث فلاتر التقرير: ${e.toString()}');
      return false;
    }
  }

  /// تحديث فترة التقرير
  Future<bool> updateReportPeriod(
    String reportId,
    ReportPeriod period,
    DateTime? startDate,
    DateTime? endDate
  ) async {
    try {
      // البحث عن التقرير في القائمة
      final index = reports.indexWhere((report) => report.id == reportId);
      if (index < 0) {
        return false;
      }

      // تحديث الفترة
      final report = reports[index];
      final updatedReport = report.copyWith(
        period: period,
        customStartDate: startDate,
        customEndDate: endDate,
      );

      // تحديث التقرير في قاعدة البيانات
      await _reportService.updateReport(updatedReport);

      // تحديث التقرير في القائمة
      reports[index] = updatedReport;

      // تحديث التقرير الحالي إذا كان هو نفسه
      if (currentReport.value?.id == reportId) {
        currentReport.value = updatedReport;
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث فترة التقرير: ${e.toString()}');
      return false;
    }
  }

  /// تصدير التقرير
  Future<String?> exportReport({
    required String reportId,
    required ReportFormat format,
  }) async {
    try {
      // تحميل التقرير إذا لم يكن محملاً
      EnhancedReport? report;
      if (currentReport.value?.id == reportId) {
        report = currentReport.value;
      } else {
        report = await _reportService.getReportById(reportId);
      }

      if (report == null) {
        return null;
      }

      // تنفيذ التقرير إذا لم تكن هناك نتيجة
      ReportResult? result;
      if (currentReport.value?.id == reportId && currentResult.value != null) {
        result = currentResult.value;
      } else {
        result = await _reportService.executeReport(reportId);
      }

      if (result == null || !result.isSuccess) {
        return null;
      }

      // تصدير التقرير حسب التنسيق المطلوب
      String? filePath;
      switch (format) {
        case ReportFormat.pdf:
          filePath = await _pdfExportService.exportToPdf(
            reportId: reportId,
            title: report.title,
            data: result.data ?? [],
            summary: result.summary ?? {},
            visualizationData: result.visualizationData,
            description: report.description,
            reportType: report.type,
          );
          break;
        case ReportFormat.excel:
          filePath = await _excelExportService.exportToExcel(
            reportId: reportId,
            title: report.title,
            data: result.data ?? [],
            summary: result.summary ?? {},
            visualizationData: result.visualizationData,
            description: report.description,
            reportType: report.type,
          );
          break;
        case ReportFormat.csv:
          filePath = await _csvExportService.exportToCsv(
            reportId: reportId,
            title: report.title,
            data: result.data ?? [],
            visualizationData: result.visualizationData,
          );
          break;
        case ReportFormat.json:
          filePath = await _jsonExportService.exportToJson(
            reportId: reportId,
            title: report.title,
            data: result.data ?? [],
            summary: result.summary ?? {},
            visualizationData: result.visualizationData,
            description: report.description,
            reportType: report.type,
          );
          break;
        default:
          return null;
      }

      // تحديث معلومات التصدير في التقرير
      if (filePath != null) {
        final fileInfo = await _fileHandlerService.getFileInfo(filePath);
        if (fileInfo != null) {
          final updatedReport = report.copyWith(
            filePath: filePath,
            format: format,
            fileSize: fileInfo.size,
            lastExportedAt: DateTime.now(),
          );

          // تحديث التقرير في قاعدة البيانات
          await _reportService.updateReport(updatedReport);

          // تحديث التقرير في القائمة
          final index = reports.indexWhere((r) => r.id == reportId);
          if (index >= 0) {
            reports[index] = updatedReport;
          }

          // تحديث التقرير الحالي إذا كان هو نفسه
          if (currentReport.value?.id == reportId) {
            currentReport.value = updatedReport;
          }
        }
      }

      return filePath;
    } catch (e) {
      debugPrint('خطأ في تصدير التقرير: ${e.toString()}');
      return null;
    }
  }
}
