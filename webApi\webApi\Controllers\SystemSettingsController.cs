using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة إعدادات النظام
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class SystemSettingsController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public SystemSettingsController(TasksDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// الحصول على جميع إعدادات النظام
        /// </summary>
        /// <returns>قائمة بجميع إعدادات النظام</returns>
        /// <response code="200">إرجاع قائمة إعدادات النظام</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<SystemSetting>>> GetSystemSettings()
        {
            return await _context.SystemSettings
                .Include(ss => ss.CreatedByNavigation)
                .OrderBy(ss => ss.SettingGroup)
                .ThenBy(ss => ss.SettingKey)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على إعداد نظام محدد
        /// </summary>
        /// <param name="id">معرف إعداد النظام</param>
        /// <returns>إعداد النظام المطلوب</returns>
        /// <response code="200">إرجاع إعداد النظام</response>
        /// <response code="404">إعداد النظام غير موجود</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<SystemSetting>> GetSystemSetting(int id)
        {
            var systemSetting = await _context.SystemSettings
                .Include(ss => ss.CreatedByNavigation)
                .FirstOrDefaultAsync(ss => ss.Id == id);

            if (systemSetting == null)
            {
                return NotFound();
            }

            return systemSetting;
        }

        /// <summary>
        /// الحصول على إعداد نظام بالمفتاح
        /// </summary>
        /// <param name="key">مفتاح الإعداد</param>
        /// <returns>إعداد النظام المطلوب</returns>
        /// <response code="200">إرجاع إعداد النظام</response>
        /// <response code="404">إعداد النظام غير موجود</response>
        [HttpGet("key/{key}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<SystemSetting>> GetSystemSettingByKey(string key)
        {
            var systemSetting = await _context.SystemSettings
                .FirstOrDefaultAsync(ss => ss.SettingKey == key);

            if (systemSetting == null)
            {
                return NotFound();
            }

            return systemSetting;
        }

        /// <summary>
        /// الحصول على قيمة إعداد نظام بالمفتاح
        /// </summary>
        /// <param name="key">مفتاح الإعداد</param>
        /// <returns>قيمة إعداد النظام</returns>
        /// <response code="200">إرجاع قيمة الإعداد</response>
        /// <response code="404">إعداد النظام غير موجود</response>
        [HttpGet("value/{key}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<string>> GetSystemSettingValue(string key)
        {
            var systemSetting = await _context.SystemSettings
                .FirstOrDefaultAsync(ss => ss.SettingKey == key);

            if (systemSetting == null)
            {
                return NotFound();
            }

            return systemSetting.SettingValue;
        }

        /// <summary>
        /// الحصول على إعدادات النظام حسب المجموعة
        /// </summary>
        /// <param name="group">مجموعة الإعدادات</param>
        /// <returns>قائمة إعدادات المجموعة</returns>
        /// <response code="200">إرجاع قائمة إعدادات المجموعة</response>
        [HttpGet("group/{group}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<SystemSetting>>> GetSystemSettingsByGroup(string group)
        {
            return await _context.SystemSettings
                .Where(ss => ss.SettingGroup == group)
                .OrderBy(ss => ss.SettingKey)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على جميع مجموعات الإعدادات
        /// </summary>
        /// <returns>قائمة مجموعات الإعدادات</returns>
        /// <response code="200">إرجاع قائمة مجموعات الإعدادات</response>
        [HttpGet("groups")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<string>>> GetSettingGroups()
        {
            var groups = await _context.SystemSettings
                .Where(ss => !string.IsNullOrEmpty(ss.SettingGroup))
                .Select(ss => ss.SettingGroup)
                .Distinct()
                .OrderBy(g => g)
                .ToListAsync();

            return groups.Where(g => !string.IsNullOrEmpty(g)).ToList();
        }

        /// <summary>
        /// إنشاء إعداد نظام جديد
        /// </summary>
        /// <param name="systemSetting">بيانات إعداد النظام</param>
        /// <returns>إعداد النظام المُنشأ</returns>
        /// <response code="201">تم إنشاء إعداد النظام بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<SystemSetting>> PostSystemSetting(SystemSetting systemSetting)
        {
            // التحقق من عدم وجود إعداد بنفس المفتاح
            var existingSetting = await _context.SystemSettings
                .FirstOrDefaultAsync(ss => ss.SettingKey == systemSetting.SettingKey);

            if (existingSetting != null)
            {
                return BadRequest("يوجد إعداد بهذا المفتاح مسبقاً");
            }

            systemSetting.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            systemSetting.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            _context.SystemSettings.Add(systemSetting);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetSystemSetting", new { id = systemSetting.Id }, systemSetting);
        }

        /// <summary>
        /// تحديث إعداد نظام
        /// </summary>
        /// <param name="id">معرف إعداد النظام</param>
        /// <param name="systemSetting">بيانات إعداد النظام المحدثة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث إعداد النظام بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">إعداد النظام غير موجود</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutSystemSetting(int id, SystemSetting systemSetting)
        {
            if (id != systemSetting.Id)
            {
                return BadRequest();
            }

            // التحقق من عدم وجود إعداد آخر بنفس المفتاح
            var existingSetting = await _context.SystemSettings
                .FirstOrDefaultAsync(ss => ss.SettingKey == systemSetting.SettingKey && ss.Id != id);

            if (existingSetting != null)
            {
                return BadRequest("يوجد إعداد آخر بهذا المفتاح مسبقاً");
            }

            systemSetting.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            _context.Entry(systemSetting).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!SystemSettingExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// تحديث قيمة إعداد نظام بالمفتاح
        /// </summary>
        /// <param name="key">مفتاح الإعداد</param>
        /// <param name="value">القيمة الجديدة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث قيمة الإعداد بنجاح</response>
        /// <response code="404">إعداد النظام غير موجود</response>
        [HttpPatch("key/{key}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateSystemSettingValue(string key, [FromBody] string value)
        {
            var systemSetting = await _context.SystemSettings
                .FirstOrDefaultAsync(ss => ss.SettingKey == key);

            if (systemSetting == null)
            {
                return NotFound();
            }

            systemSetting.SettingValue = value;
            systemSetting.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// تحديث عدة إعدادات نظام
        /// </summary>
        /// <param name="settings">قائمة الإعدادات للتحديث</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث الإعدادات بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPatch("bulk-update")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> BulkUpdateSystemSettings([FromBody] Dictionary<string, string> settings)
        {
            if (settings == null || !settings.Any())
            {
                return BadRequest("لا توجد إعدادات للتحديث");
            }

            var keys = settings.Keys.ToList();
            var systemSettings = await _context.SystemSettings
                .Where(ss => keys.Contains(ss.SettingKey))
                .ToListAsync();

            foreach (var setting in systemSettings)
            {
                if (settings.ContainsKey(setting.SettingKey))
                {
                    setting.SettingValue = settings[setting.SettingKey];
                    setting.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                }
            }

            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// حذف إعداد نظام
        /// </summary>
        /// <param name="id">معرف إعداد النظام</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف إعداد النظام بنجاح</response>
        /// <response code="404">إعداد النظام غير موجود</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteSystemSetting(int id)
        {
            var systemSetting = await _context.SystemSettings.FindAsync(id);
            if (systemSetting == null)
            {
                return NotFound();
            }

            _context.SystemSettings.Remove(systemSetting);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// حذف إعداد نظام بالمفتاح
        /// </summary>
        /// <param name="key">مفتاح الإعداد</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف إعداد النظام بنجاح</response>
        /// <response code="404">إعداد النظام غير موجود</response>
        [HttpDelete("key/{key}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteSystemSettingByKey(string key)
        {
            var systemSetting = await _context.SystemSettings
                .FirstOrDefaultAsync(ss => ss.SettingKey == key);

            if (systemSetting == null)
            {
                return NotFound();
            }

            _context.SystemSettings.Remove(systemSetting);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool SystemSettingExists(int id)
        {
            return _context.SystemSettings.Any(e => e.Id == id);
        }
    }
}
