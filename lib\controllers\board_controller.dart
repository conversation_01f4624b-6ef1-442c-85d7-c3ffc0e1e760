import 'package:flutter/material.dart';
import 'package:get/get.dart';


import '../models/board_model.dart';


/// متحكم اللوحات
///
/// يوفر واجهة للتعامل مع اللوحات في النظام
class BoardController extends GetxController {
  // خدمة قاعدة البيانات
  

  // قائمة اللوحات
  final RxList<Board> _boards = <Board>[].obs;

  // حالة التحميل
  final RxBool _isLoading = false.obs;

  // الحصول على قائمة اللوحات
  List<Board> get boards => _boards;

  // الحصول على حالة التحميل
  bool get isLoading => _isLoading.value;

  @override
  void onInit() {
    super.onInit();
    loadBoards();
  }

  /// تحميل اللوحات من قاعدة البيانات
  Future<void> loadBoards() async {
    _isLoading.value = true;
    try {
      final Database db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'boards',
        where: 'is_deleted = ?',
        whereArgs: [0],
      );

      _boards.value = List.generate(maps.length, (i) {
        return Board.fromMap(maps[i]);
      });
    } catch (e) {
      debugPrint('Error loading boards: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// إضافة لوحة جديدة
  Future<void> addBoard(Board board) async {
    _isLoading.value = true;
    try {
      final Database db = await _databaseService.database;
      final int id = await db.insert('boards', board.toMap());

      if (id > 0) {
        _boards.add(board.copyWith(id: id));
      }
    } catch (e) {
      debugPrint('Error adding board: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث لوحة
  Future<void> updateBoard(Board board) async {
    _isLoading.value = true;
    try {
      final Database db = await _databaseService.database;
      await db.update(
        'boards',
        board.toMap(),
        where: 'id = ?',
        whereArgs: [board.id],
      );

      final index = _boards.indexWhere((b) => b.id == board.id);
      if (index >= 0) {
        _boards[index] = board;
      }
    } catch (e) {
      debugPrint('Error updating board: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// حذف لوحة (تحديث حقل is_deleted)
  Future<void> deleteBoard(int id) async {
    _isLoading.value = true;
    try {
      final Database db = await _databaseService.database;
      await db.update(
        'boards',
        {'is_deleted': 1},
        where: 'id = ?',
        whereArgs: [id],
      );

      final index = _boards.indexWhere((b) => b.id == id);
      if (index >= 0) {
        _boards.removeAt(index);
      }
    } catch (e) {
      debugPrint('Error deleting board: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// البحث عن لوحة بالاسم
  List<Board> searchBoards(String query) {
    if (query.isEmpty) {
      return _boards;
    }
    return _boards.where((board) =>
      board.name.toLowerCase().contains(query.toLowerCase()) ||
      board.description.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }
}
