import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/calendar_event_model.dart';
import '../models/task_model.dart';
import '../services/calendar_service.dart';
import 'auth_controller.dart';
import 'task_controller.dart';

/// وحدة تحكم التقويم
/// توفر واجهة للتعامل مع التقويم وأحداثه
class CalendarController extends GetxController {
  final CalendarService _calendarService = Get.find<CalendarService>();
  final AuthController _authController = Get.find<AuthController>();
  final TaskController _taskController = Get.find<TaskController>();

  // قائمة الأحداث الحالية
  RxList<CalendarEvent> get events => _calendarService.events;

  // تاريخ العرض الحالي
  Rx<DateTime> get selectedDate => _calendarService.selectedDate;

  // نطاق العرض الحالي
  Rx<CalendarViewRange> get viewRange => _calendarService.viewRange;

  // حالة التحميل
  RxBool get isLoading => _calendarService.isLoading;

  // رسالة الخطأ
  RxString get errorMessage => _calendarService.errorMessage;

  @override
  void onInit() {
    super.onInit();
    // تحميل الأحداث عند تهيئة وحدة التحكم
    loadEvents();
  }

  /// تحميل الأحداث
  Future<void> loadEvents() async {
    await _calendarService.loadEvents();
    update(); // إضافة update() لتحديث GetBuilder
  }

  /// تحميل الأحداث في نطاق زمني محدد
  Future<void> loadEventsInRange(DateTime start, DateTime end) async {
    await _calendarService.loadEventsInRange(start, end);
    update(); // إضافة update() لتحديث GetBuilder
  }

  /// إنشاء حدث تقويم جديد
  Future<CalendarEvent?> createEvent(CalendarEvent event) async {
    final result = await _calendarService.createEvent(event);
    update(); // إضافة update() لتحديث GetBuilder
    return result;
  }

  /// تحديث حدث تقويم
  Future<bool> updateEvent(CalendarEvent event) async {
    final result = await _calendarService.updateEvent(event);
    update(); // إضافة update() لتحديث GetBuilder
    return result;
  }

  /// حذف حدث تقويم
  Future<bool> deleteEvent(String eventId) async {
    final result = await _calendarService.deleteEvent(eventId);
    update(); // إضافة update() لتحديث GetBuilder
    return result;
  }

  /// الحصول على حدث تقويم بواسطة المعرف
  Future<CalendarEvent?> getEventById(String eventId) async {
    return await _calendarService.getEventById(eventId);
  }

  /// مزامنة المهام مع أحداث التقويم
  Future<int> syncTasksToEvents() async {
    final result = await _calendarService.syncTasksToEvents();
    update(); // إضافة update() لتحديث GetBuilder
    return result;
  }

  /// تغيير نطاق عرض التقويم
  void changeViewRange(CalendarViewRange range) {
    _calendarService.changeViewRange(range);
    update(); // إضافة update() لتحديث GetBuilder
  }

  /// تغيير التاريخ المحدد
  void changeSelectedDate(DateTime date) {
    _calendarService.changeSelectedDate(date);
    update(); // إضافة update() لتحديث GetBuilder
  }

  /// الحصول على أحداث اليوم
  List<CalendarEvent> getEventsForDay(DateTime day) {
    return _calendarService.getEventsForDay(day);
  }

  /// الحصول على أحداث الأسبوع
  List<CalendarEvent> getEventsForWeek(DateTime weekStart) {
    return _calendarService.getEventsForWeek(weekStart);
  }

  /// الحصول على أحداث الشهر
  List<CalendarEvent> getEventsForMonth(DateTime month) {
    return _calendarService.getEventsForMonth(month);
  }

  /// إنشاء حدث من مهمة
  Future<CalendarEvent?> createEventFromTask(Task task) async {
    return await _calendarService.createEventFromTask(task);
  }

  /// تحديث حدث عند تحديث المهمة
  Future<bool> updateEventFromTask(Task task) async {
    return await _calendarService.updateEventFromTask(task);
  }

  /// إنشاء حدث تقويم جديد من البيانات المدخلة
  Future<CalendarEvent?> createNewEvent({
    required String title,
    required String description,
    required DateTime startTime,
    required DateTime endTime,
    required CalendarEventType eventType,
    String? taskId,
    Color? color,
    EventRecurrencePattern? recurrencePattern,
    int? recurrenceCount,
    DateTime? recurrenceEndDate,
    EventReminderTime? reminderTime,
    bool? isReminderEnabled,
  }) async {
    try {
      // التحقق من تسجيل الدخول
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        errorMessage.value = 'لم يتم تسجيل الدخول';
        return null;
      }

      // إنشاء حدث جديد
      final CalendarEvent newEvent = CalendarEvent(
        id: '',
        title: title,
        description: description,
        startTime: startTime,
        endTime: endTime,
        color: color,
        taskId: taskId,
        userId: currentUser.id,
        departmentId: currentUser.departmentId,
        eventType: eventType,
        status: taskId != null ? CalendarEventStatus.pending : null,
        isDeleted: false,
        recurrencePattern: recurrencePattern ?? EventRecurrencePattern.none,
        recurrenceCount: recurrenceCount ?? 0,
        recurrenceEndDate: recurrenceEndDate,
        reminderTime: reminderTime ?? EventReminderTime.none,
        isReminderEnabled: isReminderEnabled ?? false,
      );

      // حفظ الحدث في قاعدة البيانات
      return await createEvent(newEvent);
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إنشاء الحدث: $e';
      debugPrint('خطأ في إنشاء حدث التقويم: $e');
      return null;
    }
  }

  /// تحديث موعد المهمة من الحدث
  Future<bool> updateTaskFromEvent(CalendarEvent event) async {
    try {
      // التحقق من وجود معرف المهمة
      if (event.taskId == null) {
        errorMessage.value = 'الحدث غير مرتبط بمهمة';
        return false;
      }

      // الحصول على المهمة
      await _taskController.loadTaskDetails(event.taskId!);
      final task = _taskController.currentTask.value;

      if (task == null) {
        errorMessage.value = 'لم يتم العثور على المهمة';
        return false;
      }

      // تحديث مواعيد المهمة
      final Task updatedTask = task.copyWith(
        startDate: event.startTime,
        dueDate: event.endTime,
      );

      // حفظ المهمة المحدثة
      final result = await _taskController.getTaskRepository().updateTask(updatedTask);
      return result > 0;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحديث المهمة: $e';
      debugPrint('خطأ في تحديث المهمة من الحدث: $e');
      return false;
    }
  }

  /// الحصول على لون حسب نوع الحدث
  Color getColorForEventType(CalendarEventType eventType) {
    switch (eventType) {
      case CalendarEventType.task:
        return Colors.blue;
      case CalendarEventType.meeting:
        return Colors.orange;
      case CalendarEventType.reminder:
        return Colors.purple;
      case CalendarEventType.vacation:
        return Colors.green;
      case CalendarEventType.other:
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  /// الحصول على اسم نوع الحدث
  String getEventTypeName(CalendarEventType eventType) {
    switch (eventType) {
      case CalendarEventType.task:
        return 'مهمة';
      case CalendarEventType.meeting:
        return 'اجتماع';
      case CalendarEventType.reminder:
        return 'تذكير';
      case CalendarEventType.vacation:
        return 'إجازة';
      case CalendarEventType.other:
        return 'أخرى';
      default:
        return 'مهمة';
    }
  }

  /// الحصول على اسم حالة الحدث
  String getEventStatusName(CalendarEventStatus? status) {
    if (status == null) return '';

    switch (status) {
      case CalendarEventStatus.pending:
        return 'قيد الانتظار';
      case CalendarEventStatus.inProgress:
        return 'قيد التنفيذ';
      case CalendarEventStatus.completed:
        return 'مكتمل';
      case CalendarEventStatus.cancelled:
        return 'ملغي';
      case CalendarEventStatus.waiting:
        return 'في انتظار';
      default:
        return '';
    }
  }
}
