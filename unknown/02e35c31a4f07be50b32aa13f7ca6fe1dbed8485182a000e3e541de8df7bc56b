using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة تتبع الوقت
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class TimeTrackingEntriesController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public TimeTrackingEntriesController(TasksDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// الحصول على جميع سجلات تتبع الوقت
        /// </summary>
        /// <returns>قائمة بجميع سجلات تتبع الوقت</returns>
        /// <response code="200">إرجاع قائمة سجلات تتبع الوقت</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TimeTrackingEntry>>> GetTimeTrackingEntries()
        {
            return await _context.TimeTrackingEntries
                .Include(tte => tte.Task)
                .Include(tte => tte.User)
                .OrderByDescending(tte => tte.StartTime)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على سجل تتبع وقت محدد
        /// </summary>
        /// <param name="id">معرف سجل تتبع الوقت</param>
        /// <returns>سجل تتبع الوقت المطلوب</returns>
        /// <response code="200">إرجاع سجل تتبع الوقت</response>
        /// <response code="404">سجل تتبع الوقت غير موجود</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<TimeTrackingEntry>> GetTimeTrackingEntry(int id)
        {
            var timeTrackingEntry = await _context.TimeTrackingEntries
                .Include(tte => tte.Task)
                .Include(tte => tte.User)
                .FirstOrDefaultAsync(tte => tte.Id == id);

            if (timeTrackingEntry == null)
            {
                return NotFound();
            }

            return timeTrackingEntry;
        }

        /// <summary>
        /// الحصول على سجلات تتبع الوقت لمهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <returns>قائمة سجلات تتبع الوقت للمهمة</returns>
        /// <response code="200">إرجاع قائمة سجلات تتبع الوقت</response>
        [HttpGet("task/{taskId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TimeTrackingEntry>>> GetTimeTrackingEntriesByTask(int taskId)
        {
            return await _context.TimeTrackingEntries
                .Include(tte => tte.User)
                .Where(tte => tte.TaskId == taskId)
                .OrderByDescending(tte => tte.StartTime)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على سجلات تتبع الوقت لمستخدم محدد
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة سجلات تتبع الوقت للمستخدم</returns>
        /// <response code="200">إرجاع قائمة سجلات تتبع الوقت</response>
        [HttpGet("user/{userId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TimeTrackingEntry>>> GetTimeTrackingEntriesByUser(int userId)
        {
            return await _context.TimeTrackingEntries
                .Include(tte => tte.Task)
                .Where(tte => tte.UserId == userId)
                .OrderByDescending(tte => tte.StartTime)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على السجلات النشطة (غير المنتهية) لمستخدم محدد
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة السجلات النشطة</returns>
        /// <response code="200">إرجاع قائمة السجلات النشطة</response>
        [HttpGet("user/{userId}/active")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TimeTrackingEntry>>> GetActiveTimeTrackingEntries(int userId)
        {
            return await _context.TimeTrackingEntries
                .Include(tte => tte.Task)
                .Where(tte => tte.UserId == userId && tte.EndTime == null)
                .OrderByDescending(tte => tte.StartTime)
                .ToListAsync();
        }

        /// <summary>
        /// إجمالي الوقت المسجل لمهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <returns>إجمالي الوقت بالثواني</returns>
        /// <response code="200">إرجاع إجمالي الوقت</response>
        [HttpGet("task/{taskId}/total-time")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<long>> GetTotalTimeForTask(int taskId)
        {
            var totalTime = await _context.TimeTrackingEntries
                .Where(tte => tte.TaskId == taskId && tte.Duration.HasValue)
                .SumAsync(tte => tte.Duration ?? 0);

            return totalTime;
        }

        /// <summary>
        /// إجمالي الوقت المسجل لمستخدم محدد
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="startDate">تاريخ البداية (Unix timestamp)</param>
        /// <param name="endDate">تاريخ النهاية (Unix timestamp)</param>
        /// <returns>إجمالي الوقت بالثواني</returns>
        /// <response code="200">إرجاع إجمالي الوقت</response>
        [HttpGet("user/{userId}/total-time")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<long>> GetTotalTimeForUser(int userId, [FromQuery] long? startDate = null, [FromQuery] long? endDate = null)
        {
            var query = _context.TimeTrackingEntries
                .Where(tte => tte.UserId == userId && tte.Duration.HasValue);

            if (startDate.HasValue)
                query = query.Where(tte => tte.StartTime >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(tte => tte.StartTime <= endDate.Value);

            var totalTime = await query.SumAsync(tte => tte.Duration ?? 0);

            return totalTime;
        }

        /// <summary>
        /// إنشاء سجل تتبع وقت جديد
        /// </summary>
        /// <param name="timeTrackingEntry">بيانات سجل تتبع الوقت</param>
        /// <returns>سجل تتبع الوقت المُنشأ</returns>
        /// <response code="201">تم إنشاء سجل تتبع الوقت بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<TimeTrackingEntry>> PostTimeTrackingEntry(TimeTrackingEntry timeTrackingEntry)
        {
            // التحقق من وجود المهمة
            var taskExists = await _context.Tasks.AnyAsync(t => t.Id == timeTrackingEntry.TaskId && !t.IsDeleted);
            if (!taskExists)
            {
                return BadRequest("المهمة غير موجودة");
            }

            // التحقق من وجود المستخدم
            var userExists = await _context.Users.AnyAsync(u => u.Id == timeTrackingEntry.UserId && !u.IsDeleted);
            if (!userExists)
            {
                return BadRequest("المستخدم غير موجود");
            }

            // تعيين وقت البداية إذا لم يكن محدد
            if (timeTrackingEntry.StartTime == 0)
            {
                timeTrackingEntry.StartTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            }

            _context.TimeTrackingEntries.Add(timeTrackingEntry);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetTimeTrackingEntry", new { id = timeTrackingEntry.Id }, timeTrackingEntry);
        }

        /// <summary>
        /// بدء تتبع الوقت لمهمة
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="description">وصف العمل</param>
        /// <returns>سجل تتبع الوقت المُنشأ</returns>
        /// <response code="201">تم بدء تتبع الوقت بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost("start")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<TimeTrackingEntry>> StartTimeTracking(
            [FromQuery] int taskId, 
            [FromQuery] int userId, 
            [FromQuery] string? description = null)
        {
            // التحقق من عدم وجود سجل نشط للمستخدم
            var activeEntry = await _context.TimeTrackingEntries
                .FirstOrDefaultAsync(tte => tte.UserId == userId && tte.EndTime == null);

            if (activeEntry != null)
            {
                return BadRequest("يوجد سجل تتبع وقت نشط للمستخدم");
            }

            var timeTrackingEntry = new TimeTrackingEntry
            {
                TaskId = taskId,
                UserId = userId,
                Description = description,
                StartTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };

            return await PostTimeTrackingEntry(timeTrackingEntry);
        }

        /// <summary>
        /// إيقاف تتبع الوقت
        /// </summary>
        /// <param name="id">معرف سجل تتبع الوقت</param>
        /// <returns>سجل تتبع الوقت المحدث</returns>
        /// <response code="200">تم إيقاف تتبع الوقت بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">سجل تتبع الوقت غير موجود</response>
        [HttpPatch("{id}/stop")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<TimeTrackingEntry>> StopTimeTracking(int id)
        {
            var timeTrackingEntry = await _context.TimeTrackingEntries.FindAsync(id);
            if (timeTrackingEntry == null)
            {
                return NotFound();
            }

            if (timeTrackingEntry.EndTime.HasValue)
            {
                return BadRequest("تم إيقاف تتبع الوقت مسبقاً");
            }

            var endTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            timeTrackingEntry.EndTime = endTime;
            timeTrackingEntry.Duration = (int?)(endTime - timeTrackingEntry.StartTime);

            await _context.SaveChangesAsync();

            return timeTrackingEntry;
        }

        /// <summary>
        /// تحديث سجل تتبع وقت
        /// </summary>
        /// <param name="id">معرف سجل تتبع الوقت</param>
        /// <param name="timeTrackingEntry">بيانات سجل تتبع الوقت المحدثة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث سجل تتبع الوقت بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">سجل تتبع الوقت غير موجود</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutTimeTrackingEntry(int id, TimeTrackingEntry timeTrackingEntry)
        {
            if (id != timeTrackingEntry.Id)
            {
                return BadRequest();
            }

            // إعادة حساب المدة إذا تم تحديث الأوقات
            if (timeTrackingEntry.EndTime.HasValue)
            {
                timeTrackingEntry.Duration = (int?)(timeTrackingEntry.EndTime.Value - timeTrackingEntry.StartTime);
            }

            _context.Entry(timeTrackingEntry).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!TimeTrackingEntryExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// حذف سجل تتبع وقت
        /// </summary>
        /// <param name="id">معرف سجل تتبع الوقت</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف سجل تتبع الوقت بنجاح</response>
        /// <response code="404">سجل تتبع الوقت غير موجود</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteTimeTrackingEntry(int id)
        {
            var timeTrackingEntry = await _context.TimeTrackingEntries.FindAsync(id);
            if (timeTrackingEntry == null)
            {
                return NotFound();
            }

            _context.TimeTrackingEntries.Remove(timeTrackingEntry);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool TimeTrackingEntryExists(int id)
        {
            return _context.TimeTrackingEntries.Any(e => e.Id == id);
        }
    }
}
