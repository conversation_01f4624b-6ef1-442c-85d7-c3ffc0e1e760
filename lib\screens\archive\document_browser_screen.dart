import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_application_2/controllers/archive_controller.dart';

import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/utils/responsive_helper.dart';
import 'package:flutter_application_2/widgets/custom_app_bar.dart';
import 'package:flutter_application_2/widgets/loading_indicator.dart';

import 'document_detail_screen.dart';
import 'document_upload_screen.dart';
import 'widgets/document_list_item.dart';
import 'widgets/document_grid_item.dart';
import 'widgets/document_filter_sidebar.dart';
import 'widgets/document_search_bar.dart';

/// شاشة تصفح وثائق الأرشيف
class DocumentBrowserScreen extends StatefulWidget {
  /// معرف الوثيقة المراد عرضها مباشرة (اختياري)
  final String? initialDocumentId;

  const DocumentBrowserScreen({
    super.key,
    this.initialDocumentId,
  });

  @override
  State<DocumentBrowserScreen> createState() => _DocumentBrowserScreenState();
}

class _DocumentBrowserScreenState extends State<DocumentBrowserScreen> {
  final ArchiveController _controller = Get.find<ArchiveController>();

  // حالة العرض
  bool _isGridView = true;
  bool _showSidebar = true;

  @override
  void initState() {
    super.initState();

    // تحميل البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _controller.loadDocuments();
      _controller.loadCategories();
      _controller.loadTags();

      // إذا تم تحديد وثيقة، قم بتحميلها وعرضها
      if (widget.initialDocumentId != null) {
        _controller.loadDocument(widget.initialDocumentId!).then((_) {
          if (_controller.currentDocument.value != null) {
            Get.to(() => DocumentDetailScreen(
              document: _controller.currentDocument.value!,
            ));
          }
        });
      }

      // تعيين حالة العرض بناءً على حجم الشاشة - نقلناها إلى داخل الـ postFrameCallback
      // لتجنب الوصول إلى MediaQuery قبل اكتمال initState
      setState(() {
        _showSidebar = !ResponsiveHelper.isSmallScreen(context);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'تصفح الوثائق',
        actions: [
          // زر تبديل طريقة العرض
          IconButton(
            icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
            tooltip: _isGridView ? 'عرض قائمة' : 'عرض شبكة',
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          // زر إظهار/إخفاء الشريط الجانبي (فقط للشاشات الكبيرة)
          if (!isSmallScreen)
            IconButton(
              icon: Icon(_showSidebar ? Icons.filter_list_off : Icons.filter_list),
              tooltip: _showSidebar ? 'إخفاء عوامل التصفية' : 'إظهار عوامل التصفية',
              onPressed: () {
                setState(() {
                  _showSidebar = !_showSidebar;
                });
              },
            ),
          // زر إعادة تعيين عوامل التصفية
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'إعادة تعيين عوامل التصفية',
            onPressed: () {
              _controller.resetFilters();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: DocumentSearchBar(
              onSearch: (query) {
                _controller.setSearchQuery(query);
              },
              onFilterTap: isSmallScreen ? () {
                _showFilterBottomSheet(context);
              } : null,
            ),
          ),

          // المحتوى الرئيسي
          Expanded(
            child: Row(
              children: [
                // شريط التصفية الجانبي (للشاشات الكبيرة فقط)
                if (!isSmallScreen && _showSidebar)
                  SizedBox(
                    width: 280,
                    child: DocumentFilterSidebar(
                      controller: _controller,
                    ),
                  ),

                // قائمة الوثائق
                Expanded(
                  child: GetBuilder<ArchiveController>(
                    builder: (controller) {
                      if (controller.isLoading.value) {
                        return const Center(child: LoadingIndicator());
                      }

                      if (controller.documents.isEmpty) {
                        return _buildEmptyState();
                      }

                      return _isGridView
                          ? _buildDocumentsGrid()
                          : _buildDocumentsList();
                    },
                  ),
                ),
              ],
            ),
          ),

          // شريط التنقل بين الصفحات
          GetBuilder<ArchiveController>(
            builder: (controller) => _buildPagination(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Get.to(() => const DocumentUploadScreen());
        },
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildDocumentsGrid() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: ResponsiveHelper.isLargeScreen(context) ? 4 :
                         ResponsiveHelper.isMediumScreen(context) ? 3 : 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 0.8,
        ),
        itemCount: _controller.documents.length,
        itemBuilder: (context, index) {
          final document = _controller.documents[index];
          return DocumentGridItem(
            document: document,
            onTap: () {
              _controller.currentDocument.value = document;
              Get.to(() => DocumentDetailScreen(document: document));
            },
          );
        },
      ),
    );
  }

  Widget _buildDocumentsList() {
    return ListView.separated(
      padding: const EdgeInsets.all(16.0),
      itemCount: _controller.documents.length,
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        final document = _controller.documents[index];
        return DocumentListItem(
          document: document,
          onTap: () {
            _controller.currentDocument.value = document;
            Get.to(() => DocumentDetailScreen(document: document));
          },
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد وثائق مطابقة',
            style: AppStyles.headline6.copyWith(color: Colors.grey),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير عوامل التصفية أو البحث عن شيء آخر',
            style: AppStyles.body2.copyWith(color: Colors.grey),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              _controller.resetFilters();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة تعيين عوامل التصفية'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPagination() {
    final totalPages = (_controller.totalItems.value / _controller.itemsPerPage.value).ceil();

    if (totalPages <= 1) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      color: Colors.grey.shade100,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconButton(
            icon: const Icon(Icons.chevron_left),
            onPressed: _controller.currentPage.value > 0
                ? () => _controller.goToPage(_controller.currentPage.value - 1)
                : null,
          ),
          Text(
            'الصفحة ${_controller.currentPage.value + 1} من $totalPages',
            style: AppStyles.body2,
          ),
          IconButton(
            icon: const Icon(Icons.chevron_right),
            onPressed: _controller.currentPage.value < totalPages - 1
                ? () => _controller.goToPage(_controller.currentPage.value + 1)
                : null,
          ),
        ],
      ),
    );
  }

  void _showFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.7,
          minChildSize: 0.5,
          maxChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return SingleChildScrollView(
              controller: scrollController,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'عوامل التصفية',
                          style: AppStyles.headline6,
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () {
                            Navigator.pop(context);
                          },
                        ),
                      ],
                    ),
                    const Divider(),
                    DocumentFilterSidebar(
                      controller: _controller,
                      isBottomSheet: true,
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('تطبيق'),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
