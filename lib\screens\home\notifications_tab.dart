import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/notification_controller.dart';
import '../../models/notification_model.dart' as models;
import '../../utils/date_formatter.dart';

class NotificationsTab extends StatefulWidget {
  const NotificationsTab({super.key});

  @override
  State<NotificationsTab> createState() => _NotificationsTabState();
}

class _NotificationsTabState extends State<NotificationsTab> {
  @override
  void initState() {
    super.initState();
    // Load notifications when tab is created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadNotifications();
    });
  }

  /// Load notifications for the current user
  Future<void> _loadNotifications() async {
    final authController = Get.find<AuthController>();
    final notificationController = Get.find<NotificationController>();

    if (authController.currentUser.value != null) {
      await notificationController.loadNotifications(authController.currentUser.value!.id);
    }
  }

  /// Mark all notifications as read for the current user
  Future<void> _markAllAsRead() async {
    final authController = Get.find<AuthController>();
    final notificationController = Get.find<NotificationController>();

    if (authController.currentUser.value != null) {
      await notificationController.markAllAsRead(authController.currentUser.value!.id);
    }
  }

  /// Delete all notifications for the current user
  Future<void> _deleteAllNotifications() async {
    final authController = Get.find<AuthController>();
    final notificationController = Get.find<NotificationController>();

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete All Notifications'),
        content: const Text('Are you sure you want to delete all notifications?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && authController.currentUser.value != null) {
      await notificationController.deleteAllNotifications(authController.currentUser.value!.id);
    }
  }

  /// Builds the notifications tab UI
  @override
  Widget build(BuildContext context) {
    // Get controllers
    final notificationController = Get.find<NotificationController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('الاشعارات'),
      ),
      body: Column(
        children: [
          // Actions bar
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    'Notifications',
                    style: AppStyles.titleLarge,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                LayoutBuilder(
                  builder: (context, constraints) {
                    // تكيف مع الشاشات الصغيرة
                    // Adapt to small screens
                    return Obx(() {
                      if (constraints.maxWidth < 300) {
                        return Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              onPressed: notificationController.notifications.isEmpty
                                  ? null
                                  : _markAllAsRead,
                              icon: const Icon(Icons.check_circle_outline),
                              tooltip: 'Mark All Read',
                            ),
                            IconButton(
                              onPressed: notificationController.notifications.isEmpty
                                  ? null
                                  : _deleteAllNotifications,
                              icon: const Icon(Icons.delete_outline),
                              tooltip: 'Delete All',
                            ),
                          ],
                        );
                      }

                      // للشاشات العادية والكبيرة
                      // For normal and large screens
                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          TextButton.icon(
                            onPressed: notificationController.notifications.isEmpty
                                ? null
                                : _markAllAsRead,
                            icon: const Icon(Icons.check_circle_outline),
                            label: const Text('Mark All Read'),
                          ),
                          TextButton.icon(
                            onPressed: notificationController.notifications.isEmpty
                                ? null
                                : _deleteAllNotifications,
                            icon: const Icon(Icons.delete_outline),
                            label: const Text('Delete All'),
                          ),
                        ],
                      );
                    });
                  },
                ),
              ],
            ),
          ),

          // Notifications list
          Expanded(
            child: Obx(() => notificationController.isLoading.value
                ? const Center(child: CircularProgressIndicator())
                : _buildNotificationsList(notificationController)),
          ),
        ],
      ),
    );
  }

  /// Builds the list of notifications
  Widget _buildNotificationsList(NotificationController notificationController) {
    if (notificationController.notifications.isEmpty) {
      return const Center(
        child: Text('No notifications'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12), // تقليل الهوامش
      itemCount: notificationController.notifications.length,
      physics: const ClampingScrollPhysics(), // تعطيل ميزة السحب للأسفل
      itemBuilder: (context, index) {
        final notification = notificationController.notifications[index];
        return _buildNotificationItem(context, notification);
      },
    );
  }

  /// Builds a notification item
  Widget _buildNotificationItem(BuildContext context, models.Notification notification) {
    // Get controller
    final notificationController = Get.find<NotificationController>();

    return Dismissible(
      key: Key(notification.id),
      background: Container(
        color: Colors.red,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 16),
        child: const Icon(
          Icons.delete,
          color: Colors.white,
        ),
      ),
      direction: DismissDirection.endToStart,
      onDismissed: (direction) {
        notificationController.deleteNotification(notification.id);
      },
      child: Card(
        margin: const EdgeInsets.only(bottom: 12),
        color: notification.isRead ? Colors.white : Colors.blue.shade50,
        child: ListTile(
          title: Text(
            notification.title,
            style: AppStyles.titleMedium.copyWith(
              fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
            ),
            overflow: TextOverflow.ellipsis, // منع تجاوز النص للحدود
            maxLines: 1, // تحديد عدد الأسطر
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // استخدام الحد الأدنى من المساحة المطلوبة
            children: [
              const SizedBox(height: 4),
              Text(
                notification.message,
                style: AppStyles.bodySmall,
                overflow: TextOverflow.ellipsis, // منع تجاوز النص للحدود
                maxLines: 2, // تحديد عدد الأسطر
              ),
              const SizedBox(height: 4),
              Text(
                DateFormatter.getRelativeTime(notification.createdAt),
                style: AppStyles.labelSmall,
              ),
            ],
          ),
          leading: CircleAvatar(
            backgroundColor: _getNotificationColor(notification.type),
            child: Icon(
              _getNotificationIcon(notification.type),
              color: Colors.white,
              size: 20,
            ),
          ),
          trailing: notification.isRead
              ? null
              : IconButton(
                  icon: const Icon(Icons.check_circle_outline),
                  onPressed: () {
                    notificationController.markAsRead(notification.id);
                  },
                  padding: EdgeInsets.zero, // تقليل المساحة المستخدمة
                  constraints: const BoxConstraints(), // تقليل القيود
                ),
          onTap: () {
            // Mark as read when tapped
            if (!notification.isRead) {
              notificationController.markAsRead(notification.id);
            }

            // Navigate to related content if applicable
            if (notification.taskId != null) {
              // Navigate to task details using GetX
              // Get.to(() => TaskDetailScreen(taskId: notification.taskId!));
            }
          },
          dense: true, // جعل ListTile أكثر كثافة لتوفير المساحة
          visualDensity: VisualDensity.compact, // تقليل المساحة بين العناصر
        ),
      ),
    );
  }

  Color _getNotificationColor(models.NotificationType type) {
    switch (type) {
      // إشعارات المهام
      case models.NotificationType.taskAssigned:
      case models.NotificationType.taskTransferred:
        return AppColors.primary;
      case models.NotificationType.taskCompleted:
        return AppColors.success;
      case models.NotificationType.taskUpdated:
        return AppColors.info;
      case models.NotificationType.commentAdded:
        return AppColors.accent;
      case models.NotificationType.fileAttached:
        return Colors.purple;
      case models.NotificationType.infoRequested:
      case models.NotificationType.infoProvided:
        return AppColors.warning;
      case models.NotificationType.reminderDue:
        return AppColors.error;

      // إشعارات النظام
      case models.NotificationType.systemNotification:
        return Colors.grey;

      // إشعارات المحادثات
      case models.NotificationType.mentionedInTaskMessage:
        return Colors.teal;
      case models.NotificationType.newTaskMessage:
        return Colors.indigo;
      case models.NotificationType.newDirectMessage:
        return Colors.blue;
      case models.NotificationType.mentionedInMessage:
        return Colors.deepPurple;
      case models.NotificationType.newGroupMessage:
        return Colors.amber.shade800;
      case models.NotificationType.addedToGroup:
        return Colors.green;
      default:
        // قيمة افتراضية
        return Colors.grey;
    }
  }

  IconData _getNotificationIcon(models.NotificationType type) {
    switch (type) {
      // إشعارات المهام
      case models.NotificationType.taskAssigned:
        return Icons.assignment_ind;
      case models.NotificationType.taskTransferred:
        return Icons.swap_horiz;
      case models.NotificationType.taskCompleted:
        return Icons.task_alt;
      case models.NotificationType.taskUpdated:
        return Icons.update;
      case models.NotificationType.commentAdded:
        return Icons.comment;
      case models.NotificationType.fileAttached:
        return Icons.attach_file;
      case models.NotificationType.infoRequested:
        return Icons.help_outline;
      case models.NotificationType.infoProvided:
        return Icons.info_outline;
      case models.NotificationType.reminderDue:
        return Icons.alarm;

      // إشعارات النظام
      case models.NotificationType.systemNotification:
        return Icons.notifications;

      // إشعارات المحادثات
      case models.NotificationType.mentionedInTaskMessage:
        return Icons.alternate_email;
      case models.NotificationType.newTaskMessage:
        return Icons.message;
      case models.NotificationType.newDirectMessage:
        return Icons.chat;
      case models.NotificationType.mentionedInMessage:
        return Icons.alternate_email;
      case models.NotificationType.newGroupMessage:
        return Icons.group;
      case models.NotificationType.addedToGroup:
        return Icons.group_add;
      default:
        // قيمة افتراضية
        return Icons.notifications;
    }
  }
}
