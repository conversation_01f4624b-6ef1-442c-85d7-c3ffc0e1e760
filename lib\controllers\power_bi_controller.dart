import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';

import '../models/power_bi_report_model.dart';
import '../models/power_bi/aggregate_function_model.dart';
import '../services/power_bi_analytics_service.dart';

import '../database/database_repair.dart';
import 'auth_controller.dart';

/// وحدة تحكم باور بي آي
///
/// تدير حالة صفحات باور بي آي وتوفر وظائف للتفاعل مع خدمة باور بي آي
class PowerBIController extends GetxController {
  final PowerBIAnalyticsService _powerBIService =
      Get.find<PowerBIAnalyticsService>();
  final DatabaseHelper _databaseHelper = Get.find<DatabaseHelper>();
  final AuthController _authController = Get.find<AuthController>();

  // حالة التحميل
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  // قائمة التقارير
  final RxList<PowerBIReport> myReports = <PowerBIReport>[].obs;
  final RxList<PowerBIReport> sharedReports = <PowerBIReport>[].obs;

  // التقرير الحالي
  final Rx<PowerBIReport?> currentReport = Rx<PowerBIReport?>(null);

  // بيانات الجداول والأعمدة
  final RxList<String> availableTables = <String>[].obs;
  final RxMap<String, List<Map<String, dynamic>>> tableColumns =
      <String, List<Map<String, dynamic>>>{}.obs;
  final RxMap<String, List<Map<String, dynamic>>> sampleData =
      <String, List<Map<String, dynamic>>>{}.obs;

  // بيانات الرسم البياني
  final Rx<Map<String, dynamic>> chartData = Rx<Map<String, dynamic>>({});

  // الجدول المحدد
  final RxString selectedTable = ''.obs;
  final RxList<String> selectedColumns = <String>[].obs;
  final RxString xAxisColumn = ''.obs;
  final RxString yAxisColumn = ''.obs;
  final RxString sizeColumn = ''.obs;
  final RxString colorColumn = ''.obs;
  final RxString filterCriteria = ''.obs;

  // نوع الرسم البياني المحدد
  final Rx<PowerBIChartType> selectedChartType = PowerBIChartType.bar.obs;

  @override
  void onInit() {
    super.onInit();
    // إصلاح قاعدة البيانات قبل تحميل البيانات
    repairDatabase().then((_) {
      loadAvailableTables();
      loadMyReports();
      loadSharedReports();
    });
  }

  /// إصلاح قاعدة البيانات
  Future<void> repairDatabase() async {
    try {
      // إصلاح جدول power_bi_reports
      await _databaseHelper.repairPowerBIReportsTable();

      // استخدام أداة إصلاح قاعدة البيانات
      final databaseRepair = DatabaseRepair();
      await databaseRepair.repairPowerBIReportsTable();

      debugPrint('تم إصلاح جدول power_bi_reports بنجاح');
    } catch (e) {
      debugPrint('خطأ في إصلاح قاعدة البيانات: $e');
    }
  }

  /// تحميل قائمة الجداول المتاحة
  Future<void> loadAvailableTables() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final tables = await _powerBIService.getAvailableTables();
      availableTables.value = tables;
    } catch (e) {
      errorMessage.value = 'خطأ في تحميل قائمة الجداول: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل معلومات الأعمدة لجدول معين
  Future<void> loadTableColumns(String tableName) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final columns = await _powerBIService.getTableColumns(tableName);
      tableColumns[tableName] = columns;

      // تحميل عينة من البيانات
      final sample = await _powerBIService.getSampleData(tableName);
      sampleData[tableName] = sample;

      // تحديث الجدول المحدد
      selectedTable.value = tableName;

      // إعادة تعيين الأعمدة المحددة
      selectedColumns.clear();
      xAxisColumn.value = '';
      yAxisColumn.value = '';
      sizeColumn.value = '';
      colorColumn.value = '';
    } catch (e) {
      errorMessage.value = 'خطأ في تحميل معلومات الأعمدة: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل تقارير المستخدم
  Future<void> loadMyReports() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final reports = await _powerBIService.getMyReports();
      myReports.value = reports;
    } catch (e) {
      errorMessage.value = 'خطأ في تحميل التقارير: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل التقارير المشتركة
  Future<void> loadSharedReports() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final reports = await _powerBIService.getSharedReports();
      sharedReports.value = reports;
    } catch (e) {
      errorMessage.value = 'خطأ في تحميل التقارير المشتركة: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل تقرير بواسطة المعرف
  Future<void> loadReport(String reportId) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final report = await _powerBIService.getReportById(reportId);
      if (report != null) {
        currentReport.value = report;

        // تحديث الحالة بناءً على التقرير
        selectedTable.value = report.tableName;
        selectedColumns.value = report.columnNames;
        xAxisColumn.value = report.xAxisColumn;
        yAxisColumn.value = report.yAxisColumn;
        sizeColumn.value = report.sizeColumn ?? '';
        colorColumn.value = report.colorColumn ?? '';
        filterCriteria.value = report.filterCriteria ?? '';
        selectedChartType.value = report.chartType;

        // طباعة معلومات الدالة التجميعية
        debugPrint(
            'الدالة التجميعية: ${report.yAxisAggregateFunction != null ? getAggregateFunctionName(report.yAxisAggregateFunction!) : "غير محددة"}');

        // تحميل بيانات الرسم البياني
        await loadChartData(report);
      } else {
        errorMessage.value = 'التقرير غير موجود';
      }
    } catch (e) {
      errorMessage.value = 'خطأ في تحميل التقرير: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل بيانات الرسم البياني
  Future<void> loadChartData(PowerBIReport report) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      debugPrint('بدء تحميل بيانات الرسم البياني للتقرير: ${report.title}');
      debugPrint('نوع الرسم البياني: ${report.chartType}');
      debugPrint('الجدول: ${report.tableName}');
      debugPrint('الأعمدة: ${report.columnNames.join(', ')}');
      debugPrint('عمود المحور الأفقي: ${report.xAxisColumn}');
      debugPrint('عمود المحور الرأسي: ${report.yAxisColumn}');

      // التحقق من صحة بيانات التقرير
      if (report.tableName.isEmpty) {
        throw Exception('اسم الجدول غير محدد');
      }

      if (report.xAxisColumn.isEmpty || report.yAxisColumn.isEmpty) {
        throw Exception('أعمدة المحاور غير محددة بشكل صحيح');
      }

      final data = await _powerBIService.getChartData(report);

      // التحقق من وجود خطأ في البيانات
      if (data.containsKey('error')) {
        errorMessage.value = 'خطأ في البيانات: ${data['error']}';
        debugPrint('خطأ في البيانات: ${data['error']}');

        // إضافة بيانات افتراضية للعرض
        // if (!data.containsKey('chartData')) {
        //   data['chartData'] = _getDefaultChartData(report.chartType);
        // }
      }

      chartData.value = data;
      debugPrint('تم تحميل بيانات الرسم البياني بنجاح');
    } catch (e) {
      errorMessage.value = 'خطأ في تحميل بيانات الرسم البياني: $e';
      debugPrint('خطأ في تحميل بيانات الرسم البياني: $e');

      // إنشاء بيانات افتراضية في حالة الخطأ
      // chartData.value = {
      //   'error': e.toString(),
      //   'chartData': _getDefaultChartData(report.chartType)
      // };
    } finally {
      isLoading.value = false;
    }
  }

  /// إنشاء تقرير جديد
  Future<PowerBIReport?> createReport({
    required String title,
    String? description,
    required PowerBIChartType chartType,
    required String tableName,
    required List<String> columnNames,
    required String xAxisColumn,
    required String yAxisColumn,
    AggregateFunction? yAxisAggregateFunction,
    String? sizeColumn,
    String? colorColumn,
    String? filterCriteria,
    String? chartSettings,
    List<String>? relatedTables,
    List<String>? joinConditions,
    List<String>? joinTypes,
  }) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final report = PowerBIReport(
        id: '',
        title: title,
        description: description,
        chartType: chartType,
        createdById: _authController.currentUser.value?.id ?? '',
        createdAt: DateTime.now(),
        tableName: tableName,
        columnNames: columnNames,
        xAxisColumn: xAxisColumn,
        yAxisColumn: yAxisColumn,
        yAxisAggregateFunction:
            yAxisAggregateFunction ?? AggregateFunction.none,
        sizeColumn: sizeColumn,
        colorColumn: colorColumn,
        filterCriteria: filterCriteria,
        chartSettings: chartSettings,
        relatedTables: relatedTables,
        joinConditions: joinConditions,
        joinTypes: joinTypes,
      );

      final createdReport = await _powerBIService.createReport(report);

      // تحديث قائمة التقارير
      await loadMyReports();

      return createdReport;
    } catch (e) {
      errorMessage.value = 'خطأ في إنشاء التقرير: $e';
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديث تقرير موجود
  Future<bool> updateReport(PowerBIReport report) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final success = await _powerBIService.updateReport(report);

      // تحديث قائمة التقارير
      await loadMyReports();

      return success;
    } catch (e) {
      errorMessage.value = 'خطأ في تحديث التقرير: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// حذف تقرير
  Future<bool> deleteReport(String reportId) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final success = await _powerBIService.deleteReport(reportId);

      // تحديث قائمة التقارير
      await loadMyReports();

      return success;
    } catch (e) {
      errorMessage.value = 'خطأ في حذف التقرير: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// مشاركة تقرير مع مستخدمين
  Future<bool> shareReport(String reportId, List<String> userIds) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final success = await _powerBIService.shareReport(reportId, userIds);

      // تحديث قائمة التقارير
      await loadMyReports();

      return success;
    } catch (e) {
      errorMessage.value = 'خطأ في مشاركة التقرير: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// تنفيذ استعلام مخصص
  Future<List<Map<String, dynamic>>> executeQuery(
    String tableName,
    List<String> columns,
    String? filterCriteria,
  ) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final result = await _powerBIService.executeQuery(
        tableName,
        columns,
        filterCriteria,
      );

      return result;
    } catch (e) {
      errorMessage.value = 'خطأ في تنفيذ الاستعلام: $e';
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديث نوع الرسم البياني وإعادة تحميل البيانات
  Future<void> updateChartType(PowerBIChartType newChartType) async {
    if (currentReport.value == null) {
      errorMessage.value = 'لا يوجد تقرير محدد';
      return;
    }

    isLoading.value = true;
    errorMessage.value = '';

    try {
      // تحديث التقرير بنوع الرسم البياني الجديد
      final updatedReport = currentReport.value!.copyWith(
        chartType: newChartType,
        updatedAt: DateTime.now(),
      );

      // تحديث التقرير في الذاكرة
      currentReport.value = updatedReport;

      // تحميل بيانات الرسم البياني الجديد
      await loadChartData(updatedReport);

      debugPrint(
          'تم تحديث نوع الرسم البياني إلى: ${_powerBIChartTypeToString(newChartType)}');
    } catch (e) {
      errorMessage.value = 'خطأ في تحديث نوع الرسم البياني: $e';
      debugPrint('خطأ في تحديث نوع الرسم البياني: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديث أعمدة البيانات وإعادة تحميل البيانات
  Future<void> updateChartColumns({
    String? newXAxisColumn,
    String? newYAxisColumn,
    AggregateFunction? newYAxisAggregateFunction,
    String? newSizeColumn,
    String? newColorColumn,
  }) async {
    if (currentReport.value == null) {
      errorMessage.value = 'لا يوجد تقرير محدد';
      return;
    }

    isLoading.value = true;
    errorMessage.value = '';

    try {
      // تحديث التقرير بالأعمدة الجديدة
      final updatedReport = currentReport.value!.copyWith(
        xAxisColumn: newXAxisColumn ?? currentReport.value!.xAxisColumn,
        yAxisColumn: newYAxisColumn ?? currentReport.value!.yAxisColumn,
        yAxisAggregateFunction: newYAxisAggregateFunction ??
            currentReport.value!.yAxisAggregateFunction,
        sizeColumn: newSizeColumn,
        colorColumn: newColorColumn,
        updatedAt: DateTime.now(),
      );

      // تحديث التقرير في الذاكرة
      currentReport.value = updatedReport;

      // تحميل بيانات الرسم البياني الجديد
      await loadChartData(updatedReport);

      debugPrint('تم تحديث أعمدة الرسم البياني:');
      debugPrint('المحور الأفقي: ${updatedReport.xAxisColumn}');
      debugPrint('المحور الرأسي: ${updatedReport.yAxisColumn}');
      if (newSizeColumn != null) debugPrint('عمود الحجم: $newSizeColumn');
      if (newColorColumn != null) debugPrint('عمود اللون: $newColorColumn');
    } catch (e) {
      errorMessage.value = 'خطأ في تحديث أعمدة الرسم البياني: $e';
      debugPrint('خطأ في تحديث أعمدة الرسم البياني: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// حفظ التغييرات على التقرير
  Future<bool> saveReportChanges() async {
    if (currentReport.value == null) {
      errorMessage.value = 'لا يوجد تقرير محدد';
      return false;
    }

    isLoading.value = true;
    errorMessage.value = '';

    try {
      // تحديث التقرير في قاعدة البيانات
      final success = await _powerBIService.updateReport(currentReport.value!);

      if (success) {
        // تحديث قائمة التقارير
        await loadMyReports();
        debugPrint('تم حفظ التغييرات على التقرير بنجاح');
      } else {
        debugPrint('فشل في حفظ التغييرات على التقرير');
      }

      return success;
    } catch (e) {
      errorMessage.value = 'خطأ في حفظ التغييرات على التقرير: $e';
      debugPrint('خطأ في حفظ التغييرات على التقرير: $e');

      // إذا كان الخطأ متعلقًا بعدم وجود أعمدة في الجدول، نحاول إصلاح قاعدة البيانات
      if (e.toString().contains('no such column')) {
        debugPrint('محاولة إصلاح قاعدة البيانات...');
        await repairDatabase();

        // محاولة حفظ التقرير مرة أخرى بعد الإصلاح
        try {
          final success =
              await _powerBIService.updateReport(currentReport.value!);
          if (success) {
            await loadMyReports();
            debugPrint(
                'تم حفظ التغييرات على التقرير بنجاح بعد إصلاح قاعدة البيانات');
            return true;
          }
        } catch (retryError) {
          debugPrint(
              'فشل في حفظ التقرير حتى بعد إصلاح قاعدة البيانات: $retryError');
        }
      }

      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// اقتراح العلاقات المحتملة بين جدولين
  Future<List<Map<String, String>>> suggestTableRelations(
      String table1, String table2) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final suggestions =
          await _powerBIService.suggestTableRelations(table1, table2);
      return suggestions;
    } catch (e) {
      errorMessage.value = 'خطأ في اقتراح العلاقات بين الجداول: $e';
      debugPrint('خطأ في اقتراح العلاقات بين الجداول: $e');
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  /// تنفيذ استعلام متعدد الجداول
  Future<List<Map<String, dynamic>>> executeMultiTableQuery({
    required String mainTable,
    required List<String> relatedTables,
    required List<String> joinConditions,
    List<String>? joinTypes,
    List<String>? columns,
    String? filterCriteria,
  }) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final result = await _powerBIService.getMultiTableSampleData(
        mainTable: mainTable,
        relatedTables: relatedTables,
        joinConditions: joinConditions,
        joinTypes: joinTypes,
        columns: columns,
        filterCriteria: filterCriteria,
      );

      return result;
    } catch (e) {
      errorMessage.value = 'خطأ في تنفيذ الاستعلام متعدد الجداول: $e';
      debugPrint('خطأ في تنفيذ الاستعلام متعدد الجداول: $e');
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  /// الحصول على بيانات الرسم البياني لتقرير معين (بدون حفظ)
  Future<Map<String, dynamic>> getChartData(PowerBIReport report) async {
    try {
      // تحضير النتيجة
      Map<String, dynamic> result = {};
      List<Map<String, dynamic>> data = [];

      // التحقق من وجود جداول مرتبطة
      if (report.relatedTables != null &&
          report.relatedTables!.isNotEmpty &&
          report.joinConditions != null &&
          report.joinConditions!.isNotEmpty) {
        // استعلام متعدد الجداول
        data = await executeMultiTableQuery(
          mainTable: report.tableName,
          relatedTables: report.relatedTables!,
          joinConditions: report.joinConditions!,
          joinTypes: report.joinTypes,
          columns: report.columnNames,
          filterCriteria: report.filterCriteria,
        );
      } else {
        // استعلام بسيط على جدول واحد
        data = await executeQuery(
          report.tableName,
          report.columnNames,
          report.filterCriteria,
        );
      }

      // إضافة البيانات الخام إلى النتيجة
      result['data'] = data;

      // طباعة معلومات تصحيحية
      debugPrint('تم استلام ${data.length} صفوف من البيانات');
      if (data.isNotEmpty) {
        debugPrint('نموذج البيانات: ${data.first}');

        // التحقق من وجود الأعمدة المطلوبة
        final xAxisExists = data.first.containsKey(report.xAxisColumn);
        final yAxisExists = data.first.containsKey(report.yAxisColumn);

        if (!xAxisExists || !yAxisExists) {
          debugPrint('تحذير: الأعمدة المطلوبة غير موجودة في البيانات');
          debugPrint(
              'عمود المحور الأفقي (${report.xAxisColumn}): ${xAxisExists ? "موجود" : "غير موجود"}');
          debugPrint(
              'عمود المحور الرأسي (${report.yAxisColumn}): ${yAxisExists ? "موجود" : "غير موجود"}');

          // إضافة رسالة خطأ إلى النتيجة
          if (!xAxisExists || !yAxisExists) {
            result['error'] = 'الأعمدة المطلوبة غير موجودة في البيانات';
            // إضافة بيانات افتراضية للعرض
            // result['chartData'] = _getDefaultChartData(report.chartType);
            return result;
          }
        }
      }
      // else {
      //   debugPrint('لا توجد بيانات للرسم البياني');
      //   // إضافة بيانات افتراضية للعرض
      //   result['chartData'] = _getDefaultChartData(report.chartType);
      //   return result;
      // }

      // معالجة البيانات حسب نوع الرسم البياني
      switch (report.chartType) {
        case PowerBIChartType.bar:
          // تحويل البيانات إلى تنسيق الرسم البياني الشريطي
          final chartData = _processBarChartData(
              data, report.xAxisColumn, report.yAxisColumn);
          result['chartData'] = chartData;
          break;

        case PowerBIChartType.line:
          // تحويل البيانات إلى تنسيق الرسم البياني الخطي
          final chartData = _processLineChartData(
              data, report.xAxisColumn, report.yAxisColumn);
          result['chartData'] = chartData;
          break;

        case PowerBIChartType.pie:
          // تحويل البيانات إلى تنسيق الرسم البياني الدائري
          final chartData = _processPieChartData(
              data, report.xAxisColumn, report.yAxisColumn);
          result['chartData'] = chartData;
          break;

        case PowerBIChartType.scatter:
        case PowerBIChartType.bubble:
          // تحويل البيانات إلى تنسيق الرسم البياني النقطي أو الفقاعي
          final spots = _processScatterChartData(data, report.xAxisColumn,
              report.yAxisColumn, report.sizeColumn, report.colorColumn);
          result['spots'] = spots;
          break;

        case PowerBIChartType.table:
          // لا حاجة لمعالجة إضافية للجدول
          break;

        case PowerBIChartType.radar:
        case PowerBIChartType.heatmap:
        case PowerBIChartType.treemap:
        case PowerBIChartType.gauge:
          // أنواع رسوم بيانية إضافية - سيتم تنفيذها لاحقًا
          // result['chartData'] = _getDefaultChartData(report.chartType);
          break;
      }

      return result;
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات الرسم البياني: $e');
      return {
        'error': e.toString(),
        // 'chartData': _getDefaultChartData(report.chartType)
      };
    }
  }

  /// الحصول على بيانات افتراضية للرسم البياني
  // dynamic _getDefaultChartData(PowerBIChartType chartType) {
  //   switch (chartType) {
  //     case PowerBIChartType.bar:
  //       return {
  //         'الفئة 1': 10.0,
  //         'الفئة 2': 20.0,
  //         'الفئة 3': 15.0,
  //         'الفئة 4': 25.0,
  //         'الفئة 5': 18.0,
  //       };

  //     case PowerBIChartType.pie:
  //       return {
  //         'الفئة 1': 35.0,
  //         'الفئة 2': 25.0,
  //         'الفئة 3': 40.0,
  //       };

  //     case PowerBIChartType.line:
  //       return {
  //         'سلسلة 1': [
  //           FlSpot(0, 10),
  //           FlSpot(1, 15),
  //           FlSpot(2, 13),
  //           FlSpot(3, 17),
  //           FlSpot(4, 20),
  //         ],
  //         'سلسلة 2': [
  //           FlSpot(0, 5),
  //           FlSpot(1, 8),
  //           FlSpot(2, 12),
  //           FlSpot(3, 10),
  //           FlSpot(4, 13),
  //         ],
  //       };

  //     case PowerBIChartType.scatter:
  //     case PowerBIChartType.bubble:
  //       return [
  //         ScatterSpot(1, 5,
  //             dotPainter: FlDotCirclePainter(color: Colors.blue, radius: 8)),
  //         ScatterSpot(2, 8,
  //             dotPainter: FlDotCirclePainter(color: Colors.green, radius: 8)),
  //         ScatterSpot(3, 4,
  //             dotPainter: FlDotCirclePainter(color: Colors.red, radius: 8)),
  //         ScatterSpot(4, 6,
  //             dotPainter: FlDotCirclePainter(color: Colors.purple, radius: 8)),
  //         ScatterSpot(5, 9,
  //             dotPainter: FlDotCirclePainter(color: Colors.orange, radius: 8)),
  //       ];

  //     default:
  //       return {};
  //   }
  // }

  /// تحويل نوع الرسم البياني إلى نص
  String _powerBIChartTypeToString(PowerBIChartType chartType) {
    switch (chartType) {
      case PowerBIChartType.bar:
        return 'رسم بياني شريطي';
      case PowerBIChartType.line:
        return 'رسم بياني خطي';
      case PowerBIChartType.pie:
        return 'رسم بياني دائري';
      case PowerBIChartType.scatter:
        return 'رسم بياني نقطي';
      case PowerBIChartType.bubble:
        return 'رسم بياني فقاعي';
      case PowerBIChartType.radar:
        return 'رسم بياني راداري';
      case PowerBIChartType.table:
        return 'جدول بيانات';
      case PowerBIChartType.heatmap:
        return 'خريطة حرارية';
      case PowerBIChartType.treemap:
        return 'خريطة شجرية';
      case PowerBIChartType.gauge:
        return 'مقياس';
      default:
        return 'غير معروف';
    }
  }

  /// معالجة بيانات الرسم البياني الشريطي
  Map<String, double> _processBarChartData(
      List<Map<String, dynamic>> data, String xAxisColumn, String yAxisColumn) {
    final Map<String, double> result = {};
    final Set<String> processedKeys = {}; // مجموعة للتحقق من المفاتيح المعالجة
    final Map<String, String> numericToTextMapping =
        {}; // تخزين العلاقة بين الأرقام والنصوص

    // طباعة معلومات تصحيحية
    debugPrint('معالجة بيانات الرسم البياني الشريطي');
    debugPrint('عمود المحور الأفقي: $xAxisColumn');
    debugPrint('عمود المحور الرأسي: $yAxisColumn');

    if (data.isNotEmpty) {
      debugPrint('مفاتيح البيانات المتاحة: ${data.first.keys.toList()}');

      // البحث عن أي عمود يحتوي على أسماء الحالات
      final possibleStatusColumns = [
        'status',
        'status_name',
        'state',
        'state_name',
        'name',
        'label'
      ];
      String? statusColumn;

      for (final column in possibleStatusColumns) {
        if (data.first.containsKey(column)) {
          statusColumn = column;
          break;
        }
      }

      // إذا وجدنا عمود الحالة، نقوم بإنشاء تخطيط بين الأرقام والنصوص
      if (statusColumn != null) {
        for (final row in data) {
          final id = row['id']?.toString() ?? row['status_id']?.toString();
          final name = row[statusColumn]?.toString();

          if (id != null && name != null) {
            numericToTextMapping[id] = name;
          }
        }

        debugPrint(
            'تم العثور على تخطيط بين الأرقام والنصوص: $numericToTextMapping');
      }
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط العمودي
    for (final row in data) {
      // البحث عن القيم بطرق مختلفة
      dynamic xValue = _getColumnValue(row, xAxisColumn);
      dynamic yValue = _getColumnValue(row, yAxisColumn);

      // التحقق من وجود القيم
      if (xValue != null && yValue != null) {
        String xValueStr = xValue.toString();
        double numericValue = 0;

        // استخدام النص بدلاً من الرقم إذا كان متاحاً
        if (numericToTextMapping.containsKey(xValueStr)) {
          xValueStr = numericToTextMapping[xValueStr]!;
        }

        // تحويل القيمة إلى رقم عشري
        if (yValue is int) {
          numericValue = yValue.toDouble();
        } else if (yValue is double) {
          numericValue = yValue;
        } else if (yValue is String) {
          numericValue = double.tryParse(yValue) ?? 0;
        }

        // تجنب تكرار المفاتيح - استخدام المفتاح مرة واحدة فقط
        if (!processedKeys.contains(xValueStr)) {
          // تجاهل القيم الصفرية لتحسين المخطط
          if (numericValue > 0) {
            result[xValueStr] = numericValue;
            processedKeys.add(xValueStr);
          }
        } else {
          // إذا كان المفتاح موجوداً بالفعل، نجمع القيم معاً
          if (numericValue > 0) {
            result[xValueStr] = (result[xValueStr] ?? 0) + numericValue;
          }
        }
      }
    }

    // تنظيف البيانات - إزالة المفاتيح الرقمية إذا كان لدينا نفس المفاتيح كنصوص
    final keysToRemove = <String>[];
    for (final key in result.keys) {
      if (int.tryParse(key) != null) {
        // إذا كان المفتاح رقماً، نتحقق مما إذا كان لدينا نفس القيمة كنص
        for (final textKey in result.keys) {
          if (numericToTextMapping[key] == textKey) {
            keysToRemove.add(key);
            break;
          }
        }
      }
    }

    // إزالة المفاتيح الرقمية
    for (final key in keysToRemove) {
      result.remove(key);
    }

    // إذا كانت النتيجة فارغة، استخدم بيانات افتراضية
    if (result.isEmpty) {
      result['العنصر 1'] = 10.0;
      result['العنصر 2'] = 20.0;
      result['العنصر 3'] = 15.0;
    }

    // طباعة النتيجة النهائية للتصحيح
    debugPrint('النتيجة النهائية للمخطط الشريطي: $result');

    return result;
  }

  /// الحصول على قيمة عمود من صف بيانات
  dynamic _getColumnValue(Map<String, dynamic> row, String columnName) {
    // محاولة العثور على القيمة مباشرة
    if (row.containsKey(columnName)) {
      return row[columnName];
    }

    // محاولة البحث عن العمود بدون اسم الجدول
    final simpleName =
        columnName.contains('.') ? columnName.split('.').last : columnName;
    if (row.containsKey(simpleName)) {
      return row[simpleName];
    }

    return null;
  }

  /// معالجة بيانات الرسم البياني الخطي
  Map<String, List<FlSpot>> _processLineChartData(
      List<Map<String, dynamic>> data, String xAxisColumn, String yAxisColumn) {
    // طباعة معلومات تصحيحية
    debugPrint('معالجة بيانات الرسم البياني الخطي');
    debugPrint('عمود المحور الأفقي: $xAxisColumn');
    debugPrint('عمود المحور الرأسي: $yAxisColumn');

    if (data.isNotEmpty) {
      debugPrint('مفاتيح البيانات المتاحة: ${data.first.keys.toList()}');
    }

    // تصنيف البيانات حسب السلسلة (إذا كان هناك عمود تصنيف)
    final Map<String, List<Map<String, dynamic>>> groupedData = {'default': []};

    for (final row in data) {
      groupedData['default']!.add(row);
    }

    // تحويل البيانات المجمعة إلى نقاط على الرسم البياني
    final Map<String, List<FlSpot>> result = {};
    final Map<String, Set<double>> processedXValues =
        {}; // مجموعة للتحقق من القيم المعالجة

    groupedData.forEach((seriesName, seriesData) {
      final List<FlSpot> spots = [];
      processedXValues[seriesName] = {};

      // فرز البيانات حسب المحور الأفقي
      seriesData.sort((a, b) {
        final aValue = _getColumnValue(a, xAxisColumn);
        final bValue = _getColumnValue(b, xAxisColumn);

        if (aValue == null || bValue == null) return 0;

        if (aValue is DateTime && bValue is DateTime) {
          return aValue.compareTo(bValue);
        } else if (aValue is num && bValue is num) {
          return aValue.compareTo(bValue);
        } else {
          return aValue.toString().compareTo(bValue.toString());
        }
      });

      // تحويل البيانات إلى نقاط
      for (int i = 0; i < seriesData.length; i++) {
        final row = seriesData[i];

        // البحث عن القيم بطرق مختلفة
        dynamic xValue = _getColumnValue(row, xAxisColumn);
        dynamic yValue = _getColumnValue(row, yAxisColumn);

        if (xValue != null && yValue != null) {
          double x = i.toDouble(); // استخدام الفهرس كقيمة افتراضية
          double y = 0;

          // معالجة قيمة المحور الأفقي
          if (xValue is DateTime) {
            x = xValue.millisecondsSinceEpoch.toDouble();
          } else if (xValue is int) {
            x = xValue.toDouble();
          } else if (xValue is double) {
            x = xValue;
          } else if (xValue is String) {
            x = double.tryParse(xValue) ?? i.toDouble();
          }

          // معالجة قيمة المحور الرأسي
          if (yValue is int) {
            y = yValue.toDouble();
          } else if (yValue is double) {
            y = yValue;
          } else if (yValue is String) {
            y = double.tryParse(yValue) ?? 0;
          }

          // تجنب تكرار القيم على المحور الأفقي
          if (!processedXValues[seriesName]!.contains(x)) {
            spots.add(FlSpot(x, y));
            processedXValues[seriesName]!.add(x);
          }
        }
      }

      // ترتيب النقاط حسب المحور الأفقي
      spots.sort((a, b) => a.x.compareTo(b.x));

      result[seriesName] = spots;
    });

    // إذا كانت النتيجة فارغة، استخدم بيانات افتراضية
    // لانريد اي بيانات افتراضيه
    // if (result.isEmpty || result['default']!.isEmpty) {
    //   result['سلسلة 1'] = [
    //     FlSpot(0, 10),
    //     FlSpot(1, 15),
    //     FlSpot(2, 13),
    //     FlSpot(3, 17),
    //     FlSpot(4, 20),
    //   ];
    // }

    return result;
  }

  /// معالجة بيانات الرسم البياني الدائري
  Map<String, double> _processPieChartData(
      List<Map<String, dynamic>> data, String xAxisColumn, String yAxisColumn) {
    final Map<String, double> result = {};
    final Set<String> processedKeys = {}; // مجموعة للتحقق من المفاتيح المعالجة
    final Map<String, String> numericToTextMapping =
        {}; // تخزين العلاقة بين الأرقام والنصوص

    // طباعة معلومات تصحيحية
    debugPrint('معالجة بيانات الرسم البياني الدائري');
    debugPrint('عمود المحور الأفقي (التسميات): $xAxisColumn');
    debugPrint('عمود المحور الرأسي (القيم): $yAxisColumn');

    if (data.isNotEmpty) {
      debugPrint('مفاتيح البيانات المتاحة: ${data.first.keys.toList()}');

      // البحث عن أي عمود يحتوي على أسماء الحالات
      final possibleStatusColumns = [
        'status',
        'status_name',
        'state',
        'state_name',
        'name',
        'label'
      ];
      String? statusColumn;

      for (final column in possibleStatusColumns) {
        if (data.first.containsKey(column)) {
          statusColumn = column;
          break;
        }
      }

      // إذا وجدنا عمود الحالة، نقوم بإنشاء تخطيط بين الأرقام والنصوص
      if (statusColumn != null) {
        for (final row in data) {
          final id = row['id']?.toString() ?? row['status_id']?.toString();
          final name = row[statusColumn]?.toString();

          if (id != null && name != null) {
            numericToTextMapping[id] = name;
          }
        }

        debugPrint(
            'تم العثور على تخطيط بين الأرقام والنصوص: $numericToTextMapping');
      }
    }

    // تحويل البيانات إلى تنسيق مناسب للمخطط الدائري
    for (final row in data) {
      // البحث عن القيم بطرق مختلفة
      dynamic xValue = _getColumnValue(row, xAxisColumn);
      dynamic yValue = _getColumnValue(row, yAxisColumn);

      // التحقق من وجود القيم
      if (xValue != null && yValue != null) {
        String xValueStr = xValue.toString();
        double numericValue = 0;

        // استخدام النص بدلاً من الرقم إذا كان متاحاً
        if (numericToTextMapping.containsKey(xValueStr)) {
          xValueStr = numericToTextMapping[xValueStr]!;
        }

        // تحويل القيمة إلى رقم عشري
        if (yValue is int) {
          numericValue = yValue.toDouble();
        } else if (yValue is double) {
          numericValue = yValue;
        } else if (yValue is String) {
          numericValue = double.tryParse(yValue) ?? 0;
        }

        // تجنب تكرار المفاتيح - استخدام المفتاح مرة واحدة فقط
        if (!processedKeys.contains(xValueStr)) {
          // تجاهل القيم الصفرية لتحسين المخطط
          if (numericValue > 0) {
            result[xValueStr] = numericValue;
            processedKeys.add(xValueStr);
          }
        } else {
          // إذا كان المفتاح موجوداً بالفعل، نجمع القيم معاً
          if (numericValue > 0) {
            result[xValueStr] = (result[xValueStr] ?? 0) + numericValue;
          }
        }
      }
    }

    // تنظيف البيانات - إزالة المفاتيح الرقمية إذا كان لدينا نفس المفاتيح كنصوص
    final keysToRemove = <String>[];
    for (final key in result.keys) {
      if (int.tryParse(key) != null) {
        // إذا كان المفتاح رقماً، نتحقق مما إذا كان لدينا نفس القيمة كنص
        for (final textKey in result.keys) {
          if (numericToTextMapping[key] == textKey) {
            keysToRemove.add(key);
            break;
          }
        }
      }
    }

    // إزالة المفاتيح الرقمية
    for (final key in keysToRemove) {
      result.remove(key);
    }

    // طباعة النتيجة النهائية للتصحيح
    debugPrint('النتيجة النهائية للمخطط الدائري: $result');

    return result;
  }

  /// معالجة بيانات الرسم البياني النقطي أو الفقاعي
  List<ScatterSpot> _processScatterChartData(
      List<Map<String, dynamic>> data,
      String xAxisColumn,
      String yAxisColumn,
      String? sizeColumn,
      String? colorColumn) {
    final List<ScatterSpot> spots = [];
    final Set<String> processedPoints = {}; // مجموعة للتحقق من النقاط المعالجة

    // طباعة معلومات تصحيحية
    debugPrint('معالجة بيانات الرسم البياني النقطي أو الفقاعي');
    debugPrint('عمود المحور الأفقي: $xAxisColumn');
    debugPrint('عمود المحور الرأسي: $yAxisColumn');

    if (data.isNotEmpty) {
      debugPrint('مفاتيح البيانات المتاحة: ${data.first.keys.toList()}');
    }

    for (final row in data) {
      // البحث عن القيم بطرق مختلفة
      dynamic xValue = _getColumnValue(row, xAxisColumn);
      dynamic yValue = _getColumnValue(row, yAxisColumn);

      if (xValue != null && yValue != null) {
        double x = 0;
        double y = 0;

        // معالجة قيمة المحور الأفقي
        if (xValue is int) {
          x = xValue.toDouble();
        } else if (xValue is double) {
          x = xValue;
        } else if (xValue is String) {
          x = double.tryParse(xValue) ?? 0;
        }

        // معالجة قيمة المحور الرأسي
        if (yValue is int) {
          y = yValue.toDouble();
        } else if (yValue is double) {
          y = yValue;
        } else if (yValue is String) {
          y = double.tryParse(yValue) ?? 0;
        }

        // تجنب تكرار النقاط - استخدام النقطة مرة واحدة فقط
        final String pointKey = '$x,$y';
        if (!processedPoints.contains(pointKey)) {
          // معالجة قيمة الحجم (للرسم البياني الفقاعي)
          double size = 8; // الحجم الافتراضي
          if (sizeColumn != null) {
            final sizeValue = _getColumnValue(row, sizeColumn);
            if (sizeValue != null) {
              if (sizeValue is int) {
                size = sizeValue.toDouble().clamp(4, 20);
              } else if (sizeValue is double) {
                size = sizeValue.clamp(4, 20);
              } else if (sizeValue is String) {
                size = (double.tryParse(sizeValue) ?? 8).clamp(4, 20);
              }
            }
          }

          // معالجة قيمة اللون (للتصنيف)
          int color = 0xFF2196F3; // اللون الافتراضي (أزرق)
          if (colorColumn != null) {
            final colorValue = _getColumnValue(row, colorColumn);
            if (colorValue != null) {
              // استخدام قيمة العمود لإنشاء لون فريد
              color = colorValue.toString().hashCode | 0xFF000000;
            }
          }

          spots.add(ScatterSpot(
            x,
            y,
            dotPainter: FlDotCirclePainter(
              color: Color(color),
              radius: size,
            ),
          ));

          processedPoints.add(pointKey);
        }
      }
    }

    // إذا كانت النتيجة فارغة، استخدم بيانات افتراضية
    if (spots.isEmpty) {
      spots.addAll([
        ScatterSpot(1, 5,
            dotPainter: FlDotCirclePainter(color: Colors.blue, radius: 8)),
        ScatterSpot(2, 8,
            dotPainter: FlDotCirclePainter(color: Colors.green, radius: 8)),
        ScatterSpot(3, 4,
            dotPainter: FlDotCirclePainter(color: Colors.red, radius: 8)),
        ScatterSpot(4, 6,
            dotPainter: FlDotCirclePainter(color: Colors.purple, radius: 8)),
      ]);
    }

    return spots;
  }
}
