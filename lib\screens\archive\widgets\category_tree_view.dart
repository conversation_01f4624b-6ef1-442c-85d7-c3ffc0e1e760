import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/archive_category_model.dart';


/// عرض شجرة التصنيفات
class CategoryTreeView extends StatefulWidget {
  /// قائمة التصنيفات
  final List<Map<String, dynamic>> categories;

  /// دالة يتم استدعاؤها عند اختيار تصنيف
  final Function(ArchiveCategory) onCategorySelected;

  const CategoryTreeView({
    super.key,
    required this.categories,
    required this.onCategorySelected,
  });

  @override
  State<CategoryTreeView> createState() => _CategoryTreeViewState();
}

class _CategoryTreeViewState extends State<CategoryTreeView> {
  // قائمة التصنيفات المفتوحة
  final Set<String> _expandedCategories = {};

  @override
  Widget build(BuildContext context) {
    if (widget.categories.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.category_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد تصنيفات',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: widget.categories.length,
      itemBuilder: (context, index) {
        final categoryData = widget.categories[index];
        final category = categoryData['category'] as ArchiveCategory;
        final children = categoryData['children'] as List<Map<String, dynamic>>;

        return _buildCategoryItem(category, children, 0);
      },
    );
  }

  Widget _buildCategoryItem(
    ArchiveCategory category,
    List<Map<String, dynamic>> children,
    int level,
  ) {
    final hasChildren = children.isNotEmpty;
    final isExpanded = _expandedCategories.contains(category.id);

    return Column(
      children: [
        InkWell(
          onTap: () {
            widget.onCategorySelected(category);
          },
          child: Padding(
            padding: EdgeInsets.only(
              left: 16.0,
              right: 16.0 + (level * 16.0),
              top: 8.0,
              bottom: 8.0,
            ),
            child: Row(
              children: [
                if (hasChildren)
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        if (isExpanded) {
                          _expandedCategories.remove(category.id);
                        } else {
                          _expandedCategories.add(category.id);
                        }
                      });
                    },
                    child: Icon(
                      isExpanded ? Icons.expand_more : Icons.chevron_right,
                      size: 20,
                      color: Colors.grey,
                    ),
                  )
                else
                  const SizedBox(width: 20),
                const SizedBox(width: 8),
                Icon(
                  _getCategoryIcon(category.icon),
                  size: 20,
                  color: _getCategoryColor(category.color),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    category.name,
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        if (hasChildren && isExpanded)
          Column(
            children: children.map((childData) {
              final childCategory = childData['category'] as ArchiveCategory;
              final grandchildren = childData['children'] as List<Map<String, dynamic>>;

              return _buildCategoryItem(childCategory, grandchildren, level + 1);
            }).toList(),
          ),
      ],
    );
  }

  Color _getCategoryColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) {
      return Colors.blue;
    }

    try {
      return Color(int.parse(colorString.replaceAll('#', '0xff')));
    } catch (e) {
      return Colors.blue;
    }
  }

  IconData _getCategoryIcon(String? iconName) {
    switch (iconName) {
      case 'folder_special':
        return Icons.folder_special;
      case 'account_balance':
        return Icons.account_balance;
      case 'description':
        return Icons.description;
      case 'mail':
        return Icons.mail;
      case 'assessment':
        return Icons.assessment;
      default:
        return Icons.folder;
    }
  }
}
