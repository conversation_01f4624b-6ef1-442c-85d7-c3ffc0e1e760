{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutterproject\\flutter_application_2\\android\\app\\.cxx\\Debug\\3v1g2l3c\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\flutterproject\\flutter_application_2\\android\\app\\.cxx\\Debug\\3v1g2l3c\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}