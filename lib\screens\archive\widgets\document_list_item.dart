import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/archive_document_model.dart';

import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/utils/date_formatter.dart';
import 'package:flutter_application_2/utils/file_processor.dart';

/// عنصر قائمة وثيقة
///
/// يعرض معلومات الوثيقة في شكل عنصر قائمة
class DocumentListItem extends StatelessWidget {
  /// وثيقة الأرشيف
  final ArchiveDocument document;

  /// دالة يتم استدعاؤها عند النقر على العنصر
  final VoidCallback? onTap;

  const DocumentListItem({
    super.key,
    required this.document,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final fileType = FileProcessor.getFileType(document.filePath);

    return Card(
      elevation: 1,
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: CircleAvatar(
          backgroundColor: _getFileColor(fileType).withAlpha(51),
          child: Icon(
            _getFileIcon(fileType),
            color: _getFileColor(fileType),
          ),
        ),
        title: Text(
          document.title,
          style: AppStyles.subtitle1,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            if (document.description != null && document.description!.isNotEmpty)
              Text(
                document.description!,
                style: AppStyles.body2,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 14,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 4),
                Text(
                  DateFormatter.formatDate(document.createdAt),
                  style: AppStyles.caption,
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.file_present,
                  size: 14,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 4),
                Text(
                  FileProcessor.formatFileSize(document.fileSize),
                  style: AppStyles.caption,
                ),
              ],
            ),
          ],
        ),
        trailing: _buildConfidentialityBadge(document.confidentiality),
      ),
    );
  }

  Widget _buildConfidentialityBadge(ArchiveDocumentConfidentiality confidentiality) {
    Color color;
    String text;

    switch (confidentiality) {
      case ArchiveDocumentConfidentiality.normal:
        color = Colors.grey;
        text = 'عادي';
        break;
      case ArchiveDocumentConfidentiality.confidential:
        color = Colors.blue;
        text = 'سري';
        break;
      case ArchiveDocumentConfidentiality.highlyConfidential:
        color = Colors.orange;
        text = 'سري للغاية';
        break;
      case ArchiveDocumentConfidentiality.topSecret:
        color = Colors.red;
        text = 'سري جداً';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withAlpha(51),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  IconData _getFileIcon(String fileType) {
    switch (fileType) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'image':
        return Icons.image;
      case 'word':
        return Icons.description;
      case 'excel':
        return Icons.table_chart;
      case 'powerpoint':
        return Icons.slideshow;
      case 'text':
        return Icons.text_snippet;
      case 'video':
        return Icons.video_file;
      case 'audio':
        return Icons.audio_file;
      default:
        return Icons.insert_drive_file;
    }
  }

  Color _getFileColor(String fileType) {
    switch (fileType) {
      case 'pdf':
        return Colors.red;
      case 'image':
        return Colors.blue;
      case 'word':
        return Colors.indigo;
      case 'excel':
        return Colors.green;
      case 'powerpoint':
        return Colors.orange;
      case 'text':
        return Colors.grey;
      case 'video':
        return Colors.purple;
      case 'audio':
        return Colors.teal;
      default:
        return Colors.blueGrey;
    }
  }
}
