import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageController extends GetxController {
  static const String languageCodeKey = 'languageCode';
  static const String countryCodeKey = 'countryCode';

  // اللغة الافتراضية هي العربية
  final RxString _currentLanguageCode = 'ar'.obs;
  final RxString _currentCountryCode = ''.obs;

  // Getters for the current language
  String get currentLanguageCode => _currentLanguageCode.value;
  String get currentCountryCode => _currentCountryCode.value;
  Locale get currentLocale => Locale(_currentLanguageCode.value, _currentCountryCode.value);

  // Check if the current language is Arabic
  bool get isArabic => _currentLanguageCode.value == 'ar';

  @override
  void onInit() {
    super.onInit();
    loadSavedLanguage();
  }

  // Load the saved language from SharedPreferences
  Future<void> loadSavedLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    final savedLanguageCode = prefs.getString(languageCodeKey);
    final savedCountryCode = prefs.getString(countryCodeKey);

    if (savedLanguageCode != null) {
      _currentLanguageCode.value = savedLanguageCode;
      _currentCountryCode.value = savedCountryCode ?? '';
      updateLocale();
    }
  }

  // Change the language
  Future<void> changeLanguage(String languageCode, String countryCode) async {
    _currentLanguageCode.value = languageCode;
    _currentCountryCode.value = countryCode;

    // Save the selected language to SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(languageCodeKey, languageCode);
    await prefs.setString(countryCodeKey, countryCode);

    // Update the locale
    updateLocale();
  }

  // تعيين اللغة العربية دائمًا
  Future<void> toggleLanguage() async {
    // دائمًا استخدم اللغة العربية
    await changeLanguage('ar', '');
  }

  // Update the locale in GetX
  void updateLocale() {
    Get.updateLocale(currentLocale);
  }
}
