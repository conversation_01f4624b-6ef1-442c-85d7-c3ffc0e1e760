import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/task_status_controller.dart';
import '../../models/task_status_model.dart';
import '../../constants/app_styles.dart';

/// شاشة إدارة حالات المهام
///
/// توفر واجهة لإدارة حالات المهام في النظام
class TaskStatusManagementTab extends StatefulWidget {
  const TaskStatusManagementTab({super.key});

  @override
  State<TaskStatusManagementTab> createState() => _TaskStatusManagementTabState();
}

class _TaskStatusManagementTabState extends State<TaskStatusManagementTab> {
  late TaskStatusController _taskStatusController;
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _colorController = TextEditingController();
  final _iconController = TextEditingController();
  final _sortOrderController = TextEditingController();
  
  TaskStatus? _selectedTaskStatus;
  bool _showInactiveStatuses = false;
  bool _isDefault = false;

  @override
  void initState() {
    super.initState();
    
    // التحقق من وجود المتحكم أو إنشاؤه
    if (!Get.isRegistered<TaskStatusController>()) {
      Get.put(TaskStatusController());
    }
    _taskStatusController = Get.find<TaskStatusController>();
    
    // تحميل البيانات
    _loadData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _colorController.dispose();
    _iconController.dispose();
    _sortOrderController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    await _taskStatusController.loadTaskStatuses(includeInactive: _showInactiveStatuses);
  }

  /// إعادة تعيين نموذج الإدخال
  void _resetForm() {
    _selectedTaskStatus = null;
    _nameController.clear();
    _descriptionController.clear();
    _colorController.clear();
    _iconController.clear();
    _sortOrderController.text = '0';
    _isDefault = false;
    _formKey.currentState?.reset();
  }

  /// تحويل قيمة اللون من نص إلى كائن لون
  Color _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        return Color(int.parse('0xFF${colorString.substring(1)}'));
      } else {
        return Color(int.parse('0xFF$colorString'));
      }
    } catch (e) {
      return Colors.grey;
    }
  }

  /// عرض مربع حوار تأكيد الحذف
  void _showDeleteConfirmationDialog(TaskStatus taskStatus) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف حالة المهمة "${taskStatus.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              
              final success = await _taskStatusController.deleteTaskStatus(taskStatus.id);
              
              if (success) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف حالة المهمة بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } else {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('فشل حذف حالة المهمة: ${_taskStatusController.errorMessage.value}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار إنشاء/تعديل حالة مهمة
  void _showTaskStatusDialog({TaskStatus? taskStatus}) {
    _selectedTaskStatus = taskStatus;
    
    if (taskStatus != null) {
      _nameController.text = taskStatus.name;
      _descriptionController.text = taskStatus.description;
      _colorController.text = taskStatus.color ?? '';
      _iconController.text = taskStatus.icon ?? '';
      _sortOrderController.text = taskStatus.sortOrder.toString();
      _isDefault = taskStatus.isDefault;
    } else {
      _resetForm();
    }
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          taskStatus == null ? 'إنشاء حالة مهمة جديدة' : 'تعديل حالة المهمة',
          style: AppStyles.headingMedium,
        ),
        content: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // اسم حالة المهمة
                TextFormField(
                  controller: _nameController,
                  decoration: AppStyles.inputDecoration(
                    labelText: 'اسم حالة المهمة',
                    hintText: 'أدخل اسم حالة المهمة',
                    prefixIcon: const Icon(Icons.assignment_turned_in),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال اسم حالة المهمة';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                
                // وصف حالة المهمة
                TextFormField(
                  controller: _descriptionController,
                  decoration: AppStyles.inputDecoration(
                    labelText: 'وصف حالة المهمة',
                    hintText: 'أدخل وصف حالة المهمة',
                    prefixIcon: const Icon(Icons.description),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال وصف حالة المهمة';
                    }
                    return null;
                  },
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                
                // لون حالة المهمة
                TextFormField(
                  controller: _colorController,
                  decoration: AppStyles.inputDecoration(
                    labelText: 'لون حالة المهمة (اختياري)',
                    hintText: 'أدخل قيمة اللون (مثال: FF5733)',
                    prefixIcon: const Icon(Icons.color_lens),
                  ),
                ),
                const SizedBox(height: 16),
                
                // أيقونة حالة المهمة
                TextFormField(
                  controller: _iconController,
                  decoration: AppStyles.inputDecoration(
                    labelText: 'أيقونة حالة المهمة (اختياري)',
                    hintText: 'أدخل اسم الأيقونة',
                    prefixIcon: const Icon(Icons.emoji_objects),
                  ),
                ),
                const SizedBox(height: 16),
                
                // ترتيب العرض
                TextFormField(
                  controller: _sortOrderController,
                  decoration: AppStyles.inputDecoration(
                    labelText: 'ترتيب العرض',
                    hintText: 'أدخل رقم ترتيب العرض',
                    prefixIcon: const Icon(Icons.sort),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال ترتيب العرض';
                    }
                    if (int.tryParse(value) == null) {
                      return 'الرجاء إدخال رقم صحيح';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                
                // حالة افتراضية
                CheckboxListTile(
                  title: const Text('حالة افتراضية للمهام الجديدة'),
                  value: _isDefault,
                  onChanged: (value) {
                    setState(() {
                      _isDefault = value ?? false;
                    });
                  },
                  controlAffinity: ListTileControlAffinity.leading,
                ),
              ],
            ),
          ),
        ),
        actions: [
          // زر الإلغاء
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetForm();
            },
            child: const Text('إلغاء'),
          ),
          
          // زر الحفظ
          ElevatedButton(
            onPressed: () async {
              if (_formKey.currentState!.validate()) {
                final name = _nameController.text;
                final description = _descriptionController.text;
                final color = _colorController.text.isEmpty ? null : _colorController.text;
                final icon = _iconController.text.isEmpty ? null : _iconController.text;
                final sortOrder = int.tryParse(_sortOrderController.text) ?? 0;
                
                bool success;
                
                if (_selectedTaskStatus == null) {
                  // إنشاء حالة مهمة جديدة
                  success = await _taskStatusController.createTaskStatus(
                    name: name,
                    description: description,
                    color: color,
                    icon: icon,
                    isDefault: _isDefault,
                    sortOrder: sortOrder,
                  );
                } else {
                  // تحديث حالة المهمة الحالية
                  final updatedTaskStatus = _selectedTaskStatus!.copyWith(
                    name: name,
                    description: description,
                    color: color,
                    icon: icon,
                    isDefault: _isDefault,
                    sortOrder: sortOrder,
                  );
                  
                  success = await _taskStatusController.updateTaskStatus(updatedTaskStatus);
                }
                
                if (success) {
                  Navigator.of(context).pop();
                  _resetForm();
                  
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          _selectedTaskStatus == null
                              ? 'تم إنشاء حالة المهمة بنجاح'
                              : 'تم تحديث حالة المهمة بنجاح',
                        ),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } else {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'فشل العملية: ${_taskStatusController.errorMessage.value}',
                        ),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (_taskStatusController.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        
        if (_taskStatusController.taskStatuses.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.assignment_turned_in_outlined,
                  color: Colors.grey,
                  size: 48,
                ),
                const SizedBox(height: 16),
                const Text(
                  'لا توجد حالات مهام',
                  style: TextStyle(color: Colors.grey),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => _showTaskStatusDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('إنشاء حالة مهمة جديدة'),
                ),
              ],
            ),
          );
        }
        
        return Column(
          children: [
            // شريط الأدوات
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // زر إنشاء حالة مهمة جديدة
                  ElevatedButton.icon(
                    onPressed: () => _showTaskStatusDialog(),
                    icon: const Icon(Icons.add),
                    label: const Text('إنشاء حالة مهمة جديدة'),
                  ),
                  const Spacer(),
                  
                  // خيار عرض الحالات غير النشطة
                  Row(
                    children: [
                      const Text('عرض الحالات غير النشطة'),
                      Checkbox(
                        value: _showInactiveStatuses,
                        onChanged: (value) {
                          setState(() {
                            _showInactiveStatuses = value ?? false;
                            _loadData();
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // قائمة حالات المهام
            Expanded(
              child: ReorderableListView.builder(
                itemCount: _taskStatusController.taskStatuses.length,
                onReorder: (oldIndex, newIndex) async {
                  if (oldIndex < newIndex) {
                    newIndex -= 1;
                  }
                  
                  final statuses = List<TaskStatus>.from(_taskStatusController.taskStatuses);
                  final item = statuses.removeAt(oldIndex);
                  statuses.insert(newIndex, item);
                  
                  // تحديث ترتيب العرض
                  final orderedIds = statuses.map((s) => s.id).toList();
                  await _taskStatusController.reorderTaskStatuses(orderedIds);
                },
                itemBuilder: (context, index) {
                  final taskStatus = _taskStatusController.taskStatuses[index];
                  
                  return Card(
                    key: ValueKey(taskStatus.id),
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: ListTile(
                      leading: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: taskStatus.color != null
                              ? _parseColor(taskStatus.color!)
                              : Colors.grey,
                          shape: BoxShape.circle,
                        ),
                      ),
                      title: Text(
                        taskStatus.name,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: taskStatus.isActive ? null : Colors.grey,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            taskStatus.description,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: taskStatus.isActive ? null : Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'ترتيب العرض: ${taskStatus.sortOrder}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // علامة الافتراضي
                          if (taskStatus.isDefault)
                            const Chip(
                              label: Text('افتراضي'),
                              backgroundColor: Colors.green,
                              labelStyle: TextStyle(color: Colors.white),
                            ),
                          
                          // حالة النشاط
                          if (!taskStatus.isActive)
                            const Chip(
                              label: Text('غير نشط'),
                              backgroundColor: Colors.grey,
                              labelStyle: TextStyle(color: Colors.white),
                            ),
                          
                          // زر التعديل
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () => _showTaskStatusDialog(taskStatus: taskStatus),
                            tooltip: 'تعديل',
                          ),
                          
                          // زر الحذف
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () => _showDeleteConfirmationDialog(taskStatus),
                            tooltip: 'حذف',
                            color: Colors.red,
                          ),
                        ],
                      ),
                      onTap: () => _showTaskStatusDialog(taskStatus: taskStatus),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      }),
    );
  }
}
