import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import '../models/task_priority_model.dart';
import '../services/task_priority_service.dart';

/// متحكم أولويات المهام
///
/// يوفر وظائف للتعامل مع أولويات المهام في واجهة المستخدم
class TaskPriorityController extends GetxController {
  final TaskPriorityService _taskPriorityService = TaskPriorityService();
  final Uuid _uuid = const Uuid();

  // قائمة أولويات المهام
  final RxList<TaskPriority> taskPriorities = <TaskPriority>[].obs;
  
  // حالة التحميل
  final RxBool isLoading = false.obs;
  
  // رسالة الخطأ
  final RxString errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadTaskPriorities();
  }

  /// تحميل جميع أولويات المهام
  Future<void> loadTaskPriorities({bool includeInactive = false}) async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final priorities = await _taskPriorityService.getAllTaskPriorities(includeInactive: includeInactive);
      taskPriorities.assignAll(priorities);
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل أولويات المهام: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// إنشاء أولوية مهمة جديدة
  Future<bool> createTaskPriority({
    required String name,
    required String description,
    required int level,
    String? color,
    String? icon,
    bool isDefault = false,
  }) async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final newTaskPriority = TaskPriority(
        id: _uuid.v4(),
        name: name,
        description: description,
        color: color,
        icon: icon,
        isActive: true,
        isDefault: isDefault,
        level: level,
        createdAt: DateTime.now(),
      );
      
      final createdPriority = await _taskPriorityService.createTaskPriority(newTaskPriority);
      
      // إذا كانت الأولوية الجديدة هي الافتراضية، قم بتحديث القائمة بالكامل
      if (isDefault) {
        await loadTaskPriorities();
      } else {
        taskPriorities.add(createdPriority);
        // ترتيب القائمة حسب مستوى الأولوية
        taskPriorities.sort((a, b) => a.level.compareTo(b.level));
      }
      
      return true;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إنشاء أولوية المهمة: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديث أولوية مهمة
  Future<bool> updateTaskPriority(TaskPriority taskPriority) async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final updatedTaskPriority = taskPriority.copyWith(
        updatedAt: DateTime.now(),
      );
      
      final success = await _taskPriorityService.updateTaskPriority(updatedTaskPriority);
      
      if (success) {
        // إذا كانت الأولوية المحدثة هي الافتراضية، قم بتحديث القائمة بالكامل
        if (updatedTaskPriority.isDefault) {
          await loadTaskPriorities();
        } else {
          final index = taskPriorities.indexWhere((priority) => priority.id == updatedTaskPriority.id);
          if (index != -1) {
            taskPriorities[index] = updatedTaskPriority;
            // ترتيب القائمة حسب مستوى الأولوية
            taskPriorities.sort((a, b) => a.level.compareTo(b.level));
          }
        }
      }
      
      return success;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحديث أولوية المهمة: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// حذف أولوية مهمة
  Future<bool> deleteTaskPriority(String id) async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final success = await _taskPriorityService.deleteTaskPriority(id);
      
      if (success) {
        taskPriorities.removeWhere((priority) => priority.id == id);
      }
      
      return success;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء حذف أولوية المهمة: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// البحث عن أولويات المهام
  Future<List<TaskPriority>> searchTaskPriorities(String query) async {
    if (query.isEmpty) {
      return taskPriorities;
    }
    
    try {
      return await _taskPriorityService.searchTaskPriorities(query);
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء البحث عن أولويات المهام: $e';
      return [];
    }
  }

  /// تبديل حالة النشاط
  Future<bool> toggleTaskPriorityActive(String id, bool isActive) async {
    final priority = taskPriorities.firstWhere((p) => p.id == id);
    final updatedPriority = priority.copyWith(isActive: isActive);
    return await updateTaskPriority(updatedPriority);
  }

  /// تعيين كأولوية افتراضية
  Future<bool> setAsDefault(String id) async {
    final priority = taskPriorities.firstWhere((p) => p.id == id);
    final updatedPriority = priority.copyWith(isDefault: true);
    return await updateTaskPriority(updatedPriority);
  }
}
