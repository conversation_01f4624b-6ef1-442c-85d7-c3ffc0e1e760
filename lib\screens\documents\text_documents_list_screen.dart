import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../controllers/text_document_controller.dart';
import '../../models/text_document_model.dart';
import '../../utils/app_colors.dart';
import '../../utils/app_styles.dart';
import '../../routes/app_routes.dart';
import 'text_document_editor_screen.dart';
import 'advanced_text_document_editor_screen.dart';

/// شاشة قائمة المستندات النصية
///
/// تستخدم لعرض قائمة المستندات النصية
class TextDocumentsListScreen extends StatefulWidget {
  /// معرف المهمة (اختياري)
  final String? taskId;

  /// عنوان الشاشة
  final String? title;

  /// إنشاء شاشة قائمة المستندات النصية
  const TextDocumentsListScreen({
    super.key,
    this.taskId,
    this.title,
  });

  @override
  State<TextDocumentsListScreen> createState() =>
      _TextDocumentsListScreenState();
}

class _TextDocumentsListScreenState extends State<TextDocumentsListScreen> {
  final TextDocumentController _documentController =
      Get.find<TextDocumentController>();
  final TextEditingController _searchController = TextEditingController();
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd HH:mm');

  @override
  void initState() {
    super.initState();
    _loadDocuments();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل المستندات
  Future<void> _loadDocuments() async {
    if (widget.taskId != null) {
      // تحميل المستندات المرتبطة بمهمة
      await _documentController.loadDocumentsByTaskId(widget.taskId!);
    } else {
      // تحميل جميع المستندات
      await _documentController.loadAllDocuments();
    }
  }

  /// البحث عن مستندات
  Future<void> _searchDocuments(String query) async {
    await _documentController.searchDocuments(query);
  }

  /// فتح مستند للتحرير
  void _openDocument(String documentId) {
    Get.to(() => TextDocumentEditorScreen(documentId: documentId));
  }

  /// فتح مستند للتحرير في المحرر المتقدم
  void _openAdvancedDocument(String documentId) {
    Get.toNamed(AppRoutes.advancedTextDocumentEditor, arguments: {
      'documentId': documentId,
    });
  }

  /// إنشاء مستند جديد
  void _createNewDocument() {
    Get.to(() => TextDocumentEditorScreen(
          taskId: widget.taskId,
          defaultTitle: '',
          defaultType: TextDocumentType.note,
        ));
  }

  /// إنشاء مستند جديد باستخدام المحرر المتقدم
  void _createNewAdvancedDocument() {
    Get.toNamed(AppRoutes.advancedTextDocumentEditor, arguments: {
      'taskId': widget.taskId,
      'defaultTitle': '',
      'defaultType': TextDocumentType.note,
    });
  }

  /// حذف مستند
  Future<void> _deleteDocument(String documentId) async {
    // عرض مربع حوار للتأكيد
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا المستند؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _documentController.deleteDocument(documentId);
    }
  }

  /// بناء بطاقة مستند
  Widget _buildDocumentCard(TextDocument document) {
    // تحديد أيقونة نوع المستند
    IconData typeIcon;
    String typeLabel;

    switch (document.type) {
      case TextDocumentType.note:
        typeIcon = Icons.note;
        typeLabel = 'ملاحظة';
        break;
      case TextDocumentType.report:
        typeIcon = Icons.description;
        typeLabel = 'تقرير';
        break;
      case TextDocumentType.template:
        typeIcon = Icons.article_outlined;
        typeLabel = 'قالب';
        break;
      case TextDocumentType.letter:
        typeIcon = Icons.mail_outline;
        typeLabel = 'خطاب';
        break;
      case TextDocumentType.memo:
        typeIcon = Icons.sticky_note_2_outlined;
        typeLabel = 'مذكرة';
        break;
      case TextDocumentType.contract:
        typeIcon = Icons.handshake_outlined;
        typeLabel = 'عقد';
        break;
      case TextDocumentType.other:
        typeIcon = Icons.more_horiz;
        typeLabel = 'أخرى';
        break;
    }

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor:
              AppColors.primary.withValues(alpha: 26), // 0.1 * 255 = 25.5 ≈ 26
          child: Icon(typeIcon, color: AppColors.primary),
        ),
        title: Text(
          document.title,
          style: AppStyles.titleMedium,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'النوع: $typeLabel',
              style: AppStyles.bodySmall,
            ),
            Text(
              'تاريخ الإنشاء: ${_dateFormat.format(document.createdAt)}',
              style: AppStyles.bodySmall,
            ),
            if (document.updatedAt != null)
              Text(
                'آخر تحديث: ${_dateFormat.format(document.updatedAt!)}',
                style: AppStyles.bodySmall,
              ),
            if (document.isShared)
              Row(
                children: [
                  Icon(
                    Icons.share,
                    size: 14,
                    color: AppColors.primary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'مشترك',
                    style: AppStyles.bodySmall.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _openDocument(document.id);
                break;
              case 'advanced_edit':
                _openAdvancedDocument(document.id);
                break;
              case 'delete':
                _deleteDocument(document.id);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem<String>(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 20),
                  SizedBox(width: 8),
                  Text('تحرير'),
                ],
              ),
            ),
            PopupMenuItem<String>(
              value: 'advanced_edit',
              child: Row(
                children: [
                  Icon(Icons.article, size: 20, color: Colors.green),
                  SizedBox(width: 8),
                  Text('تحرير متقدم (Word)',
                      style: TextStyle(color: Colors.green)),
                ],
              ),
            ),
            const PopupMenuItem<String>(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 20, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => _openDocument(document.id),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title ?? 'المستندات النصية'),
        actions: [
          // زر البحث
          IconButton(
            icon: const Icon(Icons.search),
            tooltip: 'بحث',
            onPressed: () {
              showSearch(
                context: context,
                delegate: DocumentSearchDelegate(
                  _documentController,
                  _searchDocuments,
                  _buildDocumentCard,
                ),
              );
            },
          ),
        ],
      ),
      body: Obx(() {
        if (_documentController.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (_documentController.error.value.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _documentController.error.value,
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadDocuments,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        if (_documentController.documents.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.description_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                const Text(
                  'لا توجد مستندات',
                  style: TextStyle(fontSize: 18),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: _createNewDocument,
                  icon: const Icon(Icons.add),
                  label: const Text('إنشاء مستند جديد'),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _loadDocuments,
          child: ListView.builder(
            padding: const EdgeInsets.all(8),
            itemCount: _documentController.documents.length,
            itemBuilder: (context, index) {
              final document = _documentController.documents[index];
              return _buildDocumentCard(document);
            },
          ),
        );
      }),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          // زر إنشاء مستند جديد باستخدام المحرر المتقدم
          FloatingActionButton.small(
            heroTag: 'advanced_editor',
            onPressed: _createNewAdvancedDocument,
            tooltip: 'إنشاء مستند متقدم (Word)',
            backgroundColor: Colors.green,
            child: const Icon(Icons.article),
          ),
          const SizedBox(height: 16),
          // زر إنشاء مستند جديد
          FloatingActionButton(
            heroTag: 'simple_editor',
            onPressed: _createNewDocument,
            tooltip: 'إنشاء مستند جديد',
            child: const Icon(Icons.add),
          ),
        ],
      ),
    );
  }
}

/// مندوب البحث عن المستندات
class DocumentSearchDelegate extends SearchDelegate<String> {
  final TextDocumentController _documentController;
  final Function(String) _searchFunction;
  final Widget Function(TextDocument) _buildDocumentCard;

  DocumentSearchDelegate(
    this._documentController,
    this._searchFunction,
    this._buildDocumentCard,
  );

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    _searchFunction(query);

    return Obx(() {
      if (_documentController.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (_documentController.documents.isEmpty) {
        return const Center(
          child: Text('لا توجد نتائج'),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: _documentController.documents.length,
        itemBuilder: (context, index) {
          final document = _documentController.documents[index];
          return _buildDocumentCard(document);
        },
      );
    });
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.isEmpty) {
      return const Center(
        child: Text('اكتب للبحث عن المستندات'),
      );
    }

    _searchFunction(query);

    return Obx(() {
      if (_documentController.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (_documentController.documents.isEmpty) {
        return const Center(
          child: Text('لا توجد نتائج'),
        );
      }

      return ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: _documentController.documents.length,
        itemBuilder: (context, index) {
          final document = _documentController.documents[index];
          return _buildDocumentCard(document);
        },
      );
    });
  }
}
