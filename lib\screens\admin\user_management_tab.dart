import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/admin_controller.dart';
import '../../models/user_model.dart';
import '../../services/app_storage_service.dart';
import '../../utils/file_processor.dart';
import '../../utils/responsive_helper.dart';

/// تبويب إدارة المستخدمين
///
/// يوفر واجهة لإدارة المستخدمين وصلاحياتهم
class UserManagementTab extends StatefulWidget {
  const UserManagementTab({super.key});

  @override
  State<UserManagementTab> createState() => _UserManagementTabState();
}

class _UserManagementTabState extends State<UserManagementTab> {
  final AdminController _adminController = Get.find<AdminController>();
  final AppStorageService _appStorageService = Get.find<AppStorageService>();
  final TextEditingController _searchController = TextEditingController();
  final RxList<User> _filteredUsers = <User>[].obs;
  final RxBool _isSearching = false.obs;
  final Uuid _uuid = const Uuid();
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _filteredUsers.assignAll(_adminController.users);

    // الاستماع للتغييرات في قائمة المستخدمين
    ever(_adminController.users, (_) {
      _filterUsers();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تصفية المستخدمين بناءً على نص البحث
  void _filterUsers() {
    final searchText = _searchController.text.toLowerCase();

    if (searchText.isEmpty) {
      _filteredUsers.assignAll(_adminController.users);
      return;
    }

    _filteredUsers.assignAll(_adminController.users.where((user) {
      return user.name.toLowerCase().contains(searchText) ||
          user.email.toLowerCase().contains(searchText);
    }).toList());
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildSearchBar(),
          const SizedBox(height: 16),
          Expanded(
            child: _buildUserList(),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الصفحة
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'إدارة المستخدمين',
          style: AppStyles.titleLarge,
        ),
        ElevatedButton.icon(
          onPressed: _showAddUserDialog,
          icon: const Icon(Icons.person_add),
          label: const Text('إضافة مستخدم'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  /// بناء شريط البحث
  Widget _buildSearchBar() {
    return TextField(
      controller: _searchController,
      decoration: InputDecoration(
        hintText: 'البحث عن مستخدم...',
        prefixIcon: const Icon(Icons.search),
        suffixIcon: Obx(() {
          return _isSearching.value
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _isSearching.value = false;
                    _filterUsers();
                  },
                )
              : const SizedBox.shrink();
        }),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      onChanged: (value) {
        _isSearching.value = value.isNotEmpty;
        _filterUsers();
      },
    );
  }

  /// بناء قائمة المستخدمين
  Widget _buildUserList() {
    return Obx(() {
      if (_adminController.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      if (_filteredUsers.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.person_off,
                size: 64,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                'لا يوجد مستخدمين',
                style: AppStyles.titleMedium,
              ),
              if (_isSearching.value)
                const Text(
                  'حاول تغيير معايير البحث',
                  style: TextStyle(color: Colors.grey),
                ),
            ],
          ),
        );
      }

      return ResponsiveHelper.isDesktop(context) ||
              ResponsiveHelper.isTablet(context)
          ? _buildUsersDataTable()
          : _buildUsersMobileList();
    });
  }

  /// بناء جدول المستخدمين للشاشات الكبيرة
  Widget _buildUsersDataTable() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          columnSpacing: 20, // تقليل المسافة بين الأعمدة
          horizontalMargin: 12, // تقليل الهامش الأفقي
          columns: [
            DataColumn(
                label: SizedBox(
              width: 50,
              child: const Text('الصورة', overflow: TextOverflow.ellipsis),
            )),
            DataColumn(
                label: SizedBox(
              width: 100,
              child: const Text('الاسم', overflow: TextOverflow.ellipsis),
            )),
            DataColumn(
                label: SizedBox(
              width: 120,
              child: const Text('البريد الإلكتروني',
                  overflow: TextOverflow.ellipsis),
            )),
            DataColumn(
                label: SizedBox(
              width: 80,
              child: const Text('الدور', overflow: TextOverflow.ellipsis),
            )),
            DataColumn(
                label: SizedBox(
              width: 70,
              child: const Text('الحالة', overflow: TextOverflow.ellipsis),
            )),
            DataColumn(
                label: SizedBox(
              width: 90,
              child:
                  const Text('تاريخ الإنشاء', overflow: TextOverflow.ellipsis),
            )),
            DataColumn(
                label: SizedBox(
              width: 100,
              child:
                  const Text('آخر تسجيل دخول', overflow: TextOverflow.ellipsis),
            )),
            DataColumn(
                label: SizedBox(
              width: 100,
              child: const Text('الإجراءات', overflow: TextOverflow.ellipsis),
            )),
          ],
          rows: _filteredUsers.map((user) {
            return DataRow(
              cells: [
                DataCell(
                  CircleAvatar(
                    radius: 15,
                    backgroundColor: AppColors.primary.withAlpha(51),
                    backgroundImage: user.profileImage != null
                        ? NetworkImage(user.profileImage!)
                        : null,
                    child: user.profileImage == null
                        ? Text(
                            user.name.isNotEmpty
                                ? user.name[0].toUpperCase()
                                : '?',
                            style: const TextStyle(
                              color: AppColors.primary,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          )
                        : null,
                  ),
                ),
                DataCell(Text(user.name, overflow: TextOverflow.ellipsis)),
                DataCell(Text(user.email, overflow: TextOverflow.ellipsis)),
                DataCell(Text(_getRoleText(user.role),
                    overflow: TextOverflow.ellipsis)),
                DataCell(
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        user.isActive ? Icons.check_circle : Icons.cancel,
                        color: user.isActive ? Colors.green : Colors.red,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Flexible(
                          child: Text(user.isActive ? 'نشط' : 'معطل',
                              overflow: TextOverflow.ellipsis)),
                    ],
                  ),
                ),
                DataCell(Text(_formatDate(user.createdAt),
                    overflow: TextOverflow.ellipsis)),
                DataCell(Text(
                    user.lastLogin != null
                        ? _formatDate(user.lastLogin!)
                        : 'لم يسجل الدخول',
                    overflow: TextOverflow.ellipsis)),
                DataCell(
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.edit, size: 20),
                        tooltip: 'تعديل',
                        onPressed: () => _showEditUserDialog(user),
                        padding: const EdgeInsets.all(4),
                        constraints: const BoxConstraints(),
                      ),
                      IconButton(
                        icon: Icon(
                          user.isActive ? Icons.block : Icons.check_circle,
                          size: 20,
                          color: user.isActive ? Colors.red : Colors.green,
                        ),
                        tooltip: user.isActive ? 'تعطيل' : 'تفعيل',
                        onPressed: () => _toggleUserActive(user),
                        padding: const EdgeInsets.all(4),
                        constraints: const BoxConstraints(),
                      ),
                      IconButton(
                        icon: const Icon(Icons.admin_panel_settings, size: 20),
                        tooltip: 'الصلاحيات',
                        onPressed: () {
                          // الانتقال إلى تبويب إدارة الصلاحيات
                          final adminController = Get.find<AdminController>();
                          adminController.selectedPermissionUser.value = user;
                          // الانتقال إلى تبويب الصلاحيات (الثاني)
                          Get.find<TabController>(tag: 'admin_tab_controller')
                              .animateTo(1);
                        },
                        padding: const EdgeInsets.all(4),
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  /// بناء قائمة المستخدمين للشاشات الصغيرة
  Widget _buildUsersMobileList() {
    return ListView.builder(
      itemCount: _filteredUsers.length,
      itemBuilder: (context, index) {
        final user = _filteredUsers[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: AppColors.primary.withAlpha(51),
              backgroundImage: user.profileImage != null
                  ? NetworkImage(user.profileImage!)
                  : null,
              child: user.profileImage == null
                  ? Text(
                      user.name.isNotEmpty ? user.name[0].toUpperCase() : '?',
                      style: const TextStyle(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),
            title: Text(user.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(user.email),
                Text('الدور: ${_getRoleText(user.role)}'),
                Row(
                  children: [
                    Icon(
                      user.isActive ? Icons.check_circle : Icons.cancel,
                      color: user.isActive ? Colors.green : Colors.red,
                      size: 12,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      user.isActive ? 'نشط' : 'معطل',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _showEditUserDialog(user);
                    break;
                  case 'toggle':
                    _toggleUserActive(user);
                    break;
                  case 'permissions':
                    // الانتقال إلى تبويب إدارة الصلاحيات
                    final adminController = Get.find<AdminController>();
                    adminController.selectedPermissionUser.value = user;
                    // الانتقال إلى تبويب الصلاحيات (الثاني)
                    // الانتقال إلى تبويب الصلاحيات (الثاني)
                    Get.find<TabController>(tag: 'admin_tab_controller')
                        .animateTo(1);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem<String>(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('تعديل'),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'toggle',
                  child: Row(
                    children: [
                      Icon(
                        user.isActive ? Icons.block : Icons.check_circle,
                        color: user.isActive ? Colors.red : Colors.green,
                      ),
                      const SizedBox(width: 8),
                      Text(user.isActive ? 'تعطيل' : 'تفعيل'),
                    ],
                  ),
                ),
                const PopupMenuItem<String>(
                  value: 'permissions',
                  child: Row(
                    children: [
                      Icon(Icons.admin_panel_settings, color: Colors.blue),
                      SizedBox(width: 8),
                      Text('إدارة الصلاحيات'),
                    ],
                  ),
                ),
              ],
            ),
            onTap: () => _showUserDetailsDialog(user),
          ),
        );
      },
    );
  }

  /// عرض خيارات اختيار الصورة
  Future<void> _showImagePickerOptions(
      BuildContext context, Function(File) onImageSelected) async {
    await showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('اختيار من المعرض'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromGallery(onImageSelected);
              },
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('التقاط صورة'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromCamera(onImageSelected);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// اختيار صورة من المعرض
  Future<void> _pickImageFromGallery(Function(File) onImageSelected) async {
    final pickedFile = await _imagePicker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      // معالجة الصورة وضغطها
      final processedImage = await _processImage(File(pickedFile.path));
      onImageSelected(processedImage);
    }
  }

  /// التقاط صورة من الكاميرا
  Future<void> _pickImageFromCamera(Function(File) onImageSelected) async {
    final pickedFile = await _imagePicker.pickImage(
      source: ImageSource.camera,
      imageQuality: 80,
    );

    if (pickedFile != null) {
      // معالجة الصورة وضغطها
      final processedImage = await _processImage(File(pickedFile.path));
      onImageSelected(processedImage);
    }
  }

  /// معالجة الصورة وضغطها
  Future<File> _processImage(File imageFile) async {
    try {
      // استخدام FileProcessor لإنشاء صورة مصغرة
      final thumbnailPath = await FileProcessor.createThumbnail(
        imagePath: imageFile.path,
        thumbnailSize: 500, // حجم مناسب لصورة الملف الشخصي
      );

      if (thumbnailPath != null) {
        return File(thumbnailPath);
      }
    } catch (e) {
      debugPrint('Error processing image: $e');
    }

    // إذا فشلت المعالجة، إرجاع الصورة الأصلية
    return imageFile;
  }

  /// عرض حوار إضافة مستخدم جديد
  void _showAddUserDialog() {
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final passwordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    UserRole selectedRole = UserRole.employee;
    File? selectedImage;

    Get.dialog(
      AlertDialog(
        title: const Text('إضافة مستخدم جديد'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // صورة المستخدم
                  Center(
                    child: Stack(
                      alignment: Alignment.bottomRight,
                      children: [
                        CircleAvatar(
                          radius: 50,
                          backgroundColor: AppColors.primary.withAlpha(51),
                          backgroundImage: selectedImage != null
                              ? FileImage(selectedImage!)
                              : null,
                          child: selectedImage == null
                              ? const Icon(
                                  Icons.person,
                                  size: 50,
                                  color: AppColors.primary,
                                )
                              : null,
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(
                              Icons.camera_alt,
                              color: Colors.white,
                              size: 20,
                            ),
                            onPressed: () {
                              _showImagePickerOptions(context, (image) {
                                setState(() {
                                  selectedImage = image;
                                });
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // حقول البيانات
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'الاسم',
                      hintText: 'أدخل اسم المستخدم',
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: emailController,
                    decoration: const InputDecoration(
                      labelText: 'البريد الإلكتروني',
                      hintText: 'أدخل البريد الإلكتروني',
                    ),
                    keyboardType: TextInputType.emailAddress,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: passwordController,
                    decoration: const InputDecoration(
                      labelText: 'كلمة المرور',
                      hintText: 'أدخل كلمة المرور',
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: confirmPasswordController,
                    decoration: const InputDecoration(
                      labelText: 'تأكيد كلمة المرور',
                      hintText: 'أدخل كلمة المرور مرة أخرى',
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<UserRole>(
                    value: selectedRole,
                    decoration: const InputDecoration(
                      labelText: 'الدور',
                    ),
                    items: UserRole.values.map((role) {
                      return DropdownMenuItem<UserRole>(
                        value: role,
                        child: Text(_getRoleText(role)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedRole = value;
                        });
                      }
                    },
                  ),
                ],
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // التحقق من صحة البيانات
              if (nameController.text.isEmpty ||
                  emailController.text.isEmpty ||
                  passwordController.text.isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يرجى ملء جميع الحقول المطلوبة',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }

              if (passwordController.text != confirmPasswordController.text) {
                Get.snackbar(
                  'خطأ',
                  'كلمة المرور وتأكيدها غير متطابقين',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }

              // حفظ صورة الملف الشخصي إذا تم اختيارها
              String? profileImagePath;
              if (selectedImage != null) {
                final userId = _uuid.v4(); // إنشاء معرف المستخدم مسبقًا
                profileImagePath = await _appStorageService.saveProfileImage(
                  selectedImage!,
                  userId,
                );
              }

              // إنشاء المستخدم الجديد
              final newUser = User(
                id: _uuid.v4(),
                name: nameController.text.trim(),
                email: emailController.text.trim(),
                password: passwordController.text,
                profileImage: profileImagePath,
                role: selectedRole,
                isActive: true,
                createdAt: DateTime.now(),
              );

              final result = await _adminController.createUser(newUser);

              if (result != null) {
                Get.back();
                Get.snackbar(
                  'تم بنجاح',
                  'تم إضافة المستخدم بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  'خطأ',
                  'فشل إضافة المستخدم: ${_adminController.error.value}',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار تعديل مستخدم
  void _showEditUserDialog(User user) {
    final nameController = TextEditingController(text: user.name);
    final emailController = TextEditingController(text: user.email);
    final passwordController = TextEditingController();
    UserRole selectedRole = user.role;
    File? selectedImage;

    Get.dialog(
      AlertDialog(
        title: const Text('تعديل المستخدم'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // صورة المستخدم
                  Center(
                    child: Stack(
                      alignment: Alignment.bottomRight,
                      children: [
                        CircleAvatar(
                          radius: 50,
                          backgroundColor: AppColors.primary.withAlpha(51),
                          backgroundImage: selectedImage != null
                              ? FileImage(selectedImage!)
                              : (user.profileImage != null
                                  ? NetworkImage(user.profileImage!)
                                      as ImageProvider
                                  : null),
                          child:
                              selectedImage == null && user.profileImage == null
                                  ? const Icon(
                                      Icons.person,
                                      size: 50,
                                      color: AppColors.primary,
                                    )
                                  : null,
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(
                              Icons.camera_alt,
                              color: Colors.white,
                              size: 20,
                            ),
                            onPressed: () {
                              _showImagePickerOptions(context, (image) {
                                setState(() {
                                  selectedImage = image;
                                });
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // حقول البيانات
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'الاسم',
                      hintText: 'أدخل اسم المستخدم',
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: emailController,
                    decoration: const InputDecoration(
                      labelText: 'البريد الإلكتروني',
                      hintText: 'أدخل البريد الإلكتروني',
                    ),
                    keyboardType: TextInputType.emailAddress,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: passwordController,
                    decoration: const InputDecoration(
                      labelText:
                          'كلمة المرور الجديدة (اتركها فارغة للإبقاء على كلمة المرور الحالية)',
                      hintText: 'أدخل كلمة المرور الجديدة',
                    ),
                    obscureText: true,
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<UserRole>(
                    value: selectedRole,
                    decoration: const InputDecoration(
                      labelText: 'الدور',
                    ),
                    items: UserRole.values.map((role) {
                      return DropdownMenuItem<UserRole>(
                        value: role,
                        child: Text(_getRoleText(role)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedRole = value;
                        });
                      }
                    },
                  ),
                ],
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // التحقق من صحة البيانات
              if (nameController.text.isEmpty || emailController.text.isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يرجى ملء جميع الحقول المطلوبة',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }

              // حفظ صورة الملف الشخصي إذا تم اختيارها
              String? profileImagePath = user.profileImage;
              if (selectedImage != null) {
                profileImagePath = await _appStorageService.saveProfileImage(
                  selectedImage!,
                  user.id,
                );

                if (profileImagePath == null) {
                  Get.snackbar(
                    'تحذير',
                    'فشل في حفظ صورة الملف الشخصي، سيتم تحديث البيانات الأخرى فقط',
                    snackPosition: SnackPosition.BOTTOM,
                    backgroundColor: Colors.amber.shade100,
                    colorText: Colors.amber.shade900,
                  );
                }
              }

              // تحديث المستخدم
              final updatedUser = user.copyWith(
                name: nameController.text.trim(),
                email: emailController.text.trim(),
                password: passwordController.text.isEmpty
                    ? user.password
                    : passwordController.text,
                profileImage: profileImagePath,
                role: selectedRole,
              );

              final result = await _adminController.updateUser(updatedUser);

              if (result) {
                Get.back();
                Get.snackbar(
                  'تم بنجاح',
                  'تم تحديث المستخدم بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  'خطأ',
                  'فشل تحديث المستخدم: ${_adminController.error.value}',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار تفاصيل المستخدم
  void _showUserDetailsDialog(User user) {
    Get.dialog(
      AlertDialog(
        title: const Text('تفاصيل المستخدم'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة المستخدم
              if (user.profileImage != null)
                Center(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: CircleAvatar(
                      radius: 60,
                      backgroundColor: AppColors.primary.withAlpha(51),
                      backgroundImage: NetworkImage(user.profileImage!),
                    ),
                  ),
                ),
              _buildDetailItem('الاسم', user.name),
              _buildDetailItem('البريد الإلكتروني', user.email),
              _buildDetailItem('الدور', _getRoleText(user.role)),
              _buildDetailItem('الحالة', user.isActive ? 'نشط' : 'معطل'),
              _buildDetailItem('تاريخ الإنشاء', _formatDate(user.createdAt)),
              _buildDetailItem(
                'آخر تسجيل دخول',
                user.lastLogin != null
                    ? _formatDate(user.lastLogin!)
                    : 'لم يسجل الدخول',
              ),
              if (user.departmentId != null)
                _buildDetailItem('القسم', user.departmentId!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _showEditUserDialog(user);
            },
            child: const Text('تعديل'),
          ),
        ],
      ),
    );
  }

  /// تبديل حالة نشاط المستخدم
  void _toggleUserActive(User user) async {
    final result =
        await _adminController.toggleUserActive(user.id, !user.isActive);

    if (result) {
      Get.snackbar(
        'تم بنجاح',
        user.isActive ? 'تم تعطيل المستخدم بنجاح' : 'تم تفعيل المستخدم بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } else {
      Get.snackbar(
        'خطأ',
        'فشل تغيير حالة المستخدم: ${_adminController.error.value}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// بناء عنصر تفاصيل
  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
          Text(
            value,
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  /// الحصول على نص الدور
  String _getRoleText(UserRole role) {
    switch (role) {
      case UserRole.superAdmin:
        return 'مدير النظام العام';
      case UserRole.admin:
        return 'مدير إدارة';
      case UserRole.departmentManager:
        return 'مدير قسم';
      case UserRole.employee:
        return 'موظف';
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
