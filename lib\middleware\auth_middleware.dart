import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controllers/auth_controller.dart';
import '../routes/app_routes.dart';

/// وسيط التحقق من المصادقة
///
/// يتحقق من حالة تسجيل الدخول قبل الانتقال إلى الشاشات المحمية
class AuthMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    try {
      final authController = Get.find<AuthController>();

      // التحقق من حالة تسجيل الدخول
      if (authController.isLoggedIn) {
        // المستخدم مسجل الدخول، السماح بالوصول
        return null;
      } else {
        // المستخدم غير مسجل الدخول، إعادة التوجيه إلى شاشة تسجيل الدخول
        debugPrint('المستخدم غير مسجل الدخول، إعادة التوجيه إلى شاشة تسجيل الدخول');
        return const RouteSettings(name: AppRoutes.login);
      }
    } catch (e) {
      // خطأ في العثور على متحكم المصادقة، إعادة التوجيه إلى شاشة تسجيل الدخول
      debugPrint('خطأ في وسيط المصادقة: $e');
      return const RouteSettings(name: AppRoutes.login);
    }
  }
}
