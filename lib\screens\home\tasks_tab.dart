import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' as foundation;
import 'package:get/get.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/user_controller.dart';
import '../../models/task_model.dart';
import '../../models/task_status_enum.dart';
import '../../models/user_model.dart';
import '../../utils/date_formatter.dart';
import '../../widgets/simple_drag_drop_board.dart';
import '../../widgets/draggable_divider.dart';
import '../tasks/task_detail_screen.dart';
import '../tasks/create_task_screen.dart';
import '../../routes/app_routes.dart';
import '../../database/database_helper.dart';

class TasksTab extends StatefulWidget {
  const TasksTab({super.key});

  @override
  State<TasksTab> createState() => _TasksTabState();
}

class _TasksTabState extends State<TasksTab>
    with SingleTickerProviderStateMixin, RouteAware {
  // مراقب المسارات للتتبع عند العودة إلى الصفحة
  late RouteObserver<PageRoute> _routeObserver;
  late TabController _tabController;
  TaskStatus? _filterStatus;
  TaskPriority? _filterPriority;
  bool _isGroupedByStatus = true; // تجميع حسب الحالة
  bool _isListView = false; // عرض القائمة
  bool _isDragDropView = false; // عرض لوحة السحب والإفلات
  bool _showSidePanel = false; // عرض تفاصيل المهمة
  String? _selectedTaskId; // معرف المهمة المحددة

  // متغيرات للتحكم في حجم الأقسام
  double _mainPanelFlex = 2;
  double _sidePanelFlex = 1;
  final double _minPanelFlex = 1;
  final double _maxPanelFlex = 5;

  // متغيرات التصفية
  String? _filterAssignee;
  bool _filterHasSubtasks = false;
  double? _filterMinProgress;
  double? _filterMaxProgress;
  DateTime? _filterDueDateFrom;
  DateTime? _filterDueDateTo;
  DateTime? _filterCreationDateFrom;
  DateTime? _filterCreationDateTo;
  String? _filterTaskType;
  List<String> _filterTags =
      []; // سنحتفظ بهذا المتغير لكن لن نستخدمه في واجهة المستخدم

  // متغيرات الترتيب
  String _sortBy = 'dueDate'; // القيمة الافتراضية: تاريخ الاستحقاق
  String _sortDirection = 'asc'; // القيمة الافتراضية: تصاعدي

  @override
  void initState() {
    super.initState();
    // تهيئة TabController
    _tabController = TabController(length: 3, vsync: this);

    // الحصول على مراقب المسارات من GetX
    _routeObserver = Get.find<RouteObserver<PageRoute>>();

    // استخدام ميكانيكية addPostFrameCallback لتأخير تحميل المهام حتى اكتمال بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTasks();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // تسجيل هذه الصفحة مع مراقب المسارات
    final route = ModalRoute.of(context);
    if (route != null && route is PageRoute) {
      _routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    // إلغاء تسجيل هذه الصفحة من مراقب المسارات
    _routeObserver.unsubscribe(this);
    _tabController.dispose();
    super.dispose();
  }

  /// يتم استدعاء هذه الدالة عند العودة إلى هذه الصفحة
  @override
  void didPopNext() {
    debugPrint('تم العودة إلى صفحة المهام');
    // إعادة تحميل المهام عند العودة إلى الصفحة
    _loadTasks();
  }

  Future<void> _loadTasks() async {
    final taskController = Get.find<TaskController>();
    final authController = Get.find<AuthController>();

    // إعادة تعيين الفلاتر لتجنب اختفاء المهام
    setState(() {
      // إعادة تعيين الفلاتر فقط إذا كانت المهام تختفي
      if (taskController.tasks.isEmpty &&
          (_filterStatus != null ||
              _filterPriority != null ||
              _filterAssignee != null)) {
        _filterStatus = null;
        _filterPriority = null;
        _filterAssignee = null;
        _filterMinProgress = null;
        _filterMaxProgress = null;
        _filterDueDateFrom = null;
        _filterDueDateTo = null;
        _filterCreationDateFrom = null;
        _filterCreationDateTo = null;
        _filterTaskType = null;
        _filterTags = [];
      }
    });

    if (authController.currentUser.value != null) {
      try {
        // استخدام وظيفة تحميل المهام حسب صلاحيات المستخدم
        await taskController
            .loadTasksByUserPermissions(authController.currentUser.value!.id);

        // التحقق من وجود مهام بعد التحميل
        if (taskController.tasks.isEmpty &&
            taskController.error.value.isEmpty) {
          // لا توجد مهام ولا يوجد خطأ - هذا طبيعي
          Get.snackbar(
            'معلومات',
            'لا توجد مهام متاحة حالياً. يمكنك إنشاء مهام جديدة.',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.blue.shade100,
            colorText: Colors.blue.shade800,
            duration: const Duration(seconds: 3),
          );
        } else if (taskController.error.value.isNotEmpty) {
          // حدث خطأ أثناء التحميل
          Get.snackbar(
            'خطأ في تحميل المهام',
            taskController.error.value,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
          );
        } else {
          // إضافة رسالة توضيحية للمستخدم حول المهام المعروضة
          String message = '';
          if (authController.isAdmin) {
            message = 'تم تحميل جميع المهام في النظام';
          } else if (authController.isDepartmentManager) {
            message = 'تم تحميل مهام القسم الخاص بك';
          } else {
            message = 'تم تحميل المهام المخصصة لك فقط';
          }

          // عرض رسالة توضيحية للمستخدم
          Get.snackbar(
            'معلومات',
            message,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green.shade100,
            colorText: Colors.green.shade800,
            duration: const Duration(seconds: 3),
          );
        }
      } catch (e) {
        // خطأ غير متوقع
        Get.snackbar(
          'خطأ',
          'حدث خطأ غير متوقع: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
          duration: const Duration(seconds: 5),
          mainButton: TextButton(
            onPressed: () {
              // عرض مربع حوار إصلاح قاعدة البيانات
              if (e.toString().contains('SQL') ||
                  e.toString().contains('database')) {
                DatabaseHelper.showDatabaseErrorDialog(context);
              }
            },
            child: const Text('إصلاح', style: TextStyle(color: Colors.white)),
          ),
        );
      }
    } else {
      // في حالة عدم وجود مستخدم مسجل، نعرض رسالة خطأ
      Get.snackbar(
        'خطأ',
        'يجب تسجيل الدخول لعرض المهام',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// بناء عنوان صفحة المهام
  Widget _buildTasksTitle() {
    final authController = Get.find<AuthController>();

    // العنوان الرئيسي دائمًا "المهام"
    String subtitle = '';

    // إضافة عنوان فرعي حسب نوع المستخدم
    if (authController.currentUser.value != null) {
      if (authController.isAdmin) {
        subtitle = 'جميع المهام في النظام';
      } else if (authController.isDepartmentManager) {
        subtitle = 'مهام القسم';
      } else {
        subtitle = 'المهام المخصصة لك';
      }
    }

    // إذا لم يكن هناك عنوان فرعي، نعرض العنوان الرئيسي فقط
    if (subtitle.isEmpty) {
      return const Text('المهام');
    }

    // عرض العنوان الرئيسي والفرعي
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        const Text('المهام'),
        Text(
          subtitle,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.normal,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final taskController = Get.find<TaskController>();

    return Scaffold(
      appBar: AppBar(
        title: _buildTasksTitle(),
        actions: [
          // Group by status toggle
          // IconButton(
          //   icon:
          //       Icon(_isGroupedByStatus ? Icons.view_list : Icons.view_module),
          //   tooltip: _isGroupedByStatus ? 'عرض بدون تجميع' : 'تجميع حسب الحالة',
          //   onPressed: () {
          //     setState(() {
          //       _isGroupedByStatus = !_isGroupedByStatus;
          //       // عند تغيير طريقة العرض، نلغي العروض الأخرى
          //       _isDragDropView = false;
          //     });
          //   },
          // ),
          // View toggle button - toggle list view within the same screen
          IconButton(
            icon: Icon(_isListView ? Icons.view_module : Icons.view_list),
            tooltip: _isListView ? 'عرض المجموعات' : 'عرض القائمة',
            onPressed: () {
              setState(() {
                _isListView = !_isListView;
                // عند تغيير طريقة العرض، نلغي العروض الأخرى
                _isDragDropView = false;
              });
            },
          ),
          // Drag & Drop Board button
          IconButton(
            icon: Icon(
                _isDragDropView ? Icons.view_module : Icons.drag_indicator),
            tooltip:
                _isDragDropView ? 'العودة للعرض العادي' : 'لوحة السحب والإفلات',
            onPressed: () {
              setState(() {
                _isDragDropView = !_isDragDropView;
                // عند تفعيل عرض السحب والإفلات، نلغي العروض الأخرى
                if (_isDragDropView) {
                  _isListView = false;
                }
              });
            },
          ),
          // Filter button with badge for active filters
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.filter_alt_outlined),
                tooltip: 'تصفية المهام',
                onPressed: _showFilterDialog,
              ),
              if (_hasActiveFilters())
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 12,
                      minHeight: 12,
                    ),
                    child: Text(
                      _getActiveFiltersCount().toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 8,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
          // Clear filters button (only visible when filters are active)
          if (_hasActiveFilters())
            IconButton(
              icon: const Icon(Icons.filter_alt_off_outlined),
              tooltip: 'إلغاء جميع المرشحات',
              onPressed: _clearAllFilters,
            ),
          // Sort button
          // IconButton(
          //   icon: const Icon(Icons.sort),
          //   tooltip: 'ترتيب المهام',
          //   onPressed: _showSortDialog,
          // ),
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: _loadTasks,
          ),
          // Database repair button
          //لانحتاج له
          // IconButton(
          //   icon: const Icon(Icons.build_circle_outlined),
          //   tooltip: 'إصلاح قاعدة البيانات',
          //   onPressed: _repairDatabase,
          // ),
        ],
      ),
      // إضافة شريط معلومات لتوضيح إمكانية السحب والإفلات
      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        color: Get.isDarkMode ? AppColors.darkCard : Colors.blue.shade50,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              textDirection: TextDirection.rtl,
              children: [
                Icon(Icons.drag_indicator,
                    color: Get.isDarkMode
                        ? AppColors.primary
                        : Colors.blue.shade700),
                const SizedBox(width: 2),
                Expanded(
                  child: Text(
                    'يمكنك سحب المهام وإفلاتها بين المجموعات لتغيير حالتها أو إلى شريط الأولويات لتغيير الأولوية',
                    style: TextStyle(
                        color: Get.isDarkMode
                            ? AppColors.darkTextPrimary
                            : Colors.blue.shade700),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
            if (!_isGroupedByStatus || _isListView || _isDragDropView)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  'لتفعيل السحب والإفلات: اضغط على ${_isGroupedByStatus ? "" : "أيقونة المجموعات"} ${!_isGroupedByStatus || !_isListView ? "" : "وأيقونة المجموعات"} ${!_isDragDropView ? "" : "وأيقونة العرض العادي"}',
                  style: TextStyle(
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
      // إضافة شريط الأولويات في أعلى الشاشة عند تفعيل عرض المجموعات
      persistentFooterButtons: _isGroupedByStatus &&
              !_isListView &&
              !_isDragDropView
          ? [
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                decoration: BoxDecoration(
                  color: Get.isDarkMode
                      ? AppColors.darkCard
                      : Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Get.isDarkMode
                          ? Colors.black.withAlpha(40)
                          : Colors.black.withAlpha(20),
                      blurRadius: 5,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // const Text(
                    //   'اسحب المهام إلى الأولويات التالية لتغييرها:',
                    //   style: TextStyle(
                    //     fontWeight: FontWeight.bold,
                    //     fontSize: 14,
                    //   ),
                    // ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const Text(
                              'مناطق الإفلات',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                _buildPriorityDropTarget(TaskPriority.low),
                                const SizedBox(width: 8),
                                _buildPriorityDropTarget(TaskPriority.medium),
                                const SizedBox(width: 8),
                                _buildPriorityDropTarget(TaskPriority.high),
                                const SizedBox(width: 8),
                                _buildPriorityDropTarget(TaskPriority.urgent),
                              ],
                            ),
                          ],
                        ),
                        Container(
                          height: 20,
                          width: 1,
                          color: Colors.grey[300],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const Text(
                              'اسحب هذه الأولويات إلى المهام',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                _buildDraggablePriority(TaskPriority.low),
                                const SizedBox(width: 8),
                                _buildDraggablePriority(TaskPriority.medium),
                                const SizedBox(width: 8),
                                _buildDraggablePriority(TaskPriority.high),
                                const SizedBox(width: 8),
                                _buildDraggablePriority(TaskPriority.urgent),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ]
          : null,
      body: Row(
        children: [
          // Main content
          Expanded(
            flex: _mainPanelFlex.toInt(),
            child: _isDragDropView
                ? _buildDragDropBoard(taskController.tasks)
                : Column(
                    children: [
                      // Tab bar
                      TabBar(
                        controller: _tabController,
                        labelColor: Get.isDarkMode ? Colors.white : Colors.blue,
                        unselectedLabelColor: Get.isDarkMode
                            ? Colors.grey[300]
                            : Colors.grey[700],
                        indicatorColor:
                            Get.isDarkMode ? Colors.white : Colors.blue,
                        tabs: const [
                          Tab(text: 'جميع المهام'),
                          Tab(text: 'قيد التنفيذ'),
                          Tab(text: 'مكتملة'),
                        ],
                      ),

                      // Tab content
                      Expanded(
                        child: TabBarView(
                          controller: _tabController,
                          children: [
                            // All Tasks tab
                            _isListView
                                ? _buildTasksTable(taskController.tasks)
                                : _isGroupedByStatus
                                    ? _buildGroupedTasksView(
                                        taskController.tasks)
                                    : _buildTasksTable(taskController.tasks),

                            // In Progress tab
                            _isListView
                                ? _buildTasksTable(taskController.tasks
                                    .where((task) =>
                                        task.status == TaskStatus.pending ||
                                        task.status == TaskStatus.inProgress ||
                                        task.status ==
                                            TaskStatus.waitingForInfo)
                                    .toList())
                                : _isGroupedByStatus
                                    ? _buildGroupedTasksView(taskController
                                        .tasks
                                        .where((task) =>
                                            task.status == TaskStatus.pending ||
                                            task.status ==
                                                TaskStatus.inProgress ||
                                            task.status ==
                                                TaskStatus.waitingForInfo)
                                        .toList())
                                    : _buildTasksTable(taskController.tasks
                                        .where((task) =>
                                            task.status == TaskStatus.pending ||
                                            task.status ==
                                                TaskStatus.inProgress ||
                                            task.status ==
                                                TaskStatus.waitingForInfo)
                                        .toList()),

                            // Completed tab
                            _isListView
                                ? _buildTasksTable(taskController.tasks
                                    .where((task) =>
                                        task.status == TaskStatus.completed)
                                    .toList())
                                : _isGroupedByStatus
                                    ? _buildGroupedTasksView(taskController
                                        .tasks
                                        .where((task) =>
                                            task.status == TaskStatus.completed)
                                        .toList())
                                    : _buildTasksTable(taskController.tasks
                                        .where((task) =>
                                            task.status == TaskStatus.completed)
                                        .toList()),
                          ],
                        ),
                      ),
                    ],
                  ),
          ),

          // فاصل قابل للسحب (يظهر فقط عند عرض اللوحة الجانبية)
          if (_showSidePanel && _selectedTaskId != null)
            DraggableDivider(
              direction: Axis.vertical,
              thickness: 8.0,
              color: Colors.grey.shade200,
              activeColor: AppColors.primary.withAlpha(51),
              onDrag: (delta) {
                // تحديث حجم الأقسام بناءً على حركة السحب
                setState(() {
                  // تحويل دلتا إلى تغيير في نسبة الـ flex
                  // قيمة موجبة تعني زيادة اليسار وتقليل اليمين
                  final flexDelta =
                      delta / 50; // معامل تحويل لجعل الحركة أكثر سلاسة

                  // التحقق من الحدود
                  if (_mainPanelFlex + flexDelta >= _minPanelFlex &&
                      _sidePanelFlex - flexDelta >= _minPanelFlex &&
                      _mainPanelFlex + flexDelta <= _maxPanelFlex &&
                      _sidePanelFlex - flexDelta <= _maxPanelFlex) {
                    _mainPanelFlex += flexDelta;
                    _sidePanelFlex -= flexDelta;
                  }
                });
              },
            ),

          // Side panel for task details
          if (_showSidePanel && _selectedTaskId != null)
            Expanded(
              flex: _sidePanelFlex.toInt(),
              child: Container(
                decoration: BoxDecoration(
                  color: Get.isDarkMode ? AppColors.darkCard : Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Get.isDarkMode
                          ? Colors.black.withAlpha(30)
                          : Colors.black.withAlpha(13),
                      blurRadius: 5,
                      offset: const Offset(-2, 0),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Side panel header
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color:
                            Get.isDarkMode ? AppColors.darkCard : Colors.white,
                        border: Border(
                          bottom: BorderSide(
                              color: Get.isDarkMode
                                  ? AppColors.darkBorder
                                  : Colors.grey.shade300),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              // أيقونة المهمة
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withAlpha(51),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Icon(
                                  Icons.task_alt,
                                  color: AppColors.primary,
                                ),
                              ),
                              const SizedBox(width: 12),
                              // عنوان الشريط الجانبي
                              const Text(
                                'تفاصيل المهمة',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                            ],
                          ),
                          // أزرار الإجراءات
                          Row(
                            children: [
                              // زر فتح في نافذة جديدة
                              IconButton(
                                icon: const Icon(Icons.open_in_new),
                                tooltip: 'فتح في نافذة جديدة',
                                onPressed: () {
                                  Get.toNamed(AppRoutes.taskDetail,
                                      arguments: {'taskId': _selectedTaskId!});
                                },
                              ),
                              // زر الإغلاق
                              IconButton(
                                icon: const Icon(Icons.close),
                                tooltip: 'إغلاق',
                                onPressed: () {
                                  setState(() {
                                    _showSidePanel = false;
                                    _selectedTaskId = null;
                                  });
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Task details content
                    Expanded(
                      child: TaskDetailScreen(taskId: _selectedTaskId!),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: 'tasks_tab_fab',
        onPressed: () {
          // Navigate to create task screen
          Get.toNamed(AppRoutes.createTask);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  String _getStatusText(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return 'قيد الانتظار';
      case TaskStatus.inProgress:
        return 'قيد التنفيذ';
      case TaskStatus.waitingForInfo:
        return 'في انتظار معلومات';
      case TaskStatus.completed:
        return 'مكتملة';
      case TaskStatus.cancelled:
        return 'ملغاة';
      case TaskStatus.news:
        return 'جديدة';
    }
  }

  String _getPriorityText(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return 'منخفضة';
      case TaskPriority.medium:
        return 'متوسطة';
      case TaskPriority.high:
        return 'عالية';
      case TaskPriority.urgent:
        return 'عاجلة';
    }
  }

  /// الحصول على أيقونة الأولوية
  IconData _getPriorityIcon(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return Icons.arrow_downward;
      case TaskPriority.medium:
        return Icons.remove;
      case TaskPriority.high:
        return Icons.arrow_upward;
      case TaskPriority.urgent:
        return Icons.priority_high;
    }
  }

  /// عرض مربع حوار التصفية
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        // متغيرات مؤقتة للتصفية
        TaskStatus? tempStatus = _filterStatus;
        TaskPriority? tempPriority = _filterPriority;
        String? tempAssignee = _filterAssignee;
        bool tempHasSubtasks = _filterHasSubtasks;
        double? tempMinProgress = _filterMinProgress;
        double? tempMaxProgress = _filterMaxProgress;
        DateTime? tempDueDateFrom = _filterDueDateFrom;
        DateTime? tempDueDateTo = _filterDueDateTo;
        DateTime? tempCreationDateFrom = _filterCreationDateFrom;
        DateTime? tempCreationDateTo = _filterCreationDateTo;
        String? tempTaskType = _filterTaskType;
        List<String> tempTags = List.from(_filterTags);

        // متغيرات مؤقتة للترتيب
        String tempSortBy = _sortBy;
        String tempSortDirection = _sortDirection;

        // تم حذف قائمة الوسوم بناءً على طلب المستخدم

        // قائمة أنواع المهام (يمكن استبدالها بقائمة ديناميكية من قاعدة البيانات)
        final userController = Get.find<UserController>();

        return StatefulBuilder(
          builder: (context, setDialogState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.8,
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان مربع الحوار
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'تصفية وترتيب المهام',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        // زر إعادة تعيين الفلاتر
                        TextButton.icon(
                          icon: const Icon(Icons.refresh),
                          label: const Text('إعادة تعيين'),
                          onPressed: () {
                            setDialogState(() {
                              tempStatus = null;
                              tempPriority = null;
                              tempAssignee = null;
                              tempHasSubtasks = false;
                              tempMinProgress = null;
                              tempMaxProgress = null;
                              tempDueDateFrom = null;
                              tempDueDateTo = null;
                              tempCreationDateFrom = null;
                              tempCreationDateTo = null;
                              tempTaskType = null;
                              tempTags = [];
                              tempSortBy = 'dueDate';
                              tempSortDirection = 'asc';
                            });
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    // استخدام Expanded مع SingleChildScrollView لتمكين التمرير
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // قسم التصفية
                            const Text(
                              'خيارات التصفية',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Divider(),

                            // تصفية حسب الحالة
                            const Text('الحالة',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            Wrap(
                              spacing: 8,
                              children: [
                                FilterChip(
                                  label: const Text('الكل'),
                                  selected: tempStatus == null,
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempStatus = null;
                                      });
                                    }
                                  },
                                ),
                                ...TaskStatus.values.map((status) {
                                  return FilterChip(
                                    label: Text(_getStatusText(status)),
                                    selected: tempStatus == status,
                                    onSelected: (selected) {
                                      setDialogState(() {
                                        tempStatus = selected ? status : null;
                                      });
                                    },
                                  );
                                }),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // تصفية حسب الأولوية
                            const Text('الأولوية',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            Wrap(
                              spacing: 8,
                              children: [
                                FilterChip(
                                  label: const Text('الكل'),
                                  selected: tempPriority == null,
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempPriority = null;
                                      });
                                    }
                                  },
                                ),
                                ...TaskPriority.values.map((priority) {
                                  return FilterChip(
                                    label: Text(_getPriorityText(priority)),
                                    selected: tempPriority == priority,
                                    onSelected: (selected) {
                                      setDialogState(() {
                                        tempPriority =
                                            selected ? priority : null;
                                      });
                                    },
                                  );
                                }),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // تصفية حسب المسؤول
                            const Text('المسؤول',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            FutureBuilder<List<User>>(
                              future: userController
                                  .loadAllUsers()
                                  .then((_) => userController.users),
                              builder: (context, snapshot) {
                                if (snapshot.connectionState ==
                                    ConnectionState.waiting) {
                                  return const CircularProgressIndicator();
                                }

                                final users = snapshot.data ?? [];
                                return Wrap(
                                  spacing: 8,
                                  children: [
                                    FilterChip(
                                      label: const Text('الكل'),
                                      selected: tempAssignee == null,
                                      onSelected: (selected) {
                                        if (selected) {
                                          setDialogState(() {
                                            tempAssignee = null;
                                          });
                                        }
                                      },
                                    ),
                                    FilterChip(
                                      label: const Text('مهامي'),
                                      selected: tempAssignee == 'me',
                                      onSelected: (selected) {
                                        setDialogState(() {
                                          tempAssignee = selected ? 'me' : null;
                                        });
                                      },
                                    ),
                                    ...users.map((user) {
                                      return FilterChip(
                                        label: Text(user.name),
                                        selected: tempAssignee == user.id,
                                        onSelected: (selected) {
                                          setDialogState(() {
                                            tempAssignee =
                                                selected ? user.id : null;
                                          });
                                        },
                                      );
                                    }),
                                  ],
                                );
                              },
                            ),
                            const SizedBox(height: 16),

                            // تصفية حسب تاريخ الاستحقاق
                            const Text('تاريخ الاستحقاق',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    decoration: AppStyles.inputDecoration(
                                      labelText: 'من',
                                      prefixIcon:
                                          const Icon(Icons.calendar_today),
                                    ),
                                    readOnly: true,
                                    controller: TextEditingController(
                                      text: tempDueDateFrom != null
                                          ? DateFormatter.formatDate(
                                              tempDueDateFrom!)
                                          : '',
                                    ),
                                    onTap: () async {
                                      final date = await showDatePicker(
                                        context: context,
                                        initialDate:
                                            tempDueDateFrom ?? DateTime.now(),
                                        firstDate: DateTime(2020),
                                        lastDate: DateTime(2030),
                                      );
                                      if (date != null) {
                                        setDialogState(() {
                                          tempDueDateFrom = date;
                                        });
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextFormField(
                                    decoration: AppStyles.inputDecoration(
                                      labelText: 'إلى',
                                      prefixIcon:
                                          const Icon(Icons.calendar_today),
                                    ),
                                    readOnly: true,
                                    controller: TextEditingController(
                                      text: tempDueDateTo != null
                                          ? DateFormatter.formatDate(
                                              tempDueDateTo!)
                                          : '',
                                    ),
                                    onTap: () async {
                                      final date = await showDatePicker(
                                        context: context,
                                        initialDate:
                                            tempDueDateTo ?? DateTime.now(),
                                        firstDate: DateTime(2020),
                                        lastDate: DateTime(2030),
                                      );
                                      if (date != null) {
                                        setDialogState(() {
                                          tempDueDateTo = date;
                                        });
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // تصفية حسب تاريخ الإنشاء
                            const Text('تاريخ الإنشاء',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    decoration: AppStyles.inputDecoration(
                                      labelText: 'من',
                                      prefixIcon:
                                          const Icon(Icons.calendar_today),
                                    ),
                                    readOnly: true,
                                    controller: TextEditingController(
                                      text: tempCreationDateFrom != null
                                          ? DateFormatter.formatDate(
                                              tempCreationDateFrom!)
                                          : '',
                                    ),
                                    onTap: () async {
                                      final date = await showDatePicker(
                                        context: context,
                                        initialDate: tempCreationDateFrom ??
                                            DateTime.now(),
                                        firstDate: DateTime(2020),
                                        lastDate: DateTime(2030),
                                      );
                                      if (date != null) {
                                        setDialogState(() {
                                          tempCreationDateFrom = date;
                                        });
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextFormField(
                                    decoration: AppStyles.inputDecoration(
                                      labelText: 'إلى',
                                      prefixIcon:
                                          const Icon(Icons.calendar_today),
                                    ),
                                    readOnly: true,
                                    controller: TextEditingController(
                                      text: tempCreationDateTo != null
                                          ? DateFormatter.formatDate(
                                              tempCreationDateTo!)
                                          : '',
                                    ),
                                    onTap: () async {
                                      final date = await showDatePicker(
                                        context: context,
                                        initialDate: tempCreationDateTo ??
                                            DateTime.now(),
                                        firstDate: DateTime(2020),
                                        lastDate: DateTime(2030),
                                      );
                                      if (date != null) {
                                        setDialogState(() {
                                          tempCreationDateTo = date;
                                        });
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // تصفية حسب نسبة التقدم
                            const Text('نسبة التقدم',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    decoration: AppStyles.inputDecoration(
                                      labelText: 'من',
                                      prefixIcon: const Icon(Icons.percent),
                                    ),
                                    keyboardType: TextInputType.number,
                                    controller: TextEditingController(
                                      text: tempMinProgress?.toString() ?? '',
                                    ),
                                    onChanged: (value) {
                                      if (value.isNotEmpty) {
                                        final progress = double.tryParse(value);
                                        if (progress != null &&
                                            progress >= 0 &&
                                            progress <= 100) {
                                          setDialogState(() {
                                            tempMinProgress = progress / 100;
                                          });
                                        }
                                      } else {
                                        setDialogState(() {
                                          tempMinProgress = null;
                                        });
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextFormField(
                                    decoration: AppStyles.inputDecoration(
                                      labelText: 'إلى',
                                      prefixIcon: const Icon(Icons.percent),
                                    ),
                                    keyboardType: TextInputType.number,
                                    controller: TextEditingController(
                                      text: tempMaxProgress?.toString() ?? '',
                                    ),
                                    onChanged: (value) {
                                      if (value.isNotEmpty) {
                                        final progress = double.tryParse(value);
                                        if (progress != null &&
                                            progress >= 0 &&
                                            progress <= 100) {
                                          setDialogState(() {
                                            tempMaxProgress = progress / 100;
                                          });
                                        }
                                      } else {
                                        setDialogState(() {
                                          tempMaxProgress = null;
                                        });
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // تصفية حسب وجود مهام فرعية
                            Row(
                              children: [
                                Checkbox(
                                  value: tempHasSubtasks,
                                  onChanged: (value) {
                                    setDialogState(() {
                                      tempHasSubtasks = value ?? false;
                                    });
                                  },
                                ),
                                const Text(
                                    'المهام التي تحتوي على مهام فرعية فقط'),
                              ],
                            ),
                            const SizedBox(height: 16),

                            const SizedBox(height: 24),

                            // قسم الترتيب
                            const Text(
                              'خيارات الترتيب',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Divider(),

                            // ترتيب حسب
                            const Text('ترتيب حسب',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            Wrap(
                              spacing: 8,
                              children: [
                                ChoiceChip(
                                  label: const Text('العنوان'),
                                  selected: tempSortBy == 'title',
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempSortBy = 'title';
                                      });
                                    }
                                  },
                                ),
                                ChoiceChip(
                                  label: const Text('تاريخ الاستحقاق'),
                                  selected: tempSortBy == 'dueDate',
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempSortBy = 'dueDate';
                                      });
                                    }
                                  },
                                ),
                                ChoiceChip(
                                  label: const Text('تاريخ الإنشاء'),
                                  selected: tempSortBy == 'createdAt',
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempSortBy = 'createdAt';
                                      });
                                    }
                                  },
                                ),
                                ChoiceChip(
                                  label: const Text('الأولوية'),
                                  selected: tempSortBy == 'priority',
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempSortBy = 'priority';
                                      });
                                    }
                                  },
                                ),
                                ChoiceChip(
                                  label: const Text('الحالة'),
                                  selected: tempSortBy == 'status',
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempSortBy = 'status';
                                      });
                                    }
                                  },
                                ),
                                ChoiceChip(
                                  label: const Text('نسبة التقدم'),
                                  selected: tempSortBy == 'progress',
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempSortBy = 'progress';
                                      });
                                    }
                                  },
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // اتجاه الترتيب
                            const Text('اتجاه الترتيب',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            Row(
                              children: [
                                Expanded(
                                  child: RadioListTile<String>(
                                    title: const Text('تصاعدي'),
                                    value: 'asc',
                                    groupValue: tempSortDirection,
                                    onChanged: (value) {
                                      setDialogState(() {
                                        tempSortDirection = value!;
                                      });
                                    },
                                  ),
                                ),
                                Expanded(
                                  child: RadioListTile<String>(
                                    title: const Text('تنازلي'),
                                    value: 'desc',
                                    groupValue: tempSortDirection,
                                    onChanged: (value) {
                                      setDialogState(() {
                                        tempSortDirection = value!;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    // أزرار الإجراءات
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16.0, vertical: 8.0),
                      child: OverflowBar(
                        spacing: 8.0,
                        alignment: MainAxisAlignment.spaceBetween,
                        children: [
                          TextButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: const Text('إلغاء'),
                          ),
                          ElevatedButton(
                            onPressed: () {
                              setState(() {
                                _filterStatus = tempStatus;
                                _filterPriority = tempPriority;
                                _filterAssignee = tempAssignee;
                                _filterHasSubtasks = tempHasSubtasks;
                                _filterMinProgress = tempMinProgress;
                                _filterMaxProgress = tempMaxProgress;
                                _filterDueDateFrom = tempDueDateFrom;
                                _filterDueDateTo = tempDueDateTo;
                                _filterCreationDateFrom = tempCreationDateFrom;
                                _filterCreationDateTo = tempCreationDateTo;
                                _filterTaskType = tempTaskType;
                                _filterTags = tempTags;
                                _sortBy = tempSortBy;
                                _sortDirection = tempSortDirection;
                              });
                              Navigator.pop(context);
                            },
                            child: const Text('تطبيق'),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// أيقونة الحالة
  IconData _getStatusIcon(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return Icons.hourglass_empty;
      case TaskStatus.inProgress:
        return Icons.play_circle_outline;
      case TaskStatus.waitingForInfo:
        return Icons.help_outline;
      case TaskStatus.completed:
        return Icons.check_circle_outline;
      case TaskStatus.cancelled:
        return Icons.cancel_outlined;
      case TaskStatus.news:
        return Icons.new_label_outlined;
    }
  }

  /// الحصول على نسبة التقدم للمهمة
  double _getTaskProgress(Task task) {
    return task.completionPercentage / 100;
  }

  /// الحصول على لون التقدم بناءً على النسبة
  Color _getProgressColor(double progress) {
    if (progress < 0.3) {
      return Colors.red;
    } else if (progress < 0.7) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  /// تطبيق التصفية على المهام
  List<Task> _applyFilters(List<Task> tasks) {
    final taskController = Get.find<TaskController>();

    // التحقق من وجود فلاتر مطبقة
    final hasFilters = _filterStatus != null ||
        _filterPriority != null ||
        _filterAssignee != null ||
        _filterHasSubtasks ||
        _filterMinProgress != null ||
        _filterMaxProgress != null ||
        _filterDueDateFrom != null ||
        _filterDueDateTo != null ||
        _filterCreationDateFrom != null ||
        _filterCreationDateTo != null ||
        _filterTaskType != null ||
        _filterTags.isNotEmpty;

    // إذا لم تكن هناك فلاتر مطبقة، نعيد جميع المهام
    if (!hasFilters) {
      return _applySorting(tasks);
    }

    // التحقق من وجود مهام قبل التصفية
    if (tasks.isEmpty) {
      return tasks;
    }

    // طباعة معلومات التصفية للتشخيص
    foundation.debugPrint(
        'تطبيق الفلاتر: الحالة=$_filterStatus، الأولوية=$_filterPriority، المسؤول=$_filterAssignee');
    foundation.debugPrint('عدد المهام قبل التصفية: ${tasks.length}');

    final filteredTasks = tasks.where((task) {
      // تصفية حسب الحالة
      if (_filterStatus != null && task.status != _filterStatus) {
        return false;
      }

      // تصفية حسب الأولوية
      if (_filterPriority != null && task.priority != _filterPriority) {
        return false;
      }

      // تصفية حسب المسؤول
      if (_filterAssignee != null) {
        if (_filterAssignee == 'me') {
          final currentUser = Get.find<AuthController>().currentUser.value;
          if (currentUser == null || task.assigneeId != currentUser.id) {
            return false;
          }
        } else if (task.assigneeId != _filterAssignee) {
          return false;
        }
      }

      // تصفية حسب وجود مهام فرعية
      if (_filterHasSubtasks) {
        final hasSubtasks = taskController.subtasks.any(
            (subtask) => subtask.parentTaskId == task.id && !subtask.isDeleted);
        if (!hasSubtasks) {
          return false;
        }
      }

      // تصفية حسب نسبة التقدم
      final progress = _getTaskProgress(task);
      if (_filterMinProgress != null && progress < _filterMinProgress!) {
        return false;
      }
      if (_filterMaxProgress != null && progress > _filterMaxProgress!) {
        return false;
      }

      // تصفية حسب تاريخ الاستحقاق
      if (_filterDueDateFrom != null && task.dueDate != null) {
        if (task.dueDate!.isBefore(_filterDueDateFrom!)) {
          return false;
        }
      }
      if (_filterDueDateTo != null && task.dueDate != null) {
        if (task.dueDate!.isAfter(_filterDueDateTo!)) {
          return false;
        }
      }

      // تصفية حسب تاريخ الإنشاء
      if (_filterCreationDateFrom != null) {
        if (task.createdAt.isBefore(_filterCreationDateFrom!)) {
          return false;
        }
      }
      if (_filterCreationDateTo != null) {
        if (task.createdAt.isAfter(_filterCreationDateTo!)) {
          return false;
        }
      }

      // تصفية حسب نوع المهمة
      if (_filterTaskType != null && task.taskTypeId != _filterTaskType) {
        return false;
      }

      // تصفية حسب الوسوم
      if (_filterTags.isNotEmpty && task.tags != null) {
        bool hasMatchingTag = false;
        for (final tag in _filterTags) {
          if (task.tags!.contains(tag)) {
            hasMatchingTag = true;
            break;
          }
        }
        if (!hasMatchingTag) {
          return false;
        }
      }

      return true;
    }).toList();

    // طباعة عدد المهام بعد التصفية للتشخيص
    foundation.debugPrint('عدد المهام بعد التصفية: ${filteredTasks.length}');

    // إذا كانت نتيجة التصفية فارغة ولكن كانت هناك مهام قبل التصفية، نعرض رسالة
    if (filteredTasks.isEmpty && tasks.isNotEmpty) {
      // استخدام Future.microtask لتجنب تداخل setState مع بناء الواجهة
      Future.microtask(() {
        Get.snackbar(
          'تنبيه',
          'لا توجد مهام تطابق معايير التصفية المحددة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.amber.shade100,
          colorText: Colors.amber.shade800,
          duration: const Duration(seconds: 3),
        );
      });
    }

    // تطبيق الترتيب على المهام المصفاة
    return _applySorting(filteredTasks);
  }

  /// إلغاء جميع المرشحات (الفلاتر)
  void _clearAllFilters() {
    setState(() {
      _filterStatus = null;
      _filterPriority = null;
      _filterAssignee = null;
      _filterHasSubtasks = false;
      _filterMinProgress = null;
      _filterMaxProgress = null;
      _filterDueDateFrom = null;
      _filterDueDateTo = null;
      _filterCreationDateFrom = null;
      _filterCreationDateTo = null;
      _filterTaskType = null;
      _filterTags = [];
      _sortBy = 'dueDate';
      _sortDirection = 'asc';
    });

    // إعادة تحميل المهام بعد إلغاء المرشحات
    _loadTasks();

    // عرض رسالة للمستخدم
    Get.snackbar(
      'تم إلغاء المرشحات',
      'تم إلغاء جميع المرشحات وإعادة ضبط الترتيب',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      duration: const Duration(seconds: 2),
    );
  }

  /// التحقق من وجود فلاتر نشطة
  bool _hasActiveFilters() {
    return _filterStatus != null ||
        _filterPriority != null ||
        _filterAssignee != null ||
        _filterHasSubtasks ||
        _filterMinProgress != null ||
        _filterMaxProgress != null ||
        _filterDueDateFrom != null ||
        _filterDueDateTo != null ||
        _filterCreationDateFrom != null ||
        _filterCreationDateTo != null ||
        _filterTaskType != null ||
        _filterTags.isNotEmpty ||
        _sortBy != 'dueDate' ||
        _sortDirection != 'asc';
  }

  /// الحصول على عدد الفلاتر النشطة
  int _getActiveFiltersCount() {
    int count = 0;

    if (_filterStatus != null) count++;
    if (_filterPriority != null) count++;
    if (_filterAssignee != null) count++;
    if (_filterHasSubtasks) count++;
    if (_filterMinProgress != null) count++;
    if (_filterMaxProgress != null) count++;
    if (_filterDueDateFrom != null) count++;
    if (_filterDueDateTo != null) count++;
    if (_filterCreationDateFrom != null) count++;
    if (_filterCreationDateTo != null) count++;
    if (_filterTaskType != null) count++;
    if (_filterTags.isNotEmpty) count++;
    if (_sortBy != 'dueDate') count++;
    if (_sortDirection != 'asc') count++;

    return count;
  }

  /// تطبيق الترتيب على المهام
  List<Task> _applySorting(List<Task> tasks) {
    if (tasks.isEmpty) {
      return tasks;
    }

    final sortedTasks = List<Task>.from(tasks);

    switch (_sortBy) {
      case 'title':
        sortedTasks.sort((a, b) {
          return _sortDirection == 'asc'
              ? a.title.compareTo(b.title)
              : b.title.compareTo(a.title);
        });
        break;
      case 'dueDate':
        sortedTasks.sort((a, b) {
          if (a.dueDate == null && b.dueDate == null) return 0;
          if (a.dueDate == null) return _sortDirection == 'asc' ? 1 : -1;
          if (b.dueDate == null) return _sortDirection == 'asc' ? -1 : 1;
          return _sortDirection == 'asc'
              ? a.dueDate!.compareTo(b.dueDate!)
              : b.dueDate!.compareTo(a.dueDate!);
        });
        break;
      case 'createdAt':
        sortedTasks.sort((a, b) {
          return _sortDirection == 'asc'
              ? a.createdAt.compareTo(b.createdAt)
              : b.createdAt.compareTo(a.createdAt);
        });
        break;
      case 'priority':
        sortedTasks.sort((a, b) {
          return _sortDirection == 'asc'
              ? a.priority.index.compareTo(b.priority.index)
              : b.priority.index.compareTo(a.priority.index);
        });
        break;
      case 'status':
        sortedTasks.sort((a, b) {
          return _sortDirection == 'asc'
              ? a.status.index.compareTo(b.status.index)
              : b.status.index.compareTo(a.status.index);
        });
        break;
      case 'progress':
        sortedTasks.sort((a, b) {
          return _sortDirection == 'asc'
              ? a.completionPercentage.compareTo(b.completionPercentage)
              : b.completionPercentage.compareTo(a.completionPercentage);
        });
        break;
    }

    return sortedTasks;
  }

  /// بناء عرض المهام المجمعة حسب الحالة
  Widget _buildGroupedTasksView(List<Task> tasks) {
    // تطبيق التصفية
    final filteredTasks = _applyFilters(tasks);

    if (filteredTasks.isEmpty) {
      return const Center(
        child: Text('لا توجد مهام'),
      );
    }

    // تجميع المهام حسب الحالة
    final Map<TaskStatus, List<Task>> groupedTasks = {};

    for (final task in filteredTasks) {
      if (!groupedTasks.containsKey(task.status)) {
        groupedTasks[task.status] = [];
      }
      groupedTasks[task.status]!.add(task);
    }

    // ترتيب مجموعات الحالة بترتيب محدد
    final sortedStatuses = [
      TaskStatus.inProgress,
      TaskStatus.pending,
      TaskStatus.waitingForInfo,
      TaskStatus.completed,
      TaskStatus.cancelled,
    ].where((status) => groupedTasks.containsKey(status)).toList();

    // إضافة جميع الحالات المتاحة حتى لو لم تكن هناك مهام بها
    // لتمكين السحب والإفلات إلى مجموعات فارغة
    final allStatuses = [
      TaskStatus.inProgress,
      TaskStatus.pending,
      TaskStatus.waitingForInfo,
      TaskStatus.completed,
      TaskStatus.cancelled,
    ];

    // إضافة الحالات غير الموجودة في المجموعات الحالية
    for (final status in allStatuses) {
      if (!groupedTasks.containsKey(status)) {
        groupedTasks[status] = [];
        sortedStatuses.add(status);
      }
    }

    // إعادة ترتيب الحالات حسب الترتيب المحدد
    sortedStatuses
        .sort((a, b) => allStatuses.indexOf(a) - allStatuses.indexOf(b));

    // استخدام ListView بدلاً من CustomScrollView لتعطيل ميزة السحب للأسفل
    return ListView.builder(
      physics: const ClampingScrollPhysics(), // تعطيل ميزة السحب للأسفل
      itemCount: sortedStatuses.length,
      itemBuilder: (context, index) {
        final status = sortedStatuses[index];
        final statusTasks = groupedTasks[status]!;

        // استخدام RepaintBoundary لتحسين أداء الرسم
        return RepaintBoundary(
          child: _buildStatusGroup(status, statusTasks),
        );
      },
    );
  }

  /// بناء مجموعة مهام حسب الحالة
  Widget _buildStatusGroup(TaskStatus status, List<Task> tasks) {
    // إنشاء منطقة إفلات للمهام
    return DragTarget<Task>(
      builder: (context, candidateData, rejectedData) {
        // تغيير لون الخلفية عند سحب مهمة فوق المجموعة
        final isHovering = candidateData.isNotEmpty;

        return Card(
          margin: const EdgeInsets.all(8),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(
              color: AppColors.getTaskStatusColor(status.index),
              width:
                  isHovering ? 2 : 1, // زيادة سمك الحدود عند السحب فوق المجموعة
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان المجموعة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isHovering
                      ? AppColors.getTaskStatusColor(status.index)
                          .withAlpha(50) // تغيير لون الخلفية عند السحب
                      : AppColors.getTaskStatusColor(status.index)
                          .withAlpha(25),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _getStatusIcon(status),
                          color: AppColors.getTaskStatusColor(status.index),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${_getStatusText(status)} (${tasks.length})',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppColors.getTaskStatusColor(status.index),
                          ),
                        ),
                      ],
                    ),
                    // زر إضافة مهمة لهذه المجموعة
                    IconButton(
                      icon: const Icon(Icons.add),
                      tooltip: 'إضافة مهمة جديدة',
                      onPressed: () {
                        // الانتقال إلى شاشة إنشاء مهمة مع تحديد الحالة مسبقًا
                        Get.to(() => CreateTaskScreen(initialStatus: status))
                            ?.then((_) => _loadTasks());
                      },
                    ),
                  ],
                ),
              ),

              // المهام في هذه المجموعة
              ReorderableListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: tasks.length,
                onReorder: (oldIndex, newIndex) {
                  // تعديل المؤشر إذا كان المؤشر الجديد بعد المؤشر القديم
                  if (newIndex > oldIndex) {
                    newIndex -= 1;
                  }

                  // تحريك المهمة في القائمة
                  setState(() {
                    final taskController = Get.find<TaskController>();
                    final allTasks = taskController.tasks;

                    // الحصول على المهمة التي تم تحريكها
                    final task = tasks[oldIndex];

                    // إعادة ترتيب المهام في القائمة المحلية
                    final updatedTasks = List<Task>.from(tasks);
                    final movedTask = updatedTasks.removeAt(oldIndex);
                    updatedTasks.insert(newIndex, movedTask);

                    // تحديث قائمة المهام الرئيسية
                    for (int i = 0; i < allTasks.length; i++) {
                      if (allTasks[i].id == task.id) {
                        // تحديث المهمة في القائمة الرئيسية
                        allTasks[i] = task;
                        break;
                      }
                    }

                    // تحديث واجهة المستخدم
                    _loadTasks();
                  });

                  // تأثير اهتزاز عند إعادة الترتيب
                  HapticFeedback.lightImpact();
                },
                itemBuilder: (context, index) {
                  return KeyedSubtree(
                    key: ValueKey(tasks[index].id),
                    child: _buildTaskRow(tasks[index]),
                  );
                },
              ),

              // إضافة منطقة فارغة للإفلات إذا كانت القائمة فارغة
              if (tasks.isEmpty)
                Container(
                  height: 100,
                  alignment: Alignment.center,
                  child: Text(
                    'اسحب المهام هنا',
                    style: TextStyle(
                      color: Colors.grey[400],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
      // قبول المهمة المسحوبة وتغيير حالتها
      onAcceptWithDetails: (DragTargetDetails<Task> details) async {
        final task = details.data;
        // تجنب تغيير الحالة إذا كانت المهمة بنفس الحالة
        if (task.status != status) {
          final taskController = Get.find<TaskController>();
          final authController = Get.find<AuthController>();

          // إضافة تأثير اهتزاز عند قبول الإفلات
          HapticFeedback.mediumImpact();

          // عرض مؤشر تحميل
          // Get.dialog(
          //   const Center(
          //     child: CircularProgressIndicator(),
          //   ),
          //   barrierDismissible: false,
          // );

          // طباعة معلومات تشخيصية
          foundation.debugPrint('محاولة تغيير حالة المهمة: ${task.id}');
          foundation.debugPrint(
              'المستخدم الحالي: ${authController.currentUser.value!.id}');
          foundation.debugPrint('الحالة الجديدة: $status');

          // تحديث حالة المهمة
          final result = await taskController.updateTaskStatus(
            task.id,
            authController.currentUser.value!.id,
            status,
            'تم تغيير الحالة عن طريق سحب المهمة',
          );

          // طباعة نتيجة العملية
          foundation.debugPrint('نتيجة تحديث حالة المهمة: $result');
          if (!result && taskController.error.value.isNotEmpty) {
            foundation.debugPrint('رسالة الخطأ: ${taskController.error.value}');
          }

          // إغلاق مؤشر التحميل
          Get.back();

          // إعادة تحميل المهام بعد التحديث مع تأخير قصير
          if (result) {
            // تأخير قصير للتأكد من اكتمال عملية التحديث في قاعدة البيانات
            await Future.delayed(const Duration(milliseconds: 300));
            await _loadTasks();

            // عرض رسالة نجاح
            Get.snackbar(
              'تم بنجاح',
              'تم تغيير حالة المهمة إلى ${_getStatusText(status)}',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.green.shade100,
              colorText: Colors.green.shade800,
              duration: const Duration(seconds: 2),
            );
          } else {
            // عرض رسالة خطأ مع التفاصيل
            Get.snackbar(
              'خطأ',
              taskController.error.value.isNotEmpty
                  ? 'فشل تغيير حالة المهمة: ${taskController.error.value}'
                  : 'فشل تغيير حالة المهمة',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.red.shade100,
              colorText: Colors.red.shade800,
              duration: const Duration(seconds: 3),
            );
          }

          // تحديث واجهة المستخدم
          setState(() {});
        }
      },
    );
  }

  /// بناء جدول المهام
  Widget _buildTasksTable(List<Task> tasks) {
    // تطبيق التصفية
    final filteredTasks = _applyFilters(tasks);

    if (filteredTasks.isEmpty) {
      return const Center(
        child: Text('لا توجد مهام'),
      );
    }

    return Column(
      children: [
        // رأس الجدول
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            border: Border(
              bottom: BorderSide(color: Colors.grey[300]!),
            ),
          ),
          child: Row(
            children: [
              // عمود العنوان
              Expanded(
                flex: 4,
                child: Text(
                  'العنوان',
                  style: AppStyles.labelMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // عمود الحالة
              Expanded(
                flex: 2,
                child: Text(
                  'الحالة',
                  style: AppStyles.labelMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // عمود الأولوية
              Expanded(
                flex: 2,
                child: Text(
                  'الأولوية',
                  style: AppStyles.labelMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // عمود تاريخ الاستحقاق
              Expanded(
                flex: 3,
                child: Text(
                  'تاريخ الاستحقاق',
                  style: AppStyles.labelMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              // عمود المرفقات
              Expanded(
                flex: 1,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.attach_file,
                      size: 16,
                      color: Colors.amber.shade700,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'المرفقات',
                      style: AppStyles.labelMedium.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              // عمود التقدم
              Expanded(
                flex: 3,
                child: Text(
                  'التقدم',
                  style: AppStyles.labelMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),

        // محتوى الجدول
        Expanded(
          child: ListView.builder(
            physics: const ClampingScrollPhysics(), // تعطيل ميزة السحب للأسفل
            itemCount: filteredTasks.length,
            itemBuilder: (context, index) {
              final task = filteredTasks[index];
              return _buildTaskRow(task);
            },
          ),
        ),
      ],
    );
  }

  /// بناء صف مهمة
  Widget _buildTaskRow(Task task) {
    final taskController = Get.find<TaskController>();
    final userController = Get.find<UserController>();
    final isSelected = task.id == _selectedTaskId;

    // بناء محتوى صف المهمة
    Widget taskContent = Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.withAlpha(26) : Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          // أزلنا أيقونة الأولوية من هنا

          // عمود العنوان
          Expanded(
            flex: 4,
            child: Row(
              children: [
                // عرض عدد المهام الفرعية
                FutureBuilder<int>(
                  future: taskController.getSubtasksCount(task.id),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const SizedBox.shrink();
                    }

                    final subtasksCount = snapshot.data ?? 0;
                    if (subtasksCount > 0) {
                      return FutureBuilder<int>(
                        future:
                            taskController.getCompletedSubtasksCount(task.id),
                        builder: (context, completedSnapshot) {
                          final completedCount = completedSnapshot.data ?? 0;

                          return Container(
                            margin: const EdgeInsets.only(left: 4),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade50,
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: Colors.blue.shade200,
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.checklist,
                                  size: 12,
                                  color: Colors.blue.shade700,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '$completedCount/$subtasksCount',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue.shade700,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    }

                    return const SizedBox.shrink();
                  },
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عنوان المهمة
                      Text(
                        task.title,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      // منشئ المهمة
                      FutureBuilder<User?>(
                        future: userController.getUserById(task.creatorId),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.waiting) {
                            return const SizedBox.shrink();
                          }

                          final creator = snapshot.data;
                          if (creator == null) {
                            return const SizedBox.shrink();
                          }

                          return Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.person,
                                size: 12,
                                color: Colors.grey.shade600,
                              ),
                              const SizedBox(width: 4),
                              Flexible(
                                child: Text(
                                  creator.name,
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.grey.shade600,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),

                // المرفقات
                if (task.attachmentIds != null &&
                    task.attachmentIds!.isNotEmpty)
                  Container(
                    margin: const EdgeInsets.only(left: 8),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.amber.shade50,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: Colors.amber.shade200,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.attach_file,
                          size: 12,
                          color: Colors.amber.shade700,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${task.attachmentIds!.length}',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.amber.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),

                // المحادثات غير المقروءة
                FutureBuilder<int>(
                  future: taskController.getUnreadMessagesCount(task.id),
                  builder: (context, snapshot) {
                    final unreadCount = snapshot.data ?? 0;
                    if (unreadCount == 0) {
                      return const SizedBox.shrink();
                    }

                    return Container(
                      margin: const EdgeInsets.only(left: 8),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: Colors.green.shade200,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.chat,
                            size: 12,
                            color: Colors.green.shade700,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '$unreadCount',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.green.shade700,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          // عمود الحالة
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: AppColors.getTaskStatusColor(task.status.index)
                    .withAlpha(26),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getStatusIcon(task.status),
                    size: 16,
                    color: AppColors.getTaskStatusColor(task.status.index),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      _getStatusText(task.status),
                      style: TextStyle(
                        color: AppColors.getTaskStatusColor(task.status.index),
                        fontSize: 12,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // عمود الأولوية
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: AppColors.getTaskPriorityColor(task.priority.index)
                    .withAlpha(26),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getPriorityIcon(task.priority),
                    size: 16,
                    color: AppColors.getTaskPriorityColor(task.priority.index),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      _getPriorityText(task.priority),
                      style: TextStyle(
                        color:
                            AppColors.getTaskPriorityColor(task.priority.index),
                        fontSize: 12,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // عمود تاريخ الاستحقاق
          Expanded(
            flex: 3,
            child: Text(
              task.dueDate != null
                  ? DateFormatter.formatDate(task.dueDate!)
                  : 'غير محدد',
              style: TextStyle(
                color: task.dueDate != null &&
                        task.dueDate!.isBefore(DateTime.now())
                    ? Colors.red
                    : Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ),

          // عمود المرفقات
          Expanded(
            flex: 1,
            child: Center(
              child:
                  task.attachmentIds != null && task.attachmentIds!.isNotEmpty
                      ? Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.amber.shade50,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                              color: Colors.amber.shade200,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.attach_file,
                                size: 12,
                                color: Colors.amber.shade700,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${task.attachmentIds!.length}',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.amber.shade700,
                                ),
                              ),
                            ],
                          ),
                        )
                      : Text(
                          '0',
                          style: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 12,
                          ),
                        ),
            ),
          ),

          // عمود التقدم
          Expanded(
            flex: 3,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // شريط التقدم
                LinearPercentIndicator(
                  lineHeight: 16, // زيادة ارتفاع الشريط ليناسب النص
                  percent: _getTaskProgress(task),
                  backgroundColor: Colors.grey[200],
                  progressColor: _getProgressColor(_getTaskProgress(task)),
                  barRadius: const Radius.circular(4),
                  padding: const EdgeInsets.symmetric(horizontal: 0),
                  // إزالة trailing لمنع التداخل
                ),
                // نص النسبة المئوية في وسط الشريط
                Text(
                  '${task.completionPercentage.toInt()}%',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        color: Colors.black.withAlpha(128),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );

    // جعل المهمة قابلة للسحب فقط في عرض المجموعات
    if (_isGroupedByStatus && !_isListView) {
      return Draggable<Task>(
        // بيانات المهمة التي سيتم نقلها
        data: task,
        // المهمة أثناء السحب (العنصر المرئي الذي يتحرك مع المؤشر)
        feedback: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.7,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.getTaskStatusColor(task.status.index),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26),
                  blurRadius: 10,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: AppColors.getTaskPriorityColor(task.priority.index),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    task.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // عرض الحالة
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.getTaskStatusColor(task.status.index)
                        .withAlpha(26),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _getStatusText(task.status),
                    style: TextStyle(
                      color: AppColors.getTaskStatusColor(task.status.index),
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(width: 4),
                // عرض الأولوية
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.getTaskPriorityColor(task.priority.index)
                        .withAlpha(26),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _getPriorityText(task.priority),
                    style: TextStyle(
                      color:
                          AppColors.getTaskPriorityColor(task.priority.index),
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(width: 4),
                // عرض المرفقات
                if (task.attachmentIds != null &&
                    task.attachmentIds!.isNotEmpty)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.amber.shade50,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: Colors.amber.shade200,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.attach_file,
                          size: 12,
                          color: Colors.amber.shade700,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${task.attachmentIds!.length}',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: Colors.amber.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
        // المهمة عندما تكون قيد السحب (العنصر الذي يظل في مكانه الأصلي)
        childWhenDragging: Opacity(
          opacity: 0.3,
          child: taskContent,
        ),
        // تأثير الرسوم المتحركة عند بدء السحب
        onDragStarted: () {
          // يمكن إضافة تأثيرات صوتية أو اهتزاز هنا إذا لزم الأمر
          HapticFeedback.lightImpact(); // اهتزاز خفيف عند بدء السحب
        },
        // تأثير الرسوم المتحركة عند انتهاء السحب
        onDragEnd: (details) {
          // يمكن إضافة تأثيرات صوتية أو اهتزاز هنا إذا لزم الأمر
          if (details.wasAccepted) {
            HapticFeedback.mediumImpact(); // اهتزاز متوسط عند قبول الإفلات
          }
        },
        // المهمة في حالتها العادية
        child: DragTarget<TaskPriority>(
          // قبول إفلات الأولوية على المهمة
          onAcceptWithDetails: (details) async {
            final newPriority = details.data;
            // تجنب تحديث الأولوية إذا كانت نفس الأولوية الحالية
            if (task.priority != newPriority) {
              final taskController = Get.find<TaskController>();
              final authController = Get.find<AuthController>();

              // إضافة تأثير اهتزاز عند قبول الإفلات
              HapticFeedback.mediumImpact();

              // عرض مؤشر تحميل
              Get.dialog(
                const Center(
                  child: CircularProgressIndicator(),
                ),
                barrierDismissible: false,
              );

              // تحديث أولوية المهمة
              final result = await taskController.updateTaskPriority(
                task.id,
                authController.currentUser.value!.id,
                newPriority,
                'تم تغيير الأولوية عن طريق السحب والإفلات',
              );

              // إغلاق مؤشر التحميل
              Get.back();

              // إعادة تحميل المهام بعد التحديث مع تأخير قصير
              if (result) {
                // تأخير قصير للتأكد من اكتمال عملية التحديث في قاعدة البيانات
                await Future.delayed(const Duration(milliseconds: 300));
                await _loadTasks();

                // عرض رسالة نجاح
                Get.snackbar(
                  'تم بنجاح',
                  'تم تغيير أولوية المهمة إلى ${_getPriorityText(newPriority)}',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green.shade100,
                  colorText: Colors.green.shade800,
                  duration: const Duration(seconds: 2),
                );
              } else {
                // عرض رسالة خطأ
                Get.snackbar(
                  'خطأ',
                  'فشل تغيير أولوية المهمة',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                  duration: const Duration(seconds: 2),
                );
              }

              // تحديث واجهة المستخدم
              setState(() {});
            }
          },
          builder: (context, candidateData, rejectedData) {
            // تغيير مظهر المهمة عند سحب أولوية فوقها
            final isHovering = candidateData.isNotEmpty;

            return AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: isHovering
                  ? BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: Colors.blue,
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.blue.withAlpha(77),
                          blurRadius: 8,
                          spreadRadius: 1,
                        ),
                      ],
                    )
                  : null,
              child: InkWell(
                onTap: () {
                  foundation.debugPrint(
                      'تم النقر على المهمة: ${task.title} - المعرف: ${task.id}');
                  setState(() {
                    _selectedTaskId = task.id;
                    _showSidePanel = true;
                  });
                  foundation.debugPrint('جاري تحميل تفاصيل المهمة...');
                  taskController.loadTaskDetails(task.id).then((_) {
                    foundation.debugPrint('تم تحميل تفاصيل المهمة بنجاح');
                    if (taskController.error.value.isNotEmpty) {
                      foundation.debugPrint(
                          'حدث خطأ أثناء تحميل تفاصيل المهمة: ${taskController.error.value}');
                      Get.snackbar(
                        'خطأ',
                        'حدث خطأ أثناء تحميل تفاصيل المهمة: ${taskController.error.value}',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.red.shade100,
                        colorText: Colors.red.shade800,
                      );
                    }
                  }).catchError((error) {
                    foundation.debugPrint('حدث خطأ غير متوقع: $error');
                    Get.snackbar(
                      'خطأ',
                      'حدث خطأ غير متوقع: $error',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.red.shade100,
                      colorText: Colors.red.shade800,
                    );
                  });
                },
                child: taskContent,
              ),
            );
          },
        ),
      );
    } else {
      // في حالة عدم تفعيل عرض المجموعات، نعرض المهمة بشكل عادي
      return InkWell(
        onTap: () {
          foundation.debugPrint(
              'تم النقر على المهمة (عرض عادي): ${task.title} - المعرف: ${task.id}');
          setState(() {
            _selectedTaskId = task.id;
            _showSidePanel = true;
          });
          foundation.debugPrint('جاري تحميل تفاصيل المهمة (عرض عادي)...');
          taskController.loadTaskDetails(task.id).then((_) {
            foundation.debugPrint('تم تحميل تفاصيل المهمة بنجاح (عرض عادي)');
            if (taskController.error.value.isNotEmpty) {
              foundation.debugPrint(
                  'حدث خطأ أثناء تحميل تفاصيل المهمة (عرض عادي): ${taskController.error.value}');
              Get.snackbar(
                'خطأ',
                'حدث خطأ أثناء تحميل تفاصيل المهمة: ${taskController.error.value}',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.red.shade100,
                colorText: Colors.red.shade800,
              );
            }
          }).catchError((error) {
            foundation.debugPrint('حدث خطأ غير متوقع (عرض عادي): $error');
            Get.snackbar(
              'خطأ',
              'حدث خطأ غير متوقع: $error',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.red.shade100,
              colorText: Colors.red.shade800,
            );
          });
        },
        child: taskContent,
      );
    }
  }

  /// بناء أولوية قابلة للسحب
  Widget _buildDraggablePriority(TaskPriority priority) {
    // الحصول على لون ونص الأولوية
    Color priorityColor;
    String priorityText;
    IconData priorityIcon;

    switch (priority) {
      case TaskPriority.low:
        priorityColor = Colors.green;
        priorityText = 'منخفضة';
        priorityIcon = Icons.arrow_downward;
        break;
      case TaskPriority.medium:
        priorityColor = Colors.blue;
        priorityText = 'متوسطة';
        priorityIcon = Icons.remove;
        break;
      case TaskPriority.high:
        priorityColor = Colors.orange;
        priorityText = 'عالية';
        priorityIcon = Icons.arrow_upward;
        break;
      case TaskPriority.urgent:
        priorityColor = Colors.red;
        priorityText = 'عاجلة';
        priorityIcon = Icons.priority_high;
        break;
    }

    // إنشاء أولوية قابلة للسحب
    return Draggable<TaskPriority>(
      // بيانات الأولوية التي سيتم نقلها
      data: priority,
      // الأولوية أثناء السحب (العنصر المرئي الذي يتحرك مع المؤشر)
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: priorityColor,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: priorityColor.withAlpha(50),
                blurRadius: 8,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                priorityIcon,
                color: priorityColor,
                size: 20,
              ),
              const SizedBox(width: 4),
              Text(
                priorityText,
                style: TextStyle(
                  color: priorityColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
      // الأولوية عندما تكون قيد السحب (العنصر الذي يظل في مكانه الأصلي)
      childWhenDragging: Opacity(
        opacity: 0.3,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: priorityColor.withAlpha(20),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: priorityColor,
              width: 1,
            ),
          ),
          child: Icon(
            priorityIcon,
            color: priorityColor,
            size: 20,
          ),
        ),
      ),
      // تأثير الرسوم المتحركة عند بدء السحب
      onDragStarted: () {
        // يمكن إضافة تأثيرات صوتية أو اهتزاز هنا إذا لزم الأمر
        HapticFeedback.lightImpact(); // اهتزاز خفيف عند بدء السحب
      },
      // الأولوية في حالتها العادية
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: priorityColor.withAlpha(20),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: priorityColor,
            width: 1,
          ),
        ),
        child: Icon(
          priorityIcon,
          color: priorityColor,
          size: 20,
        ),
      ),
    );
  }

  /// بناء منطقة إفلات للأولويات
  Widget _buildPriorityDropTarget(TaskPriority priority) {
    // الحصول على لون ونص الأولوية
    Color priorityColor;
    String priorityText;
    IconData priorityIcon;

    switch (priority) {
      case TaskPriority.low:
        priorityColor = Colors.green;
        priorityText = 'منخفضة';
        priorityIcon = Icons.arrow_downward;
        break;
      case TaskPriority.medium:
        priorityColor = Colors.blue;
        priorityText = 'متوسطة';
        priorityIcon = Icons.remove;
        break;
      case TaskPriority.high:
        priorityColor = Colors.orange;
        priorityText = 'عالية';
        priorityIcon = Icons.arrow_upward;
        break;
      case TaskPriority.urgent:
        priorityColor = Colors.red;
        priorityText = 'عاجلة';
        priorityIcon = Icons.priority_high;
        break;
    }

    // إنشاء منطقة إفلات للأولوية
    return DragTarget<Task>(
      builder: (context, candidateData, rejectedData) {
        // تغيير المظهر عند سحب مهمة فوق منطقة الإفلات
        final isHovering = candidateData.isNotEmpty;

        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: isHovering
                ? priorityColor.withAlpha(50)
                : priorityColor.withAlpha(20),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: priorityColor,
              width: isHovering ? 2 : 1,
            ),
            boxShadow: isHovering
                ? [
                    BoxShadow(
                      color: priorityColor.withAlpha(50),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ]
                : null,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                priorityIcon,
                color: priorityColor,
                size: 20,
              ),
              const SizedBox(height: 4),
              Text(
                priorityText,
                style: TextStyle(
                  color: priorityColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        );
      },
      // قبول المهمة المسحوبة وتغيير أولويتها
      onAcceptWithDetails: (DragTargetDetails<Task> details) async {
        final task = details.data;
        // تجنب تحديث الأولوية إذا كانت نفس الأولوية الحالية
        if (task.priority != priority) {
          final taskController = Get.find<TaskController>();
          final authController = Get.find<AuthController>();

          // إضافة تأثير اهتزاز عند قبول الإفلات
          HapticFeedback.mediumImpact();

          // عرض مؤشر تحميل
          Get.dialog(
            const Center(
              child: CircularProgressIndicator(),
            ),
            barrierDismissible: false,
          );

          // تحديث أولوية المهمة
          final result = await taskController.updateTaskPriority(
            task.id,
            authController.currentUser.value!.id,
            priority,
            'تم تغيير الأولوية عن طريق السحب والإفلات',
          );

          // إغلاق مؤشر التحميل
          Get.back();

          // إعادة تحميل المهام بعد التحديث مع تأخير قصير
          if (result) {
            // تأخير قصير للتأكد من اكتمال عملية التحديث في قاعدة البيانات
            await Future.delayed(const Duration(milliseconds: 300));
            await _loadTasks();

            // عرض رسالة نجاح
            Get.snackbar(
              'تم بنجاح',
              'تم تغيير أولوية المهمة إلى ${_getPriorityText(priority)}',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.green.shade100,
              colorText: Colors.green.shade800,
              duration: const Duration(seconds: 2),
            );
          } else {
            // عرض رسالة خطأ
            Get.snackbar(
              'خطأ',
              'فشل تغيير أولوية المهمة',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: Colors.red.shade100,
              colorText: Colors.red.shade800,
              duration: const Duration(seconds: 2),
            );
          }

          // تحديث واجهة المستخدم
          setState(() {});
        }
      },
    );
  }

  /// بناء لوحة السحب والإفلات
  Widget _buildDragDropBoard(List<Task> tasks) {
    final taskController = Get.find<TaskController>();
    final authController = Get.find<AuthController>();

    // تصنيف المهام حسب الحالة
    final pendingTasks =
        tasks.where((task) => task.status == TaskStatus.pending).toList();
    final inProgressTasks =
        tasks.where((task) => task.status == TaskStatus.inProgress).toList();
    final waitingTasks = tasks
        .where((task) => task.status == TaskStatus.waitingForInfo)
        .toList();
    final completedTasks =
        tasks.where((task) => task.status == TaskStatus.completed).toList();

    return SimpleDragDropBoard(
      pendingTasks: pendingTasks,
      inProgressTasks: inProgressTasks,
      waitingTasks: waitingTasks,
      completedTasks: completedTasks,
      onTaskStatusChanged: (task, newStatus) async {
        // تحديث حالة المهمة
        final result = await taskController.updateTaskStatus(
          task.id,
          authController.currentUser.value!.id,
          newStatus,
          'تم تغيير الحالة عن طريق سحب المهمة',
        );

        // إعادة تحميل المهام بعد التحديث
        if (result) {
          await _loadTasks();
        }
      },
      onTaskTap: (task) {
        // عرض تفاصيل المهمة
        setState(() {
          _selectedTaskId = task.id;
          _showSidePanel = true;
        });
        taskController.loadTaskDetails(task.id);
      },
      onAddTask: () {
        // إضافة مهمة جديدة
        Get.to(() => const CreateTaskScreen())?.then((_) => _loadTasks());
      },
      onTaskReordered: (status, reorderedTasks) async {
        // تحديث ترتيب المهام في الكنترولر
        // هذه الوظيفة تحتاج إلى إضافتها في TaskController
        try {
          // تأثير اهتزاز عند إعادة الترتيب
          HapticFeedback.mediumImpact();

          // عرض رسالة للمستخدم
          Get.snackbar(
            'تم إعادة الترتيب',
            'تم إعادة ترتيب المهام بنجاح',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green.shade100,
            colorText: Colors.green.shade800,
            duration: const Duration(seconds: 2),
          );

          // إعادة تحميل المهام بعد التحديث
          await _loadTasks();
        } catch (e) {
          // عرض رسالة خطأ للمستخدم
          Get.snackbar(
            'خطأ',
            'حدث خطأ أثناء إعادة ترتيب المهام: $e',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
          );
        }
      },
    );
  }
}
 // لانحتاج لها 
/// إصلاح قاعدة البيانات
//   Future<void> _repairDatabase() async {
//     // عرض مؤشر التقدم
//     final GlobalKey<State> dialogKey = GlobalKey<State>();

//     if (!mounted) return;

//     showDialog(
//       context: context,
//       barrierDismissible: false,
//       builder: (BuildContext ctx) {
//         return AlertDialog(
//           key: dialogKey,
//           content: const Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               CircularProgressIndicator(),
//               SizedBox(height: 16),
//               Text('جاري إصلاح قاعدة البيانات...'),
//             ],
//           ),
//         );
//       },
//     );

//     try {
//       // إصلاح قاعدة البيانات
//       final databaseRepair = DatabaseRepair();
//       final success = await databaseRepair.repairDatabase();

//       // التحقق من أن الـ widget لا يزال موجودًا
//       if (!mounted) return;

//       // إغلاق مؤشر التقدم
//       if (Navigator.canPop(context)) {
//         Navigator.of(context).pop();
//       }

//       // عرض نتيجة الإصلاح
//       if (!mounted) return;

//       final bool? result = await showDialog<bool>(
//         context: context,
//         builder: (BuildContext ctx) {
//           return AlertDialog(
//             title: Text(success ? 'تم الإصلاح بنجاح' : 'فشل الإصلاح'),
//             content: Text(success
//                 ? 'تم إصلاح قاعدة البيانات بنجاح. سيتم إعادة تحميل المهام.'
//                 : 'فشل في إصلاح قاعدة البيانات. يرجى الاتصال بالدعم الفني.'),
//             actions: <Widget>[
//               TextButton(
//                 child: const Text('موافق'),
//                 onPressed: () {
//                   Navigator.of(ctx).pop(true);
//                 },
//               ),
//             ],
//           );
//         },
//       );

//       if (mounted && result == true && success) {
//         // إعادة تحميل المهام بعد الإصلاح
//         _loadTasks();
//       }
//     } catch (e) {
//       // التحقق من أن الـ widget لا يزال موجودًا
//       if (!mounted) return;

//       // إغلاق مؤشر التقدم
//       if (Navigator.canPop(context)) {
//         Navigator.of(context).pop();
//       }

//       // عرض رسالة الخطأ
//       if (!mounted) return;

//       await showDialog(
//         context: context,
//         builder: (BuildContext ctx) {
//           return AlertDialog(
//             title: const Text('خطأ'),
//             content: Text('حدث خطأ أثناء إصلاح قاعدة البيانات: $e'),
//             actions: <Widget>[
//               TextButton(
//                 child: const Text('موافق'),
//                 onPressed: () {
//                   Navigator.of(ctx).pop();
//                 },
//               ),
//             ],
//           );
//         },
//       );
//     }
//   }
// }
