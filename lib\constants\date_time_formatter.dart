import 'package:intl/intl.dart';

/// أداة تنسيق التاريخ والوقت
///
/// توفر هذه الفئة مجموعة من الدوال المساعدة لتنسيق التاريخ والوقت
class DateTimeFormatter {
  /// تنسيق التاريخ بالصيغة القصيرة (مثل 01/01/2023)
  ///
  /// [dateTime] التاريخ المراد تنسيقه
  static String formatShortDate(DateTime dateTime) {
    final formatter = DateFormat('dd/MM/yyyy');
    return formatter.format(dateTime);
  }

  /// تنسيق التاريخ بالصيغة الطويلة (مثل 1 يناير 2023)
  ///
  /// [dateTime] التاريخ المراد تنسيقه
  static String formatLongDate(DateTime dateTime) {
    final formatter = DateFormat('d MMMM yyyy');
    return formatter.format(dateTime);
  }

  /// تنسيق الوقت (مثل 12:30 م)
  ///
  /// [dateTime] الوقت المراد تنسيقه
  static String formatTime(DateTime dateTime) {
    final formatter = DateFormat('h:mm a');
    return formatter.format(dateTime);
  }

  /// تنسيق التاريخ والوقت (مثل 01/01/2023 12:30 م)
  ///
  /// [dateTime] التاريخ والوقت المراد تنسيقهما
  static String formatDateTime(DateTime dateTime) {
    final formatter = DateFormat('dd/MM/yyyy h:mm a');
    return formatter.format(dateTime);
  }

  /// تنسيق التاريخ والوقت بصيغة طويلة (مثل 1 يناير 2023 12:30 م)
  ///
  /// [dateTime] التاريخ والوقت المراد تنسيقهما
  static String formatLongDateTime(DateTime dateTime) {
    final formatter = DateFormat('d MMMM yyyy h:mm a');
    return formatter.format(dateTime);
  }

  /// تنسيق الوقت النسبي (مثل منذ 5 دقائق، منذ ساعة، إلخ)
  ///
  /// [dateTime] التاريخ المراد تنسيقه
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return 'منذ ${difference.inSeconds} ثانية';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 30) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months شهر';
    } else {
      final years = (difference.inDays / 365).floor();
      return 'منذ $years سنة';
    }
  }

  /// تنسيق التاريخ بصيغة اليوم (اليوم، أمس، إلخ)
  ///
  /// [dateTime] التاريخ المراد تنسيقه
  static String formatDay(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final tomorrow = today.add(const Duration(days: 1));
    final date = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (date == today) {
      return 'اليوم';
    } else if (date == yesterday) {
      return 'أمس';
    } else if (date == tomorrow) {
      return 'غدًا';
    } else {
      return formatShortDate(dateTime);
    }
  }

  /// تنسيق المدة الزمنية (مثل 1 ساعة و 30 دقيقة)
  ///
  /// [duration] المدة الزمنية المراد تنسيقها
  static String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hours ساعة ${minutes > 0 ? 'و $minutes دقيقة' : ''}';
    } else if (minutes > 0) {
      return '$minutes دقيقة ${seconds > 0 ? 'و $seconds ثانية' : ''}';
    } else {
      return '$seconds ثانية';
    }
  }

  /// تنسيق التاريخ والوقت للعرض في المحادثات
  ///
  /// [dateTime] التاريخ والوقت المراد تنسيقهما
  static String formatChatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final date = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (date == today) {
      return formatTime(dateTime);
    } else if (date == yesterday) {
      return 'أمس ${formatTime(dateTime)}';
    } else if (now.difference(dateTime).inDays < 7) {
      final formatter = DateFormat('EEEE');
      return '${formatter.format(dateTime)} ${formatTime(dateTime)}';
    } else {
      return formatDateTime(dateTime);
    }
  }
}
