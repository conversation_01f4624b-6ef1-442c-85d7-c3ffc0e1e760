import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/admin_controller.dart';
import '../../controllers/theme_controller.dart';


/// تبويب إعدادات النظام
///
/// يوفر واجهة لإدارة إعدادات النظام العامة
class SystemSettingsTab extends StatefulWidget {
  const SystemSettingsTab({super.key});

  @override
  State<SystemSettingsTab> createState() => _SystemSettingsTabState();
}

class _SystemSettingsTabState extends State<SystemSettingsTab> {
  final AdminController _adminController = Get.find<AdminController>();
  final ThemeController _themeController = Get.find<ThemeController>();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const Si<PERSON><PERSON><PERSON>(height: 16),
          Expanded(
            child: _buildSettingsList(),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الصفحة
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'إعدادات النظام',
          style: AppStyles.titleLarge,
        ),
        ElevatedButton.icon(
          onPressed: _resetToDefaultSettings,
          icon: const Icon(Icons.restore),
          label: const Text('استعادة الإعدادات الافتراضية'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.accent,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  /// بناء قائمة الإعدادات
  Widget _buildSettingsList() {
    return Obx(() {
      if (_adminController.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSettingsSection(
              'الإعدادات العامة',
              [
                _buildAppNameSetting(),
                _buildLanguageSetting(),
                _buildThemeSetting(),
              ],
            ),
            const SizedBox(height: 24),
            _buildSettingsSection(
              'إعدادات النسخ الاحتياطي',
              [
                _buildAutoBackupSetting(),
                _buildBackupFrequencySetting(),
                _buildMaxBackupCountSetting(),
              ],
            ),
            const SizedBox(height: 24),
            _buildSettingsSection(
              'إعدادات الإشعارات',
              [
                _buildNotificationsSetting(),
              ],
            ),
            const SizedBox(height: 24),
            _buildSettingsSection(
              'إعدادات الأمان',
              [
                _buildActivityLogSetting(),
              ],
            ),
            const SizedBox(height: 24),
            _buildSettingsSection(
              'إعدادات متقدمة',
              [
                _buildResetPermissionsSetting(),
                _buildSyncSettingsLink(),
                _buildDatabaseRepairLink(),
                _buildArchiveSystemRepairLink(),
              ],
            ),
          ],
        ),
      );
    });
  }

  /// بناء قسم إعدادات
  Widget _buildSettingsSection(String title, List<Widget> settings) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppStyles.titleMedium.copyWith(
            color: AppColors.primary,
          ),
        ),
        const Divider(),
        ...settings,
      ],
    );
  }

  /// بناء إعداد اسم التطبيق
  Widget _buildAppNameSetting() {
    final appNameController = TextEditingController();

    // الحصول على قيمة الإعداد الحالية
    _adminController.getSettingValue('app_name', defaultValue: 'نظام إدارة المهام').then((value) {
      appNameController.text = value ?? 'نظام إدارة المهام';
    });

    return ListTile(
      title: const Text('اسم التطبيق'),
      subtitle: const Text('الاسم الذي سيظهر في التطبيق'),
      trailing: SizedBox(
        width: 200,
        child: TextField(
          controller: appNameController,
          decoration: const InputDecoration(
            hintText: 'أدخل اسم التطبيق',
          ),
          onSubmitted: (value) {
            if (value.isNotEmpty) {
              _adminController.saveSetting(
                'app_name',
                value,
                description: 'اسم التطبيق',
              );
            }
          },
        ),
      ),
      onTap: () {
        // عرض حوار لتعديل الإعداد
        _showEditSettingDialog(
          'اسم التطبيق',
          'أدخل اسم التطبيق الجديد',
          appNameController.text,
          (value) {
            _adminController.saveSetting(
              'app_name',
              value,
              description: 'اسم التطبيق',
            );
          },
        );
      },
    );
  }

  /// بناء إعداد اللغة
  Widget _buildLanguageSetting() {
    return ListTile(
      title: const Text('اللغة الافتراضية'),
      subtitle: const Text('اللغة الافتراضية للتطبيق'),
      trailing: const Text(
        'العربية',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.green,
        ),
      ),
      onTap: () {
        // عرض رسالة توضيحية
        Get.snackbar(
          'اللغة العربية فقط',
          'التطبيق يدعم اللغة العربية فقط مع دعم كامل للاتجاه من اليمين إلى اليسار',
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 3),
        );
      },
    );
  }

  /// بناء إعداد السمة
  Widget _buildThemeSetting() {
    return ListTile(
      title: const Text('السمة الافتراضية'),
      subtitle: const Text('السمة الافتراضية للتطبيق'),
      trailing: Switch(
        value: _themeController.isDarkMode.value,
        onChanged: (value) {
          // تغيير السمة
          _themeController.toggleTheme();

          // حفظ الإعداد
          _adminController.saveSetting(
            'enable_dark_mode',
            value.toString(),
            description: 'تفعيل الوضع الليلي',
          );
        },
        activeColor: AppColors.primary,
      ),
    );
  }

  /// بناء إعداد النسخ الاحتياطي التلقائي
  Widget _buildAutoBackupSetting() {
    final RxBool isEnabled = true.obs;

    // الحصول على قيمة الإعداد الحالية
    _adminController.getSettingValue('auto_backup_enabled', defaultValue: 'true').then((value) {
      isEnabled.value = value?.toLowerCase() == 'true';
    });

    return Obx(() => ListTile(
      title: const Text('النسخ الاحتياطي التلقائي'),
      subtitle: const Text('تفعيل النسخ الاحتياطي التلقائي للبيانات'),
      trailing: Switch(
        value: isEnabled.value,
        onChanged: (value) {
          isEnabled.value = value;

          // حفظ الإعداد
          _adminController.saveSetting(
            'auto_backup_enabled',
            value.toString(),
            description: 'تفعيل النسخ الاحتياطي التلقائي',
          );
        },
        activeColor: AppColors.primary,
      ),
    ));
  }

  /// بناء إعداد تكرار النسخ الاحتياطي
  Widget _buildBackupFrequencySetting() {
    final frequencyController = TextEditingController();

    // الحصول على قيمة الإعداد الحالية
    _adminController.getSettingValue('auto_backup_frequency_days', defaultValue: '7').then((value) {
      frequencyController.text = value ?? '7';
    });

    return ListTile(
      title: const Text('تكرار النسخ الاحتياطي'),
      subtitle: const Text('عدد الأيام بين النسخ الاحتياطية التلقائية'),
      trailing: SizedBox(
        width: 100,
        child: TextField(
          controller: frequencyController,
          decoration: const InputDecoration(
            hintText: 'عدد الأيام',
            suffixText: 'يوم',
          ),
          keyboardType: TextInputType.number,
          onSubmitted: (value) {
            if (value.isNotEmpty) {
              final days = int.tryParse(value);
              if (days != null && days > 0) {
                _adminController.saveSetting(
                  'auto_backup_frequency_days',
                  days.toString(),
                  description: 'عدد أيام تكرار النسخ الاحتياطي التلقائي',
                );
              }
            }
          },
        ),
      ),
      onTap: () {
        // عرض حوار لتعديل الإعداد
        _showEditSettingDialog(
          'تكرار النسخ الاحتياطي',
          'أدخل عدد الأيام بين النسخ الاحتياطية التلقائية',
          frequencyController.text,
          (value) {
            final days = int.tryParse(value);
            if (days != null && days > 0) {
              _adminController.saveSetting(
                'auto_backup_frequency_days',
                days.toString(),
                description: 'عدد أيام تكرار النسخ الاحتياطي التلقائي',
              );
            }
          },
        );
      },
    );
  }

  /// بناء إعداد الحد الأقصى لعدد النسخ الاحتياطية
  Widget _buildMaxBackupCountSetting() {
    final countController = TextEditingController();

    // الحصول على قيمة الإعداد الحالية
    _adminController.getSettingValue('max_backup_count', defaultValue: '10').then((value) {
      countController.text = value ?? '10';
    });

    return ListTile(
      title: const Text('الحد الأقصى لعدد النسخ الاحتياطية'),
      subtitle: const Text('الحد الأقصى لعدد النسخ الاحتياطية المحفوظة'),
      trailing: SizedBox(
        width: 100,
        child: TextField(
          controller: countController,
          decoration: const InputDecoration(
            hintText: 'العدد',
          ),
          keyboardType: TextInputType.number,
          onSubmitted: (value) {
            if (value.isNotEmpty) {
              final count = int.tryParse(value);
              if (count != null && count > 0) {
                _adminController.saveSetting(
                  'max_backup_count',
                  count.toString(),
                  description: 'الحد الأقصى لعدد النسخ الاحتياطية',
                );
              }
            }
          },
        ),
      ),
      onTap: () {
        // عرض حوار لتعديل الإعداد
        _showEditSettingDialog(
          'الحد الأقصى لعدد النسخ الاحتياطية',
          'أدخل الحد الأقصى لعدد النسخ الاحتياطية المحفوظة',
          countController.text,
          (value) {
            final count = int.tryParse(value);
            if (count != null && count > 0) {
              _adminController.saveSetting(
                'max_backup_count',
                count.toString(),
                description: 'الحد الأقصى لعدد النسخ الاحتياطية',
              );
            }
          },
        );
      },
    );
  }

  /// بناء إعداد الإشعارات
  Widget _buildNotificationsSetting() {
    final RxBool isEnabled = true.obs;

    // الحصول على قيمة الإعداد الحالية
    _adminController.getSettingValue('enable_notifications', defaultValue: 'true').then((value) {
      isEnabled.value = value?.toLowerCase() == 'true';
    });

    return Obx(() => ListTile(
      title: const Text('الإشعارات'),
      subtitle: const Text('تفعيل الإشعارات في التطبيق'),
      trailing: Switch(
        value: isEnabled.value,
        onChanged: (value) {
          isEnabled.value = value;

          // حفظ الإعداد
          _adminController.saveSetting(
            'enable_notifications',
            value.toString(),
            description: 'تفعيل الإشعارات',
          );
        },
        activeColor: AppColors.primary,
      ),
    ));
  }

  /// بناء إعداد سجل النشاط
  Widget _buildActivityLogSetting() {
    final RxBool isEnabled = true.obs;

    // الحصول على قيمة الإعداد الحالية
    _adminController.getSettingValue('enable_activity_log', defaultValue: 'true').then((value) {
      isEnabled.value = value?.toLowerCase() == 'true';
    });

    return Obx(() => ListTile(
      title: const Text('سجل النشاط'),
      subtitle: const Text('تفعيل تسجيل نشاط المستخدمين'),
      trailing: Switch(
        value: isEnabled.value,
        onChanged: (value) {
          isEnabled.value = value;

          // حفظ الإعداد
          _adminController.saveSetting(
            'enable_activity_log',
            value.toString(),
            description: 'تفعيل سجل النشاط',
          );
        },
        activeColor: AppColors.primary,
      ),
    ));
  }

  /// بناء إعداد إعادة تعيين الصلاحيات
  Widget _buildResetPermissionsSetting() {
    return ListTile(
      title: const Text('إعادة تعيين الصلاحيات'),
      subtitle: const Text('إعادة تعيين صلاحيات الأدوار إلى الإعدادات الافتراضية'),
      trailing: ElevatedButton(
        onPressed: _resetPermissions,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.orange,
          foregroundColor: Colors.white,
        ),
        child: const Text('إعادة تعيين'),
      ),
    );
  }

  /// بناء رابط إلى إعدادات التزامن
  Widget _buildSyncSettingsLink() {
    return ListTile(
      title: const Text('إعدادات التزامن'),
      subtitle: const Text('إدارة إعدادات التزامن والمزامنة بين المستخدمين'),
      trailing: const Icon(Icons.arrow_forward_ios),
      leading: const Icon(Icons.sync, color: AppColors.primary),
      onTap: () {
        // الانتقال إلى تبويب إعدادات التزامن
        try {
          final tabController = Get.find<TabController>(tag: 'admin_tab_controller');
          tabController.animateTo(6); // تبويب إعدادات التزامن هو السابع (index 6)
        } catch (e) {
          debugPrint('خطأ في الانتقال إلى تبويب إعدادات التزامن: $e');
          // في حالة الخطأ، استخدم المسار المباشر
          Get.toNamed('/settings/sync');
        }
      },
    );
  }

  /// بناء رابط إلى إصلاح قاعدة البيانات
  Widget _buildDatabaseRepairLink() {
    return ListTile(
      title: const Text('إصلاح قاعدة البيانات'),
      subtitle: const Text('إصلاح مشاكل قاعدة البيانات مثل الجداول المفقودة'),
      trailing: const Icon(Icons.arrow_forward_ios),
      leading: const Icon(Icons.build_circle, color: Colors.orange),
      onTap: () {
        // الانتقال إلى شاشة إصلاح قاعدة البيانات
        Get.toNamed('/settings/database-repair');
      },
    );
  }

  /// بناء رابط إلى إصلاح نظام الأرشفة الإلكتروني
  Widget _buildArchiveSystemRepairLink() {
    return ListTile(
      title: const Text('إصلاح نظام الأرشفة'),
      subtitle: const Text('إصلاح مشاكل نظام الأرشفة الإلكتروني'),
      trailing: const Icon(Icons.arrow_forward_ios),
      leading: const Icon(Icons.archive_outlined, color: Colors.green),
      onTap: () {
        // الانتقال إلى شاشة إصلاح نظام الأرشفة الإلكتروني
        Get.toNamed('/settings/archive-system-repair');
      },
    );
  }

  /// عرض حوار تعديل إعداد
  void _showEditSettingDialog(String title, String hint, String initialValue, Function(String) onSave) {
    final controller = TextEditingController(text: initialValue);

    Get.dialog(
      AlertDialog(
        title: Text(title),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: hint,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                onSave(controller.text);
                Get.back();
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// إعادة تعيين الصلاحيات
  void _resetPermissions() {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد إعادة تعيين الصلاحيات'),
        content: const Text(
          'هل أنت متأكد من إعادة تعيين صلاحيات الأدوار إلى الإعدادات الافتراضية؟ سيتم إعادة تعيين جميع الصلاحيات المخصصة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            onPressed: () async {
              Get.back();

              await _adminController.createDefaultRolePermissions();

              Get.snackbar(
                'تم بنجاح',
                'تم إعادة تعيين الصلاحيات بنجاح',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.green,
                colorText: Colors.white,
              );
            },
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  /// إعادة تعيين الإعدادات الافتراضية
  void _resetToDefaultSettings() {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد استعادة الإعدادات الافتراضية'),
        content: const Text(
          'هل أنت متأكد من استعادة جميع الإعدادات إلى القيم الافتراضية؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            onPressed: () async {
              Get.back();

              await _adminController.createDefaultSettings();

              Get.snackbar(
                'تم بنجاح',
                'تم استعادة الإعدادات الافتراضية بنجاح',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: Colors.green,
                colorText: Colors.white,
              );
            },
            child: const Text('استعادة'),
          ),
        ],
      ),
    );
  }
}
