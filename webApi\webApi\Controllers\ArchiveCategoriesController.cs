using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة فئات الأرشيف
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class ArchiveCategoriesController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public ArchiveCategoriesController(TasksDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// الحصول على جميع فئات الأرشيف
        /// </summary>
        /// <returns>قائمة بجميع فئات الأرشيف</returns>
        /// <response code="200">إرجاع قائمة فئات الأرشيف</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveCategory>>> GetArchiveCategories()
        {
            return await _context.ArchiveCategories
                .Include(ac => ac.CreatedByNavigation)
                .Include(ac => ac.Parent)
                .Include(ac => ac.InverseParent)
                .Where(ac => !ac.IsDeleted)
                .OrderBy(ac => ac.Name)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على فئة أرشيف محددة
        /// </summary>
        /// <param name="id">معرف فئة الأرشيف</param>
        /// <returns>فئة الأرشيف المطلوبة</returns>
        /// <response code="200">إرجاع فئة الأرشيف</response>
        /// <response code="404">فئة الأرشيف غير موجودة</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<ArchiveCategory>> GetArchiveCategory(int id)
        {
            var archiveCategory = await _context.ArchiveCategories
                .Include(ac => ac.CreatedByNavigation)
                .Include(ac => ac.Parent)
                .Include(ac => ac.InverseParent)
                .Include(ac => ac.ArchiveDocuments)
                .FirstOrDefaultAsync(ac => ac.Id == id && !ac.IsDeleted);

            if (archiveCategory == null)
            {
                return NotFound();
            }

            return archiveCategory;
        }

        /// <summary>
        /// الحصول على الفئات الرئيسية (بدون فئة أب)
        /// </summary>
        /// <returns>قائمة الفئات الرئيسية</returns>
        /// <response code="200">إرجاع قائمة الفئات الرئيسية</response>
        [HttpGet("root")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveCategory>>> GetRootCategories()
        {
            return await _context.ArchiveCategories
                .Include(ac => ac.CreatedByNavigation)
                .Include(ac => ac.InverseParent)
                .Where(ac => ac.ParentId == null && !ac.IsDeleted)
                .OrderBy(ac => ac.Name)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على الفئات الفرعية لفئة محددة
        /// </summary>
        /// <param name="parentId">معرف الفئة الأب</param>
        /// <returns>قائمة الفئات الفرعية</returns>
        /// <response code="200">إرجاع قائمة الفئات الفرعية</response>
        [HttpGet("parent/{parentId}/children")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveCategory>>> GetChildCategories(int parentId)
        {
            return await _context.ArchiveCategories
                .Include(ac => ac.CreatedByNavigation)
                .Where(ac => ac.ParentId == parentId && !ac.IsDeleted)
                .OrderBy(ac => ac.Name)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على شجرة الفئات الهرمية
        /// </summary>
        /// <returns>شجرة الفئات</returns>
        /// <response code="200">إرجاع شجرة الفئات</response>
        [HttpGet("tree")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<object>>> GetCategoriesTree()
        {
            var allCategories = await _context.ArchiveCategories
                .Include(ac => ac.CreatedByNavigation)
                .Where(ac => !ac.IsDeleted)
                .OrderBy(ac => ac.Name)
                .ToListAsync();

            var rootCategories = allCategories.Where(ac => ac.ParentId == null).ToList();

            var tree = rootCategories.Select(root => BuildCategoryTree(root, allCategories)).ToList();

            return Ok(tree);
        }

        private object BuildCategoryTree(ArchiveCategory category, List<ArchiveCategory> allCategories)
        {
            var children = allCategories.Where(ac => ac.ParentId == category.Id).ToList();

            return new
            {
                category.Id,
                category.Name,
                category.Description,
                category.Color,
                category.Icon,
                category.IsActive,
                category.CreatedAt,
                CreatedBy = category.CreatedByNavigation?.Name,
                Children = children.Select(child => BuildCategoryTree(child, allCategories)).ToList()
            };
        }

        /// <summary>
        /// البحث في فئات الأرشيف
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة فئات الأرشيف المطابقة</returns>
        /// <response code="200">إرجاع قائمة فئات الأرشيف المطابقة</response>
        [HttpGet("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveCategory>>> SearchCategories([FromQuery] string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await GetArchiveCategories();
            }

            return await _context.ArchiveCategories
                .Include(ac => ac.CreatedByNavigation)
                .Include(ac => ac.Parent)
                .Where(ac => !ac.IsDeleted &&
                           (ac.Name.Contains(searchTerm) ||
                            (ac.Description != null && ac.Description.Contains(searchTerm))))
                .OrderBy(ac => ac.Name)
                .ToListAsync();
        }

        /// <summary>
        /// عدد الوثائق في فئة محددة
        /// </summary>
        /// <param name="id">معرف فئة الأرشيف</param>
        /// <returns>عدد الوثائق</returns>
        /// <response code="200">إرجاع عدد الوثائق</response>
        [HttpGet("{id}/documents/count")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<int>> GetDocumentsCount(int id)
        {
            var count = await _context.ArchiveDocuments
                .CountAsync(ad => ad.CategoryId == id && !ad.IsDeleted);

            return count;
        }

        /// <summary>
        /// إنشاء فئة أرشيف جديدة
        /// </summary>
        /// <param name="archiveCategory">بيانات فئة الأرشيف</param>
        /// <returns>فئة الأرشيف المُنشأة</returns>
        /// <response code="201">تم إنشاء فئة الأرشيف بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ArchiveCategory>> PostArchiveCategory(ArchiveCategory archiveCategory)
        {
            // التحقق من عدم وجود فئة بنفس الاسم في نفس المستوى
            var existingCategory = await _context.ArchiveCategories
                .FirstOrDefaultAsync(ac => ac.Name.ToLower() == archiveCategory.Name.ToLower() &&
                                          ac.ParentId == archiveCategory.ParentId &&
                                          !ac.IsDeleted);

            if (existingCategory != null)
            {
                return BadRequest("يوجد فئة بهذا الاسم في نفس المستوى مسبقاً");
            }

            // التحقق من وجود الفئة الأب إذا تم تحديدها
            if (archiveCategory.ParentId.HasValue)
            {
                var parentExists = await _context.ArchiveCategories
                    .AnyAsync(ac => ac.Id == archiveCategory.ParentId.Value && !ac.IsDeleted);

                if (!parentExists)
                {
                    return BadRequest("الفئة الأب غير موجودة");
                }
            }

            archiveCategory.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            archiveCategory.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            archiveCategory.IsDeleted = false;

            _context.ArchiveCategories.Add(archiveCategory);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetArchiveCategory", new { id = archiveCategory.Id }, archiveCategory);
        }

        /// <summary>
        /// تحديث فئة أرشيف
        /// </summary>
        /// <param name="id">معرف فئة الأرشيف</param>
        /// <param name="archiveCategory">بيانات فئة الأرشيف المحدثة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث فئة الأرشيف بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">فئة الأرشيف غير موجودة</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutArchiveCategory(int id, ArchiveCategory archiveCategory)
        {
            if (id != archiveCategory.Id)
            {
                return BadRequest();
            }

            // التحقق من عدم وجود فئة أخرى بنفس الاسم في نفس المستوى
            var existingCategory = await _context.ArchiveCategories
                .FirstOrDefaultAsync(ac => ac.Name.ToLower() == archiveCategory.Name.ToLower() &&
                                          ac.ParentId == archiveCategory.ParentId &&
                                          ac.Id != id &&
                                          !ac.IsDeleted);

            if (existingCategory != null)
            {
                return BadRequest("يوجد فئة أخرى بهذا الاسم في نفس المستوى مسبقاً");
            }

            archiveCategory.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            _context.Entry(archiveCategory).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ArchiveCategoryExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// تفعيل/إلغاء تفعيل فئة أرشيف
        /// </summary>
        /// <param name="id">معرف فئة الأرشيف</param>
        /// <param name="active">حالة التفعيل</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث حالة الفئة بنجاح</response>
        /// <response code="404">فئة الأرشيف غير موجودة</response>
        [HttpPatch("{id}/toggle-active")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ToggleActive(int id, [FromQuery] bool active)
        {
            var archiveCategory = await _context.ArchiveCategories.FindAsync(id);
            if (archiveCategory == null || archiveCategory.IsDeleted)
            {
                return NotFound();
            }

            archiveCategory.IsActive = active;
            archiveCategory.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// حذف فئة أرشيف (حذف منطقي)
        /// </summary>
        /// <param name="id">معرف فئة الأرشيف</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف فئة الأرشيف بنجاح</response>
        /// <response code="400">لا يمكن حذف الفئة لوجود وثائق أو فئات فرعية</response>
        /// <response code="404">فئة الأرشيف غير موجودة</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteArchiveCategory(int id)
        {
            var archiveCategory = await _context.ArchiveCategories.FindAsync(id);
            if (archiveCategory == null || archiveCategory.IsDeleted)
            {
                return NotFound();
            }

            // التحقق من عدم وجود وثائق في الفئة
            var hasDocuments = await _context.ArchiveDocuments
                .AnyAsync(ad => ad.CategoryId == id && !ad.IsDeleted);

            if (hasDocuments)
            {
                return BadRequest("لا يمكن حذف الفئة لوجود وثائق مرتبطة بها");
            }

            // التحقق من عدم وجود فئات فرعية
            var hasSubCategories = await _context.ArchiveCategories
                .AnyAsync(ac => ac.ParentId == id && !ac.IsDeleted);

            if (hasSubCategories)
            {
                return BadRequest("لا يمكن حذف الفئة لوجود فئات فرعية");
            }

            // حذف منطقي
            archiveCategory.IsDeleted = true;
            archiveCategory.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// نقل فئة إلى فئة أب أخرى
        /// </summary>
        /// <param name="id">معرف الفئة المراد نقلها</param>
        /// <param name="newParentId">معرف الفئة الأب الجديدة (null للجذر)</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم نقل الفئة بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">الفئة غير موجودة</response>
        [HttpPatch("{id}/move")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> MoveCategory(int id, [FromQuery] int? newParentId)
        {
            var category = await _context.ArchiveCategories.FindAsync(id);
            if (category == null || category.IsDeleted)
            {
                return NotFound();
            }

            // التحقق من عدم نقل الفئة إلى نفسها أو إلى فئة فرعية منها
            if (newParentId.HasValue)
            {
                if (newParentId.Value == id)
                {
                    return BadRequest("لا يمكن نقل الفئة إلى نفسها");
                }

                // التحقق من وجود الفئة الأب الجديدة
                var newParent = await _context.ArchiveCategories.FindAsync(newParentId.Value);
                if (newParent == null || newParent.IsDeleted)
                {
                    return BadRequest("الفئة الأب الجديدة غير موجودة");
                }

                // التحقق من عدم كون الفئة الجديدة فرعية من الفئة المراد نقلها
                if (await IsDescendant(newParentId.Value, id))
                {
                    return BadRequest("لا يمكن نقل الفئة إلى فئة فرعية منها");
                }
            }

            // التحقق من عدم وجود فئة بنفس الاسم في المكان الجديد
            var existingCategory = await _context.ArchiveCategories
                .FirstOrDefaultAsync(ac => ac.Name.ToLower() == category.Name.ToLower() &&
                                          ac.ParentId == newParentId &&
                                          ac.Id != id &&
                                          !ac.IsDeleted);

            if (existingCategory != null)
            {
                return BadRequest("يوجد فئة بهذا الاسم في المكان الجديد مسبقاً");
            }

            category.ParentId = newParentId;
            category.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// التحقق من كون فئة معينة فرعية من فئة أخرى
        /// </summary>
        private async Task<bool> IsDescendant(int potentialDescendantId, int ancestorId)
        {
            var category = await _context.ArchiveCategories.FindAsync(potentialDescendantId);

            while (category != null && category.ParentId.HasValue)
            {
                if (category.ParentId.Value == ancestorId)
                {
                    return true;
                }
                category = await _context.ArchiveCategories.FindAsync(category.ParentId.Value);
            }

            return false;
        }

        /// <summary>
        /// الحصول على مسار الفئة (من الجذر إلى الفئة)
        /// </summary>
        /// <param name="id">معرف الفئة</param>
        /// <returns>مسار الفئة</returns>
        /// <response code="200">إرجاع مسار الفئة</response>
        /// <response code="404">الفئة غير موجودة</response>
        [HttpGet("{id}/path")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<object>>> GetCategoryPath(int id)
        {
            var category = await _context.ArchiveCategories
                .Include(ac => ac.CreatedByNavigation)
                .FirstOrDefaultAsync(ac => ac.Id == id && !ac.IsDeleted);

            if (category == null)
            {
                return NotFound();
            }

            var path = new List<object>();
            var current = category;

            while (current != null)
            {
                path.Insert(0, new
                {
                    current.Id,
                    current.Name,
                    current.Description,
                    current.Color,
                    current.Icon
                });

                if (current.ParentId.HasValue)
                {
                    current = await _context.ArchiveCategories
                        .FirstOrDefaultAsync(ac => ac.Id == current.ParentId.Value && !ac.IsDeleted);
                }
                else
                {
                    current = null;
                }
            }

            return Ok(path);
        }

        private bool ArchiveCategoryExists(int id)
        {
            return _context.ArchiveCategories.Any(e => e.Id == id && !e.IsDeleted);
        }
    }
}
