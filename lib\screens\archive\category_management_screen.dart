import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_application_2/controllers/archive_controller.dart';
import 'package:flutter_application_2/models/archive_category_model.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/utils/responsive_helper.dart';
import 'package:flutter_application_2/widgets/custom_app_bar.dart';
import 'package:flutter_application_2/widgets/loading_indicator.dart';
import 'package:uuid/uuid.dart';

/// شاشة إدارة تصنيفات الأرشيف
class CategoryManagementScreen extends StatefulWidget {
  const CategoryManagementScreen({super.key});

  @override
  State<CategoryManagementScreen> createState() => _CategoryManagementScreenState();
}

class _CategoryManagementScreenState extends State<CategoryManagementScreen> {
  final ArchiveController _controller = Get.find<ArchiveController>();
  final Uuid _uuid = const Uuid();

  // حالة التحميل
  bool _isLoading = false;

  // حالة التحرير
  ArchiveCategory? _selectedCategory;
  bool _isEditing = false;

  // متحكمات النموذج
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  String? _selectedParentId;
  String _selectedColor = '#3498db';
  String _selectedIcon = 'folder';

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _controller.loadCategories();
    } catch (e) {
      debugPrint('خطأ في تحميل التصنيفات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _selectCategory(ArchiveCategory category) {
    setState(() {
      _selectedCategory = category;
      _isEditing = true;
      _nameController.text = category.name;
      _descriptionController.text = category.description ?? '';
      _selectedParentId = category.parentId;
      _selectedColor = category.color ?? '#3498db';
      _selectedIcon = category.icon ?? 'folder';
    });
  }

  void _resetForm() {
    setState(() {
      _selectedCategory = null;
      _isEditing = false;
      _nameController.clear();
      _descriptionController.clear();
      _selectedParentId = null;
      _selectedColor = '#3498db';
      _selectedIcon = 'folder';
    });
  }

  Future<void> _saveCategory() async {
    if (_nameController.text.isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى إدخال اسم التصنيف',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(178),
        colorText: Colors.white,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isEditing && _selectedCategory != null) {
        // تحديث تصنيف موجود
        final updatedCategory = _selectedCategory!.copyWith(
          name: _nameController.text,
          description: _descriptionController.text.isEmpty ? null : _descriptionController.text,
          parentId: _selectedParentId,
          color: _selectedColor,
          icon: _selectedIcon,
          updatedAt: DateTime.now(),
        );

        await _controller.archiveService.updateCategory(updatedCategory);
      } else {
        // إنشاء تصنيف جديد
        final newCategory = ArchiveCategory(
          id: _uuid.v4(),
          name: _nameController.text,
          description: _descriptionController.text.isEmpty ? null : _descriptionController.text,
          parentId: _selectedParentId,
          color: _selectedColor,
          icon: _selectedIcon,
          order: _controller.categories.length,
          createdAt: DateTime.now(),
        );

        await _controller.createCategory(
          name: newCategory.name,
          description: newCategory.description,
          parentId: newCategory.parentId,
          color: newCategory.color,
          icon: newCategory.icon,
        );
      }

      // إعادة تحميل التصنيفات
      await _loadCategories();

      // إعادة تعيين النموذج
      _resetForm();

      Get.snackbar(
        'تم الحفظ',
        'تم حفظ التصنيف بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success.withAlpha(178),
        colorText: Colors.white,
      );
    } catch (e) {
      debugPrint('خطأ في حفظ التصنيف: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حفظ التصنيف',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(178),
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteCategory(ArchiveCategory category) async {
    // التحقق من وجود وثائق أو تصنيفات فرعية
    final hasDocuments = await _controller.archiveService.hasDocumentsInCategory(category.id);
    final hasSubcategories = await _controller.archiveService.hasSubcategories(category.id);

    if (hasDocuments || hasSubcategories) {
      Get.snackbar(
        'تعذر الحذف',
        'لا يمكن حذف التصنيف لأنه يحتوي على وثائق أو تصنيفات فرعية',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.warning.withAlpha(178),
        colorText: Colors.white,
      );
      return;
    }

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف تصنيف "${category.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (result == true) {
      setState(() {
        _isLoading = true;
      });

      try {
        await _controller.archiveService.deleteCategory(category.id);

        // إعادة تحميل التصنيفات
        await _loadCategories();

        // إعادة تعيين النموذج إذا كان التصنيف المحذوف هو المحدد
        if (_selectedCategory?.id == category.id) {
          _resetForm();
        }

        Get.snackbar(
          'تم الحذف',
          'تم حذف التصنيف بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success.withAlpha(178),
          colorText: Colors.white,
        );
      } catch (e) {
        debugPrint('خطأ في حذف التصنيف: $e');
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء حذف التصنيف',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withAlpha(178),
          colorText: Colors.white,
        );
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'إدارة تصنيفات الأرشيف',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: _loadCategories,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : isSmallScreen
              ? _buildSmallScreenLayout()
              : _buildLargeScreenLayout(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _resetForm();
          _showCategoryFormDialog();
        },
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildLargeScreenLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // قائمة التصنيفات
        Expanded(
          flex: 2,
          child: Card(
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'التصنيفات',
                    style: AppStyles.headline6,
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: _buildCategoriesList(),
                  ),
                ],
              ),
            ),
          ),
        ),

        // نموذج التصنيف
        Expanded(
          flex: 3,
          child: Card(
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildCategoryForm(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSmallScreenLayout() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التصنيفات',
            style: AppStyles.headline6,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _buildCategoriesList(),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesList() {
    return Obx(() {
      if (_controller.categories.isEmpty) {
        return Center(
          child: Text(
            'لا توجد تصنيفات',
            style: AppStyles.subtitle1.copyWith(color: Colors.grey),
          ),
        );
      }

      return ListView.builder(
        itemCount: _controller.categories.length,
        itemBuilder: (context, index) {
          final category = _controller.categories[index];
          return ListTile(
            title: Text(category.name),
            subtitle: category.description != null
                ? Text(
                    category.description!,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  )
                : null,
            leading: CircleAvatar(
              backgroundColor: _getCategoryColor(category.color).withAlpha(51),
              child: Icon(
                _getCategoryIcon(category.icon),
                color: _getCategoryColor(category.color),
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit),
                  tooltip: 'تحرير',
                  onPressed: () {
                    _selectCategory(category);
                    if (ResponsiveHelper.isSmallScreen(context)) {
                      _showCategoryFormDialog();
                    }
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  tooltip: 'حذف',
                  onPressed: () => _deleteCategory(category),
                ),
              ],
            ),
            onTap: () {
              _selectCategory(category);
              if (ResponsiveHelper.isSmallScreen(context)) {
                _showCategoryFormDialog();
              }
            },
          );
        },
      );
    });
  }

  Widget _buildCategoryForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          _isEditing ? 'تحرير التصنيف' : 'إضافة تصنيف جديد',
          style: AppStyles.headline6,
        ),
        const SizedBox(height: 24),

        // اسم التصنيف
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'اسم التصنيف *',
            hintText: 'أدخل اسم التصنيف',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),

        // وصف التصنيف
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'وصف التصنيف',
            hintText: 'أدخل وصف التصنيف',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        const SizedBox(height: 16),

        // التصنيف الأب
        Obx(() => DropdownButtonFormField<String?>(
          value: _selectedParentId,
          decoration: const InputDecoration(
            labelText: 'التصنيف الأب',
            border: OutlineInputBorder(),
          ),
          items: [
            const DropdownMenuItem<String?>(
              value: null,
              child: Text('بدون تصنيف أب'),
            ),
            ..._controller.categories
                .where((c) => c.id != _selectedCategory?.id) // استبعاد التصنيف الحالي
                .map((category) => DropdownMenuItem<String?>(
                      value: category.id,
                      child: Text(category.name),
                    ))
,
          ],
          onChanged: (value) {
            setState(() {
              _selectedParentId = value;
            });
          },
        )),
        const SizedBox(height: 24),

        // أزرار الإجراءات
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: _resetForm,
              child: const Text('إلغاء'),
            ),
            const SizedBox(width: 16),
            ElevatedButton(
              onPressed: _saveCategory,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: Text(_isEditing ? 'تحديث' : 'إضافة'),
            ),
          ],
        ),
      ],
    );
  }

  void _showCategoryFormDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_isEditing ? 'تحرير التصنيف' : 'إضافة تصنيف جديد'),
        content: SingleChildScrollView(
          child: _buildCategoryForm(),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (mounted) {
                  _saveCategory();
                Navigator.of(context).pop();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: Text(_isEditing ? 'تحديث' : 'إضافة'),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) {
      return Colors.blue;
    }

    try {
      return Color(int.parse(colorString.replaceAll('#', '0xff')));
    } catch (e) {
      return Colors.blue;
    }
  }

  IconData _getCategoryIcon(String? iconName) {
    switch (iconName) {
      case 'folder_special':
        return Icons.folder_special;
      case 'account_balance':
        return Icons.account_balance;
      case 'description':
        return Icons.description;
      case 'mail':
        return Icons.mail;
      case 'assessment':
        return Icons.assessment;
      default:
        return Icons.folder;
    }
  }
}
