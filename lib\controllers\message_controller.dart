import 'package:get/get.dart';
import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import '../models/message_model.dart';
import '../models/chat_group_model.dart';
import '../models/group_member_model.dart';
import '../models/user_model.dart';
import '../models/message_attachment_model.dart';
import '../models/message_reaction_model.dart';
import '../services/message_service.dart';
import '../services/notification_service.dart';
// تم إزالة sync_service - سيتم استخدام API
import '../services/message_reaction_service.dart';
import '../services/pinned_message_service.dart';
import '../services/media_message_service.dart';
import '../services/message_search_service.dart';
import '../services/unified_realtime_service.dart';
// تم إزالة استيراد UserRepository - سيتم استخدام API
import '../controllers/auth_controller.dart';
import 'package:file_picker/file_picker.dart';

/// @deprecated استخدم UnifiedChatController بدلاً من ذلك
/// تم استبداله بوحدة التحكم الموحدة للمحادثات
/// انظر: lib/controllers/unified_chat_controller.dart
class MessageController extends GetxController {
  final MessageService _messageService = MessageService();
  final NotificationService _notificationService = NotificationService();
  // تم إزالة UserRepository - سيتم استخدام API
  final MessageReactionService _reactionService = MessageReactionService();
  final PinnedMessageService _pinnedMessageService = PinnedMessageService();
  final MediaMessageService _mediaMessageService = MediaMessageService();
  final MessageSearchService _searchService = MessageSearchService();
  final UnifiedRealtimeService _realtimeService = UnifiedRealtimeService();

  // تم إزالة SyncService - سيتم استخدام API

  final RxList<ChatGroup> chatGroups = <ChatGroup>[].obs;
  final RxList<Message> messages = <Message>[].obs;
  final RxList<GroupMember> groupMembers = <GroupMember>[].obs;
  final RxMap<String, User> users = <String, User>{}.obs;
  final RxMap<String, int> unreadCounts = <String, int>{}.obs;
  final RxList<MessageAttachment> messageAttachments =
      <MessageAttachment>[].obs;

  // مستمع للتغييرات
  StreamSubscription? _changeSubscription;

  final RxBool isLoadingGroups = false.obs;
  final RxBool isLoadingMessages = false.obs;
  final RxBool isSendingMessage = false.obs;
  final RxBool isAttachingFile = false.obs;
  final RxString selectedGroupId = ''.obs;
  final RxString error = ''.obs;

  // متغير للإشعار بتحديث الرسائل
  final RxBool _messagesUpdated = false.obs;

  // متغيرات للتحكم في تكرار الإشعارات
  DateTime _lastNotificationTime = DateTime.now();
  int _notificationCount = 0;
  static const int _notificationThreshold =
      10; // الحد الأقصى للإشعارات في الفترة الزمنية
  static const Duration _notificationCooldown =
      Duration(seconds: 5); // فترة التهدئة

  // Get total unread messages count
  int get totalUnreadCount =>
      unreadCounts.values.fold(0, (sum, count) => sum + count);

  @override
  void onInit() {
    super.onInit();

    // تم إزالة خدمة التزامن - سيتم استخدام API
    debugPrint('MessageController initialized - ready for API integration');
  }

  @override
  void onClose() {
    // إلغاء الاشتراك في تدفق التغييرات
    _changeSubscription?.cancel();
    super.onClose();
  }

  // تم إزالة معالجة تغييرات التزامن - سيتم عبر API

  // Load chat groups for a user
  Future<void> loadChatGroups(String userId) async {
    isLoadingGroups.value = true;
    error.value = '';

    try {
      // TODO: التحقق من وجود المستخدم عبر API
      // final user = await apiService.getUserById(userId);
      // if (user == null) {
      //   error.value = 'المستخدم غير موجود';
      //   chatGroups.value = [];
      //   isLoadingGroups.value = false;
      //   return;
      // }

      chatGroups.value = await _messageService.getChatGroupsForUser(userId);

      // Load unread counts for each group
      for (final group in chatGroups) {
        try {
          final count =
              await _messageService.getUnreadMessagesCount(group.id, userId);
          unreadCounts[group.id] = count;
        } catch (countError) {
          // إذا فشل حساب الرسائل غير المقروءة، نضع القيمة صفر ونستمر
          unreadCounts[group.id] = 0;
          debugPrint(
              'خطأ في حساب الرسائل غير المقروءة للمجموعة ${group.id}: $countError');
        }
      }
    } catch (e) {
      error.value = e.toString();
      chatGroups.value = [];
      debugPrint('خطأ في تحميل مجموعات المحادثة: $e');
    } finally {
      isLoadingGroups.value = false;
    }
  }

  // Get or create a direct message chat group
  Future<ChatGroup?> getOrCreateDirectMessageGroup(
      String userId1, String userId2) async {
    error.value = '';

    try {
      // TODO: التحقق من وجود المستخدمين عبر API
      // final user1 = await apiService.getUserById(userId1);
      // final user2 = await apiService.getUserById(userId2);

      // if (user1 == null) {
      //   error.value = 'المستخدم الأول غير موجود';
      //   debugPrint('المستخدم الأول غير موجود: $userId1');
      //   return null;
      // }

      // if (user2 == null) {
      //   error.value = 'المستخدم الثاني غير موجود';
      //   debugPrint('المستخدم الثاني غير موجود: $userId2');
      //   return null;
      // }

      // TODO: إضافة المستخدمين إلى قائمة المستخدمين المحملة مسبقًا عبر API
      // users[userId1] = user1;
      // users[userId2] = user2;

      final group =
          await _messageService.getOrCreateDirectMessageGroup(userId1, userId2);

      // Add to chat groups if not already there
      if (!chatGroups.any((g) => g.id == group.id)) {
        chatGroups.add(group);
        try {
          unreadCounts[group.id] =
              await _messageService.getUnreadMessagesCount(group.id, userId1);
        } catch (countError) {
          // إذا فشل حساب الرسائل غير المقروءة، نضع القيمة صفر ونستمر
          unreadCounts[group.id] = 0;
          debugPrint(
              'خطأ في حساب الرسائل غير المقروءة للمجموعة ${group.id}: $countError');
        }
      }

      return group;
    } catch (e) {
      error.value = e.toString();
      debugPrint('خطأ في إنشاء أو الحصول على مجموعة محادثة مباشرة: $e');
      return null;
    }
  }

  // Create a new group chat
  Future<ChatGroup?> createGroupChat(String name, String? description,
      String creatorId, List<String> memberIds) async {
    error.value = '';

    try {
      final group = await _messageService.createGroupChat(
          name, description, creatorId, memberIds);

      // Add to chat groups
      chatGroups.add(group);
      unreadCounts[group.id] = 0;

      return group;
    } catch (e) {
      error.value = e.toString();
      return null;
    }
  }

  // Load messages for a chat group
  Future<void> loadMessages(String groupId, String userId,
      {int limit = 50, int offset = 0}) async {
    isLoadingMessages.value = true;
    error.value = '';
    selectedGroupId.value = groupId;

    try {
      // تحميل الرسائل بشكل تدريجي
      await _loadMessagesInBatches(groupId, userId,
          limit: limit, offset: offset);
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحميل الرسائل: ${e.toString()}';
      debugPrint('خطأ في تحميل الرسائل: $e');
      messages.value = [];
    } finally {
      isLoadingMessages.value = false;
    }
  }

  // تحميل الرسائل بشكل تدريجي
  Future<void> _loadMessagesInBatches(String groupId, String userId,
      {int limit = 50, int offset = 0}) async {
    try {
      // تحميل الرسائل
      final loadedMessages = await _messageService.getMessagesForGroup(groupId,
          limit: limit, offset: offset);
      messages.value = loadedMessages;

      // تحميل أعضاء المجموعة في عملية منفصلة
      _loadGroupMembersAsync(groupId);

      // تحميل بيانات المستخدمين لمرسلي الرسائل بشكل تدريجي
      _loadMessageSendersAsync(loadedMessages);

      // وضع علامة على الرسائل كمقروءة إذا كانت هناك رسائل
      if (loadedMessages.isNotEmpty) {
        final lastMessageId = loadedMessages.last.id;
        _markMessagesAsReadAsync(groupId, userId, lastMessageId);
      }

      // إرسال إشعار بتحديث الرسائل
      notifyMessagesUpdated();
    } catch (e) {
      debugPrint('خطأ في تحميل الرسائل بشكل تدريجي: $e');
      rethrow;
    }
  }

  // تحميل أعضاء المجموعة بشكل غير متزامن
  void _loadGroupMembersAsync(String groupId) {
    Future.microtask(() async {
      try {
        await loadGroupMembers(groupId);
      } catch (e) {
        debugPrint('خطأ في تحميل أعضاء المجموعة: $e');
      }
    });
  }

  // تحميل بيانات المستخدمين لمرسلي الرسائل بشكل غير متزامن
  void _loadMessageSendersAsync(List<Message> loadedMessages) {
    Future.microtask(() async {
      try {
        final userIds = loadedMessages.map((m) => m.senderId).toSet().toList();
        for (final userId in userIds) {
          if (!users.containsKey(userId)) {
            // TODO: تحميل بيانات المستخدم عبر API
            // final user = await apiService.getUserById(userId);
            // if (user != null) {
            //   users[userId] = user;
            // }
          }
        }
        // تحديث واجهة المستخدم بعد تحميل بيانات المستخدمين
        update();
      } catch (e) {
        debugPrint('خطأ في تحميل بيانات المستخدمين: $e');
      }
    });
  }

  // وضع علامة على الرسائل كمقروءة بشكل غير متزامن
  void _markMessagesAsReadAsync(
      String groupId, String userId, String lastMessageId) {
    Future.microtask(() async {
      try {
        await _messageService.markMessagesAsRead(
            groupId, userId, lastMessageId);
        unreadCounts[groupId] = 0;
        // تحديث واجهة المستخدم بعد وضع علامة على الرسائل كمقروءة
        update();
      } catch (e) {
        debugPrint('خطأ في وضع علامة على الرسائل كمقروءة: $e');
      }
    });
  }

  // Load more messages (pagination)
  Future<void> loadMoreMessages(String groupId, String userId,
      {int limit = 50}) async {
    if (isLoadingMessages.value) return;

    isLoadingMessages.value = true;
    error.value = '';

    try {
      final offset = messages.length;
      final moreMessages = await _messageService.getMessagesForGroup(groupId,
          limit: limit, offset: offset);

      if (moreMessages.isNotEmpty) {
        // إضافة إلى الرسائل الموجودة
        messages.addAll(moreMessages);

        // تحميل بيانات المستخدمين لمرسلي الرسائل الجديدة بشكل غير متزامن
        _loadMessageSendersAsync(moreMessages);

        // إرسال إشعار بتحديث الرسائل
        notifyMessagesUpdated();
      }
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحميل المزيد من الرسائل: ${e.toString()}';
      debugPrint('خطأ في تحميل المزيد من الرسائل: $e');
    } finally {
      isLoadingMessages.value = false;
    }
  }

  /// إرسال رسالة
  ///
  /// يقوم بإرسال رسالة جديدة إلى مجموعة محادثة وإرسال الإشعارات المناسبة
  Future<Message?> sendMessage(
    String groupId,
    String senderId,
    String content, {
    MessageContentType contentType = MessageContentType.text,
    List<String>? attachmentIds,
    List<String>? mentionedUserIds,
    String? replyToMessageId,
  }) async {
    if (content.trim().isEmpty) return null;

    isSendingMessage.value = true;
    error.value = '';

    try {
      // إرسال الرسالة
      final message = await _messageService.sendMessage(
        groupId,
        senderId,
        content,
        contentType: contentType,
        attachmentIds: attachmentIds,
        mentionedUserIds: mentionedUserIds,
        replyToMessageId: replyToMessageId,
      );

      // إضافة إلى قائمة الرسائل
      messages.add(message);

      // تحديث مجموعة المحادثة في القائمة
      _updateChatGroupWithNewMessage(groupId, message);

      // إرسال الإشعارات بشكل غير متزامن
      _sendNotificationsAsync(message, groupId);

      // تم إزالة إضافة تغيير للتزامن - سيتم عبر API

      // إرسال إشعار بتحديث الرسائل
      notifyMessagesUpdated();

      return message;
    } catch (e) {
      error.value = 'حدث خطأ أثناء إرسال الرسالة: ${e.toString()}';
      debugPrint('خطأ في إرسال الرسالة: $e');
      return null;
    } finally {
      isSendingMessage.value = false;
    }
  }

  // تحديث مجموعة المحادثة بعد إرسال رسالة جديدة
  void _updateChatGroupWithNewMessage(String groupId, Message message) {
    final index = chatGroups.indexWhere((g) => g.id == groupId);
    if (index >= 0) {
      final updatedGroup = chatGroups[index].copyWith(
        lastMessageId: message.id,
        updatedAt: DateTime.now(),
      );
      chatGroups[index] = updatedGroup;

      // ترتيب مجموعات المحادثة حسب وقت التحديث
      chatGroups.sort((a, b) =>
          b.updatedAt?.compareTo(a.updatedAt ?? a.createdAt) ??
          b.createdAt.compareTo(a.createdAt));
    }
  }

  // إرسال الإشعارات بشكل غير متزامن
  void _sendNotificationsAsync(Message message, String groupId) {
    Future.microtask(() async {
      try {
        final index = chatGroups.indexWhere((g) => g.id == groupId);
        if (index >= 0) {
          final group = chatGroups[index];
          await _sendNotifications(message, group);
        }
      } catch (e) {
        debugPrint('خطأ في إرسال الإشعارات: $e');
      }
    });
  }

  /// إضافة تفاعل إلى رسالة
  Future<bool> addReaction(
      String messageId, String userId, ReactionType type) async {
    try {
      await _reactionService.addReaction(messageId, userId, type);
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة تفاعل: $e');
      return false;
    }
  }

  /// إزالة تفاعل من رسالة
  Future<bool> removeReaction(String messageId, String userId) async {
    try {
      await _reactionService.removeReaction(messageId, userId);
      return true;
    } catch (e) {
      debugPrint('خطأ في إزالة تفاعل: $e');
      return false;
    }
  }

  /// تثبيت رسالة
  Future<bool> pinMessage(String messageId, String userId) async {
    try {
      final success = await _pinnedMessageService.pinMessage(messageId, userId);
      if (success) {
        // تحديث الرسالة في القائمة
        final index = messages.indexWhere((m) => m.id == messageId);
        if (index >= 0) {
          final updatedMessage = messages[index].copyWith(
            isPinned: true,
            pinnedAt: DateTime.now(),
            pinnedBy: userId,
          );
          messages[index] = updatedMessage;
          messages.refresh();
        }
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في تثبيت الرسالة: $e');
      return false;
    }
  }

  /// إلغاء تثبيت رسالة
  Future<bool> unpinMessage(String messageId, String userId) async {
    try {
      final success =
          await _pinnedMessageService.unpinMessage(messageId, userId);
      if (success) {
        // تحديث الرسالة في القائمة
        final index = messages.indexWhere((m) => m.id == messageId);
        if (index >= 0) {
          final updatedMessage = messages[index].copyWith(
            isPinned: false,
            pinnedAt: null,
            pinnedBy: null,
          );
          messages[index] = updatedMessage;
          messages.refresh();
        }
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في إلغاء تثبيت الرسالة: $e');
      return false;
    }
  }

  /// تعليم رسالة للمتابعة
  Future<bool> markMessageForFollowUp(String messageId, String userId,
      {DateTime? followUpAt}) async {
    try {
      final success = await _pinnedMessageService
          .markMessageForFollowUp(messageId, userId, followUpAt: followUpAt);
      if (success) {
        // تحديث الرسالة في القائمة
        final index = messages.indexWhere((m) => m.id == messageId);
        if (index >= 0) {
          final updatedMessage = messages[index].copyWith(
            isMarkedForFollowUp: true,
            followUpAt: followUpAt,
            markedForFollowUpBy: userId,
          );
          messages[index] = updatedMessage;
          messages.refresh();
        }
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في تعليم الرسالة للمتابعة: $e');
      return false;
    }
  }

  /// إلغاء تعليم رسالة للمتابعة
  Future<bool> unmarkMessageForFollowUp(String messageId, String userId) async {
    try {
      final success = await _pinnedMessageService.unmarkMessageForFollowUp(
          messageId, userId);
      if (success) {
        // تحديث الرسالة في القائمة
        final index = messages.indexWhere((m) => m.id == messageId);
        if (index >= 0) {
          final updatedMessage = messages[index].copyWith(
            isMarkedForFollowUp: false,
            followUpAt: null,
            markedForFollowUpBy: null,
          );
          messages[index] = updatedMessage;
          messages.refresh();
        }
      }
      return success;
    } catch (e) {
      debugPrint('خطأ في إلغاء تعليم الرسالة للمتابعة: $e');
      return false;
    }
  }

  /// إرسال رسالة صورة
  Future<Message?> sendImageMessage(
    String groupId,
    String senderId,
    File imageFile, {
    String? caption,
    List<String>? mentionedUserIds,
    String? replyToMessageId,
  }) async {
    try {
      isSendingMessage.value = true;

      final message = await _mediaMessageService.sendImageMessage(
        groupId,
        senderId,
        imageFile,
        caption: caption,
        mentionedUserIds: mentionedUserIds,
        replyToMessageId: replyToMessageId,
      );

      // إضافة إلى قائمة الرسائل
      messages.add(message);

      // تحديث مجموعة المحادثة في القائمة
      _updateChatGroupWithNewMessage(groupId, message);

      // إرسال الإشعارات بشكل غير متزامن
      _sendNotificationsAsync(message, groupId);

      // تم إزالة إضافة تغيير للتزامن - سيتم عبر API

      // إرسال إشعار بتحديث الرسائل
      notifyMessagesUpdated();

      return message;
    } catch (e) {
      error.value = 'حدث خطأ أثناء إرسال الصورة: ${e.toString()}';
      debugPrint('خطأ في إرسال رسالة صورة: $e');
      return null;
    } finally {
      isSendingMessage.value = false;
    }
  }

  /// إرسال رسالة ملف
  Future<Message?> sendFileMessage(
    String groupId,
    String senderId,
    File file, {
    String? fileName,
    String? caption,
    List<String>? mentionedUserIds,
    String? replyToMessageId,
  }) async {
    try {
      isSendingMessage.value = true;

      final message = await _mediaMessageService.sendFileMessage(
        groupId,
        senderId,
        file,
        caption: caption ?? (fileName != null ? 'ملف: $fileName' : null),
        mentionedUserIds: mentionedUserIds,
        replyToMessageId: replyToMessageId,
      );

      // إضافة إلى قائمة الرسائل
      messages.add(message);

      // تحديث مجموعة المحادثة في القائمة
      _updateChatGroupWithNewMessage(groupId, message);

      // إرسال الإشعارات بشكل غير متزامن
      _sendNotificationsAsync(message, groupId);

      // تم إزالة إضافة تغيير للتزامن - سيتم عبر API

      // إرسال إشعار بتحديث الرسائل
      notifyMessagesUpdated();

      return message;
    } catch (e) {
      error.value = 'حدث خطأ أثناء إرسال الملف: ${e.toString()}';
      debugPrint('خطأ في إرسال رسالة ملف: $e');
      return null;
    } finally {
      isSendingMessage.value = false;
    }
  }

  /// إرسال رسالة موقع
  Future<Message?> sendLocationMessage(
    String groupId,
    String senderId,
    double latitude,
    double longitude, {
    List<String>? mentionedUserIds,
    String? replyToMessageId,
  }) async {
    try {
      isSendingMessage.value = true;

      // إنشاء رابط خرائط جوجل
      final googleMapsUrl =
          'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';

      // إرسال الموقع كرسالة
      final message = await _messageService.sendMessage(
        groupId,
        senderId,
        'شارك موقعه الحالي: $googleMapsUrl',
        contentType: MessageContentType.location,
        mentionedUserIds: mentionedUserIds,
        replyToMessageId: replyToMessageId,
      );

      // إضافة إلى قائمة الرسائل
      messages.add(message);

      // تحديث مجموعة المحادثة في القائمة
      _updateChatGroupWithNewMessage(groupId, message);

      // إرسال الإشعارات بشكل غير متزامن
      _sendNotificationsAsync(message, groupId);

      // تم إزالة إضافة تغيير للتزامن - سيتم عبر API

      // إرسال إشعار بتحديث الرسائل
      notifyMessagesUpdated();

      return message;
    } catch (e) {
      error.value = 'حدث خطأ أثناء إرسال الموقع: ${e.toString()}';
      debugPrint('خطأ في إرسال رسالة موقع: $e');
      return null;
    } finally {
      isSendingMessage.value = false;
    }
  }

  /// البحث في الرسائل
  Future<List<Message>> searchMessages(String query, String userId,
      {int limit = 20, int offset = 0}) async {
    try {
      return await _searchService.searchMessages(query, userId,
          limit: limit, offset: offset);
    } catch (e) {
      debugPrint('خطأ في البحث عن الرسائل: $e');
      return [];
    }
  }

  /// البحث المتقدم في الرسائل
  Future<List<Message>> advancedSearch({
    String? query,
    required String userId,
    String? groupId,
    String? senderId,
    DateTime? startDate,
    DateTime? endDate,
    List<MessageContentType>? contentTypes,
    bool? hasMentions,
    bool? hasAttachments,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      return await _searchService.advancedSearch(
        query: query,
        userId: userId,
        groupId: groupId,
        senderId: senderId,
        startDate: startDate,
        endDate: endDate,
        contentTypes: contentTypes,
        hasMentions: hasMentions,
        hasAttachments: hasAttachments,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      debugPrint('خطأ في البحث المتقدم: $e');
      return [];
    }
  }

  /// الحصول على الرسائل المثبتة لمجموعة
  Future<List<Message>> getPinnedMessagesForGroup(String groupId) async {
    try {
      return await _pinnedMessageService.getPinnedMessagesForGroup(groupId);
    } catch (e) {
      debugPrint('خطأ في الحصول على الرسائل المثبتة: $e');
      return [];
    }
  }

  /// الحصول على الرسائل المعلمة للمتابعة للمستخدم
  Future<List<Message>> getFollowUpMessagesForUser(String userId) async {
    try {
      return await _pinnedMessageService.getFollowUpMessagesForUser(userId);
    } catch (e) {
      debugPrint('خطأ في الحصول على رسائل المتابعة: $e');
      return [];
    }
  }

  // تم إزالة إضافة تغيير للتزامن - سيتم عبر API

  /// إرسال الإشعارات
  ///
  /// يقوم بإرسال الإشعارات المناسبة للمستخدمين عند إرسال رسالة جديدة
  Future<void> _sendNotifications(Message message, ChatGroup chatGroup) async {
    try {
      // الحصول على اسم المرسل
      final sender = users[message.senderId];
      if (sender == null) {
        // TODO: تحميل بيانات المرسل عبر API
        // final user = await apiService.getUserById(message.senderId);
        // if (user != null) {
        //   users[message.senderId] = user;
        // }
      }
      final senderName = getUserName(message.senderId);

      // إرسال إشعارات للمستخدمين المشار إليهم
      final mentionedUserIds = message.mentionedUserIds ?? [];
      if (mentionedUserIds.isNotEmpty) {
        for (final userId in mentionedUserIds) {
          // تجاهل المرسل نفسه
          if (userId == message.senderId) continue;

          await _notificationService.createMentionedInMessageNotification(
            userId,
            message.senderId,
            senderName,
            chatGroup.name,
            message.content,
            chatGroup.id,
          );
        }
      }

      // إرسال إشعارات للمستخدمين الآخرين في المجموعة
      for (final member in groupMembers) {
        // تجاهل المرسل نفسه
        if (member.userId == message.senderId) continue;

        // تجاهل المستخدمين الذين تمت الإشارة إليهم (لأنهم تلقوا إشعارات بالفعل)
        if (mentionedUserIds.contains(member.userId)) {
          continue;
        }

        // إرسال إشعار مناسب حسب نوع المجموعة
        if (chatGroup.isDirectMessage) {
          await _notificationService.createNewDirectMessageNotification(
            member.userId,
            message.senderId,
            senderName,
            message.content,
            chatGroup.id,
          );
        } else {
          await _notificationService.createNewGroupMessageNotification(
            member.userId,
            message.senderId,
            senderName,
            chatGroup.name,
            message.content,
            chatGroup.id,
          );
        }
      }
    } catch (e) {
      debugPrint('Error sending notifications: $e');
    }
  }

  // Load group members
  Future<void> loadGroupMembers(String groupId) async {
    error.value = '';

    try {
      groupMembers.value = await _messageService.getGroupMembers(groupId);

      // Load user data for all members
      final userIds = groupMembers.map((m) => m.userId).toList();
      for (final userId in userIds) {
        if (!users.containsKey(userId)) {
          // TODO: تحميل بيانات المستخدم عبر API
          // final user = await apiService.getUserById(userId);
          // if (user != null) {
          //   users[userId] = user;
          // }
        }
      }
    } catch (e) {
      error.value = e.toString();
      groupMembers.value = [];
    }
  }

  // Delete a message
  Future<bool> deleteMessage(String messageId) async {
    error.value = '';

    try {
      final result = await _messageService.deleteMessage(messageId);

      if (result) {
        // Remove from messages list or mark as deleted
        final index = messages.indexWhere((m) => m.id == messageId);
        if (index >= 0) {
          final deletedMessage = messages[index].copyWith(
            content: 'This message was deleted',
            isDeleted: true,
          );
          messages[index] = deletedMessage;
        }
      }

      return result;
    } catch (e) {
      error.value = e.toString();
      return false;
    }
  }

  // Leave a chat group
  Future<bool> leaveGroup(String groupId, String userId) async {
    error.value = '';

    try {
      final result = await _messageService.leaveGroup(groupId, userId);

      if (result) {
        // Remove from chat groups list
        chatGroups.removeWhere((g) => g.id == groupId);
        unreadCounts.remove(groupId);
      }

      return result;
    } catch (e) {
      error.value = e.toString();
      return false;
    }
  }

  /// إضافة أعضاء إلى مجموعة
  ///
  /// يقوم بإضافة مستخدمين إلى مجموعة محادثة وإرسال إشعارات لهم
  Future<bool> addMembersToGroup(
      String groupId, List<String> userIds, String addedByUserId) async {
    error.value = '';

    try {
      // إضافة الأعضاء إلى المجموعة
      final result = await _messageService.addMembersToGroup(groupId, userIds);

      if (result) {
        // إعادة تحميل أعضاء المجموعة
        await loadGroupMembers(groupId);

        // الحصول على معلومات المجموعة
        final groupIndex = chatGroups.indexWhere((g) => g.id == groupId);
        if (groupIndex >= 0) {
          final group = chatGroups[groupIndex];

          // الحصول على اسم المستخدم الذي قام بالإضافة
          final addedByUserName = getUserName(addedByUserId);

          // إرسال إشعارات للمستخدمين المضافين
          for (final userId in userIds) {
            await _notificationService.createAddedToGroupNotification(
              userId,
              addedByUserId,
              addedByUserName,
              group.name,
              groupId,
            );
          }
        }
      }

      return result;
    } catch (e) {
      error.value = e.toString();
      return false;
    }
  }

  // Update group info
  Future<bool> updateGroupInfo(
      String groupId, String name, String? description) async {
    error.value = '';

    try {
      final result =
          await _messageService.updateGroupInfo(groupId, name, description);

      if (result) {
        // Update chat group in the list
        final index = chatGroups.indexWhere((g) => g.id == groupId);
        if (index >= 0) {
          final updatedGroup = chatGroups[index].copyWith(
            name: name,
            description: description,
            updatedAt: DateTime.now(),
          );
          chatGroups[index] = updatedGroup;
        }
      }

      return result;
    } catch (e) {
      error.value = e.toString();
      return false;
    }
  }

  // Get user name by ID
  String getUserName(String userId) {
    return users[userId]?.name ?? 'Unknown User';
  }

  // Get user avatar by ID
  String? getUserAvatar(String userId) {
    return users[userId]?.profileImage;
  }

  // Clear error
  void clearError() {
    error.value = '';
  }

  /// إشعار بتحديث الرسائل
  /// يستخدم لإعلام الشاشات الأخرى بأن الرسائل قد تم تحديثها
  /// مع تطبيق آلية للحد من تكرار الإشعارات
  void notifyMessagesUpdated() {
    final now = DateTime.now();
    debugPrint(
        '🔄 تم استدعاء notifyMessagesUpdated، عدد الرسائل: ${messages.length}');

    // التحقق من الوقت المنقضي منذ آخر إشعار
    final timeSinceLastNotification = now.difference(_lastNotificationTime);

    // إذا كان الوقت المنقضي أقل من فترة التهدئة، نزيد العداد
    if (timeSinceLastNotification < _notificationCooldown) {
      _notificationCount++;

      // إذا تجاوز العداد الحد الأقصى، نتجاهل الإشعار
      if (_notificationCount > _notificationThreshold) {
        debugPrint(
            'تم تجاهل إشعار تحديث الرسائل (تكرار كثير: $_notificationCount)');
        return;
      }
    } else {
      // إعادة تعيين العداد إذا مرت فترة التهدئة
      _notificationCount = 1;
    }

    // تحديث وقت آخر إشعار
    _lastNotificationTime = now;

    // استخدام Future.microtask لتأخير الإشعار قليلاً
    // هذا يساعد في تجميع عدة تحديثات متتالية في إشعار واحد
    Future.microtask(() {
      // تغيير قيمة المتغير لإطلاق الإشعار
      final oldValue = _messagesUpdated.value;
      _messagesUpdated.value = !_messagesUpdated.value;
      debugPrint(
          '🔄 تم تغيير قيمة _messagesUpdated من $oldValue إلى ${_messagesUpdated.value}');

      // تحديث الرسائل مرتين لضمان تحديث واجهة المستخدم
      messages.refresh();

      // إضافة تأخير قصير ثم تحديث مرة أخرى للتأكد من تحديث واجهة المستخدم
      Future.delayed(const Duration(milliseconds: 100), () {
        messages.refresh();
        debugPrint('🔄 تم استدعاء messages.refresh() مرة أخرى بعد تأخير');
      });

      // طباعة رسالة تشخيصية مع عدد الإشعارات
      debugPrint('تم إرسال إشعار بتحديث الرسائل (عدد: $_notificationCount)');
    });
  }

  /// الاشتراك في إشعارات تحديث الرسائل
  /// يمكن استخدامها في الشاشات للاستماع للتغييرات
  Worker subscribeToMessagesUpdates(Function() callback) {
    // إنشاء مراقب للمتغير
    return ever(_messagesUpdated, (_) => callback());
  }

  // Pick and attach a file to a message
  Future<File?> pickFile() async {
    try {
      isAttachingFile.value = true;
      error.value = '';

      final result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        return file;
      }
      return null;
    } catch (e) {
      error.value = e.toString();
      debugPrint('Error picking file: $e');
      return null;
    } finally {
      isAttachingFile.value = false;
    }
  }

  // Save a message attachment
  Future<MessageAttachment?> saveMessageAttachment(
    String messageId,
    String uploaderId,
    File file,
  ) async {
    try {
      isAttachingFile.value = true;
      error.value = '';

      final fileName = file.path.split('/').last;
      final fileExtension =
          fileName.contains('.') ? fileName.split('.').last : '';
      final fileType = _getFileType(fileExtension);

      final attachment = await _messageService.saveMessageAttachment(
        messageId,
        uploaderId,
        file,
        fileName,
        fileType,
      );

      if (attachment != null) {
        messageAttachments.add(attachment);
      }

      return attachment;
    } catch (e) {
      error.value = e.toString();
      debugPrint('Error saving attachment: $e');
      return null;
    } finally {
      isAttachingFile.value = false;
    }
  }

  // Get file type based on extension
  String _getFileType(String extension) {
    switch (extension.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'image/${extension.toLowerCase()}';
      case 'pdf':
        return 'application/pdf';
      case 'doc':
      case 'docx':
        return 'application/msword';
      case 'xls':
      case 'xlsx':
        return 'application/vnd.ms-excel';
      case 'ppt':
      case 'pptx':
        return 'application/vnd.ms-powerpoint';
      case 'txt':
        return 'text/plain';
      default:
        return 'application/octet-stream';
    }
  }

  // Get attachments for a message
  Future<void> loadAttachmentsForMessage(String messageId) async {
    try {
      error.value = '';
      messageAttachments.value =
          await _messageService.getAttachmentsForMessage(messageId);
    } catch (e) {
      error.value = e.toString();
      messageAttachments.value = [];
    }
  }

  // Delete an attachment
  Future<bool> deleteMessageAttachment(String attachmentId) async {
    try {
      error.value = '';
      final result =
          await _messageService.deleteMessageAttachment(attachmentId);

      if (result) {
        messageAttachments.removeWhere((a) => a.id == attachmentId);
      }

      return result;
    } catch (e) {
      error.value = e.toString();
      return false;
    }
  }

  /// تعديل رسالة
  /// @param messageId معرف الرسالة المراد تعديلها
  /// @param newContent المحتوى الجديد للرسالة
  /// @return نجاح العملية
  Future<bool> editMessage(String messageId, String newContent) async {
    if (newContent.trim().isEmpty) return false;

    error.value = '';

    try {
      // البحث عن الرسالة في القائمة
      final index = messages.indexWhere((m) => m.id == messageId);
      if (index < 0) {
        error.value = 'الرسالة غير موجودة';
        return false;
      }

      final message = messages[index];

      // تعديل الرسالة في قاعدة البيانات
      final success =
          await _messageService.updateMessageContent(messageId, newContent);

      if (success) {
        // تحديث الرسالة في القائمة
        final updatedMessage = message.copyWith(
          content: newContent,
          isEdited: true,
        );

        messages[index] = updatedMessage;
        messages.refresh();

        // إرسال إشعار بتحديث الرسائل
        notifyMessagesUpdated();
      }

      return success;
    } catch (e) {
      error.value = 'حدث خطأ أثناء تعديل الرسالة: ${e.toString()}';
      debugPrint('خطأ في تعديل الرسالة: $e');
      return false;
    }
  }
}
