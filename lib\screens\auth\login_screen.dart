import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_strings.dart';
import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../utils/validators.dart';
import '../home/<USER>';
import 'register_screen.dart';

/// Login screen for user authentication
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// Attempts to log in the user with the provided email and password.
  ///
  /// This method validates the form, then calls the AuthController's login method.
  /// If login is successful, navigates to the HomeScreen.
  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      try {
        // Get the auth controller
        final authController = Get.find<AuthController>();

        // Clear any previous errors
        authController.clearError();

        // Attempt to login
        final success = await authController.login(
          _emailController.text.trim(),
          _passwordController.text,
        );

        if (success && mounted) {
          // قفل أحداث الماوس قبل الانتقال للشاشة الرئيسية
          // هذا يمنع مشاكل تتبع الماوس أثناء التنقل بين الشاشات
          // SafeMouseRegion.lockEvents(1000);

          // تنظيف أي أحداث ماوس معلقة
          // SafeMouseRegion.clearEvents();

          // استخدام جدولة الإطار التالي للتأكد من تطبيق قفل أحداث الماوس
          SchedulerBinding.instance.addPostFrameCallback((_) {
            // Navigate to home screen using GetX navigation
            Get.offAll(() => const HomeScreen());
          });
        } else if (mounted) {
          // If login failed but no error was set, show a generic error
          if (authController.error.value.isEmpty) {
            authController.setError(
                'فشل تسجيل الدخول. يرجى التحقق من بيانات الدخول والمحاولة مرة أخرى.');
          }
        }
      } catch (e) {
        // Handle any unexpected errors
        final authController = Get.find<AuthController>();
        authController.setError('حدث خطأ أثناء تسجيل الدخول: ${e.toString()}');
        debugPrint('خطأ في تسجيل الدخول: $e');
      }
    }
  }

  /// Builds the login screen UI
  @override
  Widget build(BuildContext context) {
    // Get the auth controller
    final authController = Get.find<AuthController>();

    // TODO: تحسين تجربة المستخدم في شاشة تسجيل الدخول
    // TODO: إضافة خيار تذكر بيانات الدخول

    return Directionality(
      // استخدام الاتجاه من اليمين إلى اليسار للغة العربية
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              // تحقق مما إذا كانت الشاشة كبيرة (جهاز لوحي أو سطح مكتب)
              final isLargeScreen = constraints.maxWidth > 600;

              return Center(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: Container(
                    width: isLargeScreen ? 500 : double.infinity,
                    padding: isLargeScreen
                        ? const EdgeInsets.all(32.0)
                        : EdgeInsets.zero,
                    decoration: isLargeScreen
                        ? BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(20),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          )
                        : null,
                    child: Form(
                      key: _formKey,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // App logo
                          Center(
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withAlpha(30),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.task_alt,
                                size: 64,
                                color: AppColors.primary,
                              ),
                            ),
                          ),
                          const SizedBox(height: 24),

                          // App name
                          Text(
                            AppStrings.appName,
                            style: AppStyles.headingLarge,
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),

                          // Welcome message
                          Text(
                            'welcomeMessage'.tr,
                            style: AppStyles.bodyLarge.copyWith(
                              color: AppColors.textSecondary,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 32),

                          // Email or Username field
                          TextFormField(
                            controller: _emailController,
                            decoration: AppStyles.inputDecoration(
                              labelText: 'البريد الإلكتروني أو اسم المستخدم',
                              hintText: 'أدخل بريدك الإلكتروني أو اسم المستخدم',
                              prefixIcon: const Icon(Icons.person),
                            ),
                            keyboardType: TextInputType.emailAddress,
                            textInputAction: TextInputAction.next,
                            validator: (value) {
                              if (!Validators.isNotEmpty(value)) {
                                return 'هذا الحقل مطلوب';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          // Password field
                          TextFormField(
                            controller: _passwordController,
                            decoration: AppStyles.inputDecoration(
                              labelText: 'كلمة المرور',
                              hintText: 'أدخل كلمة المرور',
                              prefixIcon: const Icon(Icons.lock),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscurePassword
                                      ? Icons.visibility
                                      : Icons.visibility_off,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                              ),
                            ),
                            obscureText: _obscurePassword,
                            textInputAction: TextInputAction.done,
                            validator: (value) {
                              if (!Validators.isNotEmpty(value)) {
                                return 'هذا الحقل مطلوب';
                              }
                              return null;
                            },
                            onFieldSubmitted: (_) => _login(),
                          ),
                          const SizedBox(height: 8),

                          // Forgot password link
                          Align(
                            alignment: Alignment.centerLeft,
                            child: TextButton(
                              onPressed: () {
                                // TODO: Navigate to forgot password screen
                              },
                              child: const Text('نسيت كلمة المرور؟'),
                            ),
                          ),
                          const SizedBox(height: 24),

                          // Error message
                          Obx(() => authController.error.value.isNotEmpty
                              ? Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.red.shade50,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Colors.red.shade200,
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.error_outline,
                                        color: Colors.red,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          authController.error.value,
                                          style: const TextStyle(
                                              color: Colors.red),
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : const SizedBox.shrink()),
                          const SizedBox(height: 16),

                          // Login button
                          Obx(() => ElevatedButton(
                                onPressed: authController.isLoading.value
                                    ? null
                                    : _login,
                                style: AppStyles.primaryButtonStyle,
                                child: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 12),
                                  child: authController.isLoading.value
                                      ? const SizedBox(
                                          height: 20,
                                          width: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                    Colors.white),
                                          ),
                                        )
                                      : const Text(
                                          'تسجيل الدخول',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                ),
                              )),
                          const SizedBox(height: 24),

                          // Register link
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text('ليس لديك حساب؟'),
                              TextButton(
                                onPressed: () {
                                  Navigator.of(context).push(
                                    MaterialPageRoute(
                                        builder: (_) => const RegisterScreen()),
                                  );
                                },
                                child: const Text('إنشاء حساب جديد'),
                              ),
                            ],
                          ),

                          if (isLargeScreen) const SizedBox(height: 16),

                          // Version info
                          if (isLargeScreen)
                            Text(
                              'الإصدار 1.0.0',
                              style: TextStyle(
                                color: Colors.grey.shade500,
                                fontSize: 12,
                              ),
                              textAlign: TextAlign.center,
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
