
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_application_2/controllers/archive_controller.dart';

import 'package:flutter_application_2/controllers/user_controller.dart';
import 'package:flutter_application_2/models/archive_document_model.dart';
import 'package:flutter_application_2/models/archive_category_model.dart';
import 'package:flutter_application_2/models/archive_tag_model.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/utils/responsive_helper.dart';
import 'package:flutter_application_2/utils/date_formatter.dart';
import 'package:flutter_application_2/utils/file_processor.dart';
import 'package:flutter_application_2/widgets/custom_app_bar.dart';
import 'package:flutter_application_2/widgets/loading_indicator.dart';

import 'package:flutter_application_2/services/download_service.dart';
import 'package:flutter_application_2/screens/common/file_viewer_screen.dart';

import 'widgets/document_metadata_card.dart';
import 'widgets/document_tag_chip.dart';
import 'widgets/version_history_list.dart';
import 'document_version_history_screen.dart';
import 'edit_document_screen.dart';

/// شاشة عرض تفاصيل وثيقة الأرشيف
class DocumentDetailScreen extends StatefulWidget {
  /// وثيقة الأرشيف المراد عرضها
  final ArchiveDocument document;

  const DocumentDetailScreen({
    super.key,
    required this.document,
  });

  @override
  State<DocumentDetailScreen> createState() => _DocumentDetailScreenState();
}

class _DocumentDetailScreenState extends State<DocumentDetailScreen> with SingleTickerProviderStateMixin {
  final ArchiveController _archiveController = Get.find<ArchiveController>();

  final UserController _userController = Get.find<UserController>();
  final DownloadService _downloadService = DownloadService();

  late TabController _tabController;
  late ArchiveDocument _document;

  bool _isLoading = false;
  bool _isDownloading = false;
  double _downloadProgress = 0.0;

  ArchiveCategory? _category;
  List<ArchiveTag> _tags = [];
  String _creatorName = '';

  @override
  void initState() {
    super.initState();
    _document = widget.document;
    _tabController = TabController(length: 3, vsync: this);
    _loadDocumentDetails();
    _loadDocumentVersions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadDocumentDetails() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل التصنيف
      final categories = await _archiveController.archiveService.getAllCategories();
      final category = categories.firstWhereOrNull((c) => c.id == _document.categoryId);
      if (category != null) {
        setState(() {
          _category = category;
        });
      }

      // تحميل الوسوم
      if (_document.tagIds != null && _document.tagIds!.isNotEmpty) {
        final allTags = await _archiveController.archiveService.getAllTags();
        final documentTags = allTags.where((tag) => _document.tagIds!.contains(tag.id)).toList();
        setState(() {
          _tags = documentTags;
        });
      }

      // تحميل اسم المنشئ
      final creator = await _userController.getUserById(_document.creatorId);
      if (creator != null) {
        setState(() {
          _creatorName = creator.name;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل تفاصيل الوثيقة: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تحميل إصدارات الوثيقة
  Future<void> _loadDocumentVersions() async {
    try {
      await _archiveController.loadDocumentVersions(_document.id);
    } catch (e) {
      debugPrint('خطأ في تحميل إصدارات الوثيقة: $e');
    }
  }

  Future<void> _downloadDocument() async {
    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    try {
      final result = await _downloadService.downloadFile(
        _document.filePath,
        _document.fileName,
        onProgress: (progress) {
          setState(() {
            _downloadProgress = progress;
          });
        },
      );

      if (result['success'] == true) {
        // عرض رسالة نجاح مع مسار التنزيل وخيارات فتح الملف
        Get.dialog(
          AlertDialog(
            title: const Text('تم التنزيل بنجاح'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('تم تنزيل الملف بنجاح إلى:'),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.folder, color: AppColors.primary, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          result['downloadPath'],
                          style: AppStyles.body2.copyWith(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.insert_drive_file, color: AppColors.primary, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        result['fileName'],
                        style: AppStyles.body2.copyWith(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              // زر فتح الملف باستخدام التطبيق الافتراضي
              TextButton.icon(
                onPressed: () {
                  Get.back();
                  _openDownloadedFile(result['downloadPath'], result['fileName']);
                },
                icon: const Icon(Icons.open_in_new, size: 18),
                label: const Text('فتح الملف'),
              ),
              // زر إغلاق
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('إغلاق'),
              ),
            ],
          ),
        );
      } else {
        // عرض رسالة خطأ
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء تنزيل الملف',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withAlpha(178),
          colorText: Colors.white,
        );
      }
    } catch (e) {
      debugPrint('خطأ في تنزيل الوثيقة: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تنزيل الملف',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(178),
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isDownloading = false;
        _downloadProgress = 0.0;
      });
    }
  }

  /// فتح الملف المنزل باستخدام التطبيق الافتراضي
  Future<void> _openDownloadedFile(String downloadPath, String fileName) async {
    try {
      final result = await _downloadService.openDownloadedFile(downloadPath, fileName);
      if (!result['success']) {
        Get.snackbar(
          'خطأ',
          result['message'] ?? 'فشل في فتح الملف',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withAlpha(178),
          colorText: Colors.white,
        );
      }
    } catch (e) {
      debugPrint('خطأ في فتح الملف المنزل: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء فتح الملف',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(178),
        colorText: Colors.white,
      );
    }
  }

  /// فتح الملف داخل التطبيق
  void _openFileInApp() {
    try {
      // التحقق من إمكانية عرض الملف داخل التطبيق
      if (_downloadService.canViewInApp(_document.filePath)) {
        Get.to(() => FileViewerScreen(
          filePath: _document.filePath,
          fileName: _document.fileName,
          title: _document.title,
        ));
      } else {
        // إذا كان الملف غير مدعوم للعرض داخل التطبيق، نقوم بتنزيله وفتحه باستخدام التطبيق الافتراضي
        Get.snackbar(
          'معلومات',
          'هذا النوع من الملفات غير مدعوم للعرض داخل التطبيق. سيتم تنزيله وفتحه باستخدام التطبيق الافتراضي.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.info.withAlpha(178),
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
        _downloadDocument();
      }
    } catch (e) {
      debugPrint('خطأ في فتح الملف داخل التطبيق: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء فتح الملف',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(178),
        colorText: Colors.white,
      );
    }
  }

  /// تعديل الوثيقة
  Future<void> _editDocument() async {
    final result = await Get.to(() => EditDocumentScreen(document: _document));

    if (result == true) {
      // إعادة تحميل الوثيقة بعد التعديل
      await _loadDocumentDetails();
      // إعادة تحميل إصدارات الوثيقة
      await _loadDocumentVersions();
    }
  }

  Future<void> _deleteDocument() async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذه الوثيقة؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (result == true) {
      final success = await _archiveController.deleteDocument(_document.id);
      if (success) {
        Get.back(); // العودة إلى الشاشة السابقة
        Get.snackbar(
          'تم الحذف',
          'تم حذف الوثيقة بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success.withAlpha(178),
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء حذف الوثيقة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withAlpha(178),
          colorText: Colors.white,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: _document.title,
        actions: [
          // زر التنزيل
          IconButton(
            icon: _isDownloading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.download),
            tooltip: 'تنزيل',
            onPressed: _isDownloading ? null : _downloadDocument,
          ),
          // زر التحرير
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: 'تحرير',
            onPressed: _editDocument,
          ),
          // زر الحذف
          IconButton(
            icon: const Icon(Icons.delete),
            tooltip: 'حذف',
            onPressed: _deleteDocument,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _buildBody(context, isSmallScreen),
    );
  }

  Widget _buildBody(BuildContext context, bool isSmallScreen) {
    if (isSmallScreen) {
      return Column(
        children: [
          // شريط التبويب
          TabBar(
            controller: _tabController,
            labelColor: AppColors.primary,
            unselectedLabelColor: Colors.grey,
            tabs: const [
              Tab(text: 'الملف', icon: Icon(Icons.description)),
              Tab(text: 'التفاصيل', icon: Icon(Icons.info)),
              Tab(text: 'الإصدارات', icon: Icon(Icons.history)),
            ],
          ),
          // محتوى التبويب
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildFileViewer(),
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: _buildDocumentDetails(),
                ),
                _buildVersionsTab(),
              ],
            ),
          ),
        ],
      );
    } else {
      return Column(
        children: [
          // شريط التبويب
          TabBar(
            controller: _tabController,
            labelColor: AppColors.primary,
            unselectedLabelColor: Colors.grey,
            tabs: const [
              Tab(text: 'الملف', icon: Icon(Icons.description)),
              Tab(text: 'التفاصيل', icon: Icon(Icons.info)),
              Tab(text: 'الإصدارات', icon: Icon(Icons.history)),
            ],
          ),
          // محتوى التبويب
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildFileViewer(),
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: _buildDocumentDetails(),
                ),
                _buildVersionsTab(),
              ],
            ),
          ),
        ],
      );
    }
  }

  Widget _buildFileViewer() {
    final fileType = FileProcessor.getFileType(_document.filePath);
    final canViewInApp = _downloadService.canViewInApp(_document.filePath);

    // استخدام عارض الملفات المناسب حسب نوع الملف
    return Container(
      color: Colors.grey.shade100,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getFileIcon(fileType),
              size: 64,
              color: _getFileColor(fileType),
            ),
            const SizedBox(height: 16),
            Text(
              _document.fileName,
              style: AppStyles.subtitle1,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              FileProcessor.formatFileSize(_document.fileSize),
              style: AppStyles.body2,
            ),
            const SizedBox(height: 24),
            // أزرار العمليات
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // زر فتح الملف
                ElevatedButton.icon(
                  onPressed: _openFileInApp,
                  icon: const Icon(Icons.visibility),
                  label: const Text('فتح الملف'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.buttonSecondary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                ),
                const SizedBox(width: 16),
                // زر تنزيل الملف
                ElevatedButton.icon(
                  onPressed: _isDownloading ? null : _downloadDocument,
                  icon: _isDownloading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.download),
                  label: Text(_isDownloading ? 'جاري التنزيل...' : 'تنزيل الملف'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                ),
              ],
            ),
            if (_isDownloading) ...[
              const SizedBox(height: 16),
              SizedBox(
                width: 200,
                child: LinearProgressIndicator(
                  value: _downloadProgress,
                  backgroundColor: Colors.grey.shade300,
                  valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${(_downloadProgress * 100).toStringAsFixed(0)}%',
                style: AppStyles.body2,
              ),
            ],
            // معلومات حول دعم العرض داخل التطبيق
            if (!canViewInApp) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.amber.withAlpha(51), // 0.2 * 255 = 51
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.info_outline, color: Colors.amber, size: 20),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Text(
                        'هذا النوع من الملفات سيتم فتحه باستخدام التطبيق الافتراضي',
                        style: AppStyles.body2.copyWith(color: Colors.amber.shade900),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // معلومات الوثيقة الأساسية
        DocumentMetadataCard(
          title: 'معلومات الوثيقة',
          icon: Icons.description,
          items: [
            {'العنوان': _document.title},
            {'الوصف': _document.description ?? 'لا يوجد وصف'},
            {'التصنيف': _category?.name ?? 'غير محدد'},
            {'رقم الوثيقة': _document.documentNumber ?? 'غير محدد'},
            {'تاريخ الوثيقة': _document.documentDate != null
                ? DateFormatter.formatDate(_document.documentDate!)
                : 'غير محدد'},
            {'الجهة المصدرة': _document.issuer ?? 'غير محدد'},
            {'الجهة المستلمة': _document.recipient ?? 'غير محدد'},
          ],
        ),

        const SizedBox(height: 16),

        // معلومات الملف
        DocumentMetadataCard(
          title: 'معلومات الملف',
          icon: Icons.insert_drive_file,
          items: [
            {'اسم الملف': _document.fileName},
            {'نوع الملف': _document.fileType},
            {'حجم الملف': FileProcessor.formatFileSize(_document.fileSize)},
          ],
        ),

        const SizedBox(height: 16),

        // معلومات إضافية
        DocumentMetadataCard(
          title: 'معلومات إضافية',
          icon: Icons.info,
          items: [
            {'مستوى السرية': _getConfidentialityText(_document.confidentiality)},
            {'مستوى الأهمية': _getImportanceText(_document.importance)},
            {'تاريخ الإضافة': DateFormatter.formatDateTime(_document.createdAt)},
            {'بواسطة': _creatorName},
          ],
        ),

        const SizedBox(height: 16),

        // الوسوم
        if (_tags.isNotEmpty) ...[
          Text(
            'الوسوم',
            style: AppStyles.subtitle1,
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _tags.map((tag) => DocumentTagChip(tag: tag)).toList(),
          ),
          const SizedBox(height: 16),
        ],
      ],
    );
  }

  IconData _getFileIcon(String fileType) {
    switch (fileType) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'image':
        return Icons.image;
      case 'word':
        return Icons.description;
      case 'excel':
        return Icons.table_chart;
      case 'powerpoint':
        return Icons.slideshow;
      case 'text':
        return Icons.text_snippet;
      case 'video':
        return Icons.video_file;
      case 'audio':
        return Icons.audio_file;
      default:
        return Icons.insert_drive_file;
    }
  }

  Color _getFileColor(String fileType) {
    switch (fileType) {
      case 'pdf':
        return Colors.red;
      case 'image':
        return Colors.blue;
      case 'word':
        return Colors.indigo;
      case 'excel':
        return Colors.green;
      case 'powerpoint':
        return Colors.orange;
      case 'text':
        return Colors.grey;
      case 'video':
        return Colors.purple;
      case 'audio':
        return Colors.teal;
      default:
        return Colors.blueGrey;
    }
  }

  String _getConfidentialityText(ArchiveDocumentConfidentiality confidentiality) {
    switch (confidentiality) {
      case ArchiveDocumentConfidentiality.normal:
        return 'عادي';
      case ArchiveDocumentConfidentiality.confidential:
        return 'سري';
      case ArchiveDocumentConfidentiality.highlyConfidential:
        return 'سري للغاية';
      case ArchiveDocumentConfidentiality.topSecret:
        return 'سري جداً';
    }
  }

  String _getImportanceText(ArchiveDocumentImportance importance) {
    switch (importance) {
      case ArchiveDocumentImportance.low:
        return 'منخفضة';
      case ArchiveDocumentImportance.normal:
        return 'عادية';
      case ArchiveDocumentImportance.high:
        return 'مرتفعة';
      case ArchiveDocumentImportance.urgent:
        return 'عاجلة';
    }
  }

  /// بناء علامة تبويب الإصدارات
  Widget _buildVersionsTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'تاريخ إصدارات الوثيقة',
                style: AppStyles.headline6,
              ),
              ElevatedButton.icon(
                onPressed: () {
                  Get.to(() => DocumentVersionHistoryScreen(
                    documentId: _document.id,
                    documentTitle: _document.title,
                  ));
                },
                icon: const Icon(Icons.history),
                label: const Text('عرض كل الإصدارات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: GetBuilder<ArchiveController>(
              builder: (controller) {
                if (controller.isLoading.value) {
                  return const Center(child: LoadingIndicator());
                }

                if (controller.documentVersions.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.history,
                          size: 64,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد إصدارات سابقة لهذه الوثيقة',
                          style: AppStyles.subtitle1,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'سيتم إنشاء إصدار جديد في كل مرة يتم فيها تعديل الوثيقة',
                          style: AppStyles.body2,
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                }

                return VersionHistoryList(
                  documentId: _document.id,
                  limit: 5,
                  showViewMoreButton: controller.documentVersions.length > 5,
                  onViewMoreTap: () {
                    Get.to(() => DocumentVersionHistoryScreen(
                      documentId: _document.id,
                      documentTitle: _document.title,
                    ));
                  },
                  onVersionTap: (version) {
                    // عرض الإصدار
                    Get.snackbar(
                      'عرض الإصدار',
                      'جاري فتح الإصدار ${version.versionNumber}',
                      snackPosition: SnackPosition.BOTTOM,
                    );
                  },
                  onVersionRestore: (version) {
                    // استعادة الإصدار
                    Get.dialog(
                      AlertDialog(
                        title: const Text('استعادة الإصدار'),
                        content: Text('هل أنت متأكد من استعادة الإصدار ${version.versionNumber} كإصدار حالي للوثيقة؟'),
                        actions: [
                          TextButton(
                            onPressed: () => Get.back(),
                            child: const Text('إلغاء'),
                          ),
                          ElevatedButton(
                            onPressed: () {
                              Get.back();
                              // تنفيذ استعادة الإصدار
                              Get.snackbar(
                                'استعادة الإصدار',
                                'تم استعادة الإصدار ${version.versionNumber} بنجاح',
                                snackPosition: SnackPosition.BOTTOM,
                              );
                            },
                            child: const Text('استعادة'),
                          ),
                        ],
                      ),
                    );
                  },
                  onVersionDelete: (version) {
                    // حذف الإصدار
                    Get.dialog(
                      AlertDialog(
                        title: const Text('حذف الإصدار'),
                        content: Text('هل أنت متأكد من حذف الإصدار ${version.versionNumber}؟'),
                        actions: [
                          TextButton(
                            onPressed: () => Get.back(),
                            child: const Text('إلغاء'),
                          ),
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              foregroundColor: Colors.white,
                            ),
                            onPressed: () async {
                              Get.back();
                              final success = await _archiveController.deleteDocumentVersion(version.id);
                              if (success) {
                                Get.snackbar(
                                  'حذف الإصدار',
                                  'تم حذف الإصدار ${version.versionNumber} بنجاح',
                                  snackPosition: SnackPosition.BOTTOM,
                                );
                              } else {
                                Get.snackbar(
                                  'خطأ',
                                  'فشل في حذف الإصدار',
                                  snackPosition: SnackPosition.BOTTOM,
                                  backgroundColor: Colors.red,
                                  colorText: Colors.white,
                                );
                              }
                            },
                            child: const Text('حذف'),
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
