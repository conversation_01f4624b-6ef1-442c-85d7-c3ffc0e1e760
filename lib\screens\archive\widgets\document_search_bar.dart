import 'package:flutter/material.dart';
import 'package:flutter_application_2/constants/app_colors.dart';

/// شريط بحث وثائق الأرشيف
class DocumentSearchBar extends StatefulWidget {
  /// دالة يتم استدعاؤها عند البحث
  final Function(String) onSearch;

  /// دالة يتم استدعاؤها عند النقر على زر التصفية
  final VoidCallback? onFilterTap;

  const DocumentSearchBar({
    super.key,
    required this.onSearch,
    this.onFilterTap,
  });

  @override
  State<DocumentSearchBar> createState() => _DocumentSearchBarState();
}

class _DocumentSearchBarState extends State<DocumentSearchBar> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    // يمكن تنفيذ البحث التلقائي هنا إذا لزم الأمر
  }

  void _clearSearch() {
    _searchController.clear();
    widget.onSearch('');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث في الوثائق...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: _clearSearch,
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
              ),
              onSubmitted: widget.onSearch,
            ),
          ),
          if (widget.onFilterTap != null)
            Container(
              decoration: BoxDecoration(
                border: Border(
                  left: BorderSide(
                    color: Colors.grey.shade300,
                    width: 1,
                  ),
                ),
              ),
              child: IconButton(
                icon: const Icon(Icons.filter_list),
                tooltip: 'عوامل التصفية',
                onPressed: widget.onFilterTap,
                color: AppColors.primary,
              ),
            ),
        ],
      ),
    );
  }
}
