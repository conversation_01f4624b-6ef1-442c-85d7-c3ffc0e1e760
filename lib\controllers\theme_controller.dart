import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import '../constants/app_theme.dart';

class ThemeController extends GetxController {
  final RxBool isDarkMode = false.obs;
  final RxBool isSystemTheme = false.obs;

  @override
  void onInit() {
    super.onInit();
    loadThemeMode();
  }

  /// تحميل وضع السمة من التخزين
  Future<void> loadThemeMode() async {
    try {
      final storage = Get.find<GetStorage>();
      final themeMode = storage.read<String>('theme_mode');

      debugPrint('تحميل وضع السمة المحفوظ: $themeMode');

      if (themeMode == 'ThemeMode.dark') {
        isDarkMode.value = true;
        isSystemTheme.value = false;
        debugPrint('تم تحميل الوضع الداكن');
      } else if (themeMode == 'ThemeMode.light') {
        isDarkMode.value = false;
        isSystemTheme.value = false;
        debugPrint('تم تحميل الوضع الفاتح');
      } else {
        // استخدام وضع النظام
        isSystemTheme.value = true;
        final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
        isDarkMode.value = brightness == Brightness.dark;
        debugPrint('تم تحميل وضع النظام: ${brightness == Brightness.dark ? "داكن" : "فاتح"}');
      }

      // تطبيق السمة المناسبة مباشرة
      if (!isSystemTheme.value) {
        if (isDarkMode.value) {
          Get.changeTheme(AppTheme.darkTheme);
        } else {
          Get.changeTheme(AppTheme.lightTheme);
        }
      } else {
        // في حالة وضع النظام، نطبق السمة بناءً على سطوع النظام
        final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
        if (brightness == Brightness.dark) {
          Get.changeTheme(AppTheme.darkTheme);
        } else {
          Get.changeTheme(AppTheme.lightTheme);
        }
      }

      // تطبيق وضع السمة
      final themeModeValue = isSystemTheme.value
          ? ThemeMode.system
          : (isDarkMode.value ? ThemeMode.dark : ThemeMode.light);
      Get.changeThemeMode(themeModeValue);

      // تحديث واجهة المستخدم بعد تأخير قصير
      await Future.delayed(const Duration(milliseconds: 100), () {
        Get.forceAppUpdate();
        debugPrint('تم تحديث واجهة المستخدم بعد تحميل السمة');
      });

    } catch (e) {
      debugPrint('خطأ في تحميل وضع السمة: $e');
      // استخدام القيم الافتراضية
      isDarkMode.value = false;
      isSystemTheme.value = false;
      Get.changeTheme(AppTheme.lightTheme);
      Get.changeThemeMode(ThemeMode.light);

      // محاولة حفظ الإعدادات الافتراضية
      try {
        await AppTheme.saveThemeMode(ThemeMode.light);
      } catch (saveError) {
        debugPrint('خطأ في حفظ الإعدادات الافتراضية: $saveError');
      }
    }
  }

  /// تبديل بين الوضع الفاتح والداكن
  Future<void> toggleTheme() async {
    // تغيير الوضع
    isDarkMode.value = !isDarkMode.value;
    isSystemTheme.value = false;

    // طباعة معلومات التصحيح
    debugPrint('جاري تغيير وضع السمة إلى: ${isDarkMode.value ? "داكن" : "فاتح"}');

    // حفظ وضع السمة
    final themeMode = isDarkMode.value ? ThemeMode.dark : ThemeMode.light;
    await AppTheme.saveThemeMode(themeMode);

    // تطبيق السمة المناسبة مباشرة
    if (isDarkMode.value) {
      Get.changeTheme(AppTheme.darkTheme);
    } else {
      Get.changeTheme(AppTheme.lightTheme);
    }

    // تغيير وضع السمة
    Get.changeThemeMode(themeMode);

    // تحديث واجهة المستخدم بعد تأخير أطول
    await Future.delayed(const Duration(milliseconds: 100), () {
      Get.forceAppUpdate();
      debugPrint('تم تحديث واجهة المستخدم بعد تغيير السمة');
    });

    // تحديث إضافي بعد تأخير أطول للتأكد من تطبيق التغييرات
    await Future.delayed(const Duration(milliseconds: 300), () {
      update(); // تحديث المتحكم نفسه
      Get.forceAppUpdate();
      debugPrint('تم إجراء تحديث إضافي للواجهة');
    });

    // طباعة معلومات التصحيح
    debugPrint('تم تغيير وضع السمة إلى: ${isDarkMode.value ? "داكن" : "فاتح"}');
  }

  /// استخدام وضع سمة النظام
  Future<void> useSystemTheme() async {
    isSystemTheme.value = true;

    // تحديث وضع السمة بناءً على وضع النظام
    final brightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
    isDarkMode.value = brightness == Brightness.dark;

    // طباعة معلومات التصحيح
    debugPrint('جاري تغيير وضع السمة إلى وضع النظام: ${brightness == Brightness.dark ? "داكن" : "فاتح"}');

    // حفظ وضع السمة
    await AppTheme.saveThemeMode(ThemeMode.system);

    // تطبيق السمة المناسبة مباشرة
    if (brightness == Brightness.dark) {
      Get.changeTheme(AppTheme.darkTheme);
    } else {
      Get.changeTheme(AppTheme.lightTheme);
    }

    // تطبيق وضع السمة
    Get.changeThemeMode(ThemeMode.system);

    // تحديث واجهة المستخدم بعد تأخير أطول
    await Future.delayed(const Duration(milliseconds: 100), () {
      Get.forceAppUpdate();
      debugPrint('تم تحديث واجهة المستخدم بعد تغيير السمة');
    });

    // تحديث إضافي بعد تأخير أطول للتأكد من تطبيق التغييرات
    await Future.delayed(const Duration(milliseconds: 300), () {
      update(); // تحديث المتحكم نفسه
      Get.forceAppUpdate();
      debugPrint('تم إجراء تحديث إضافي للواجهة');
    });
  }


}
