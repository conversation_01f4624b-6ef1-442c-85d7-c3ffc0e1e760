import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../database/department_repository.dart';
import '../../database/task_repository.dart';
import '../../database/user_repository.dart';
import '../../models/department_model.dart';
import '../../models/task_model.dart';
import '../../models/task_status_enum.dart';
import '../../models/user_model.dart';
import '../../routes/app_routes.dart';
import '../../services/department_service.dart';

/// شاشة تفاصيل القسم
/// تعرض تفاصيل القسم وأعضاءه والمهام المرتبطة به
class DepartmentDetailScreen extends StatefulWidget {
  final String departmentId;

  const DepartmentDetailScreen({
    super.key,
    required this.departmentId,
  });

  @override
  State<DepartmentDetailScreen> createState() => _DepartmentDetailScreenState();
}

class _DepartmentDetailScreenState extends State<DepartmentDetailScreen> with SingleTickerProviderStateMixin {
  final _departmentRepository = DepartmentRepository();
  final _userRepository = UserRepository();
  final _taskRepository = TaskRepository();
  final _departmentService = DepartmentService();
  final _authController = Get.find<AuthController>();

  late TabController _tabController;
  Department? _department;
  User? _manager;
  List<User> _members = [];
  List<Task> _departmentTasks = [];
  List<Task> _filteredTasks = [];
  Map<String, int> _taskStatusCounts = {};
  Map<String, double> _userProductivity = {};
  bool _isLoading = true;
  bool _isLoadingTasks = false;
  String? _errorMessage;

  // متغيرات الفلترة
  TaskStatus? _filterStatus;
  String? _filterAssigneeId;
  double _filterMinCompletion = 0.0;
  double _filterMaxCompletion = 100.0;
  bool _isFiltered = false;

  // تنسيق التاريخ
  final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم التبويبات
    _tabController = TabController(length: 3, vsync: this);

    // تسجيل متحكم التبويبات في GetX
    Get.put(_tabController, tag: 'department_detail_tab_controller');

    // تحميل بيانات القسم
    _loadDepartmentDetails();

    // تحميل مهام القسم عند تغيير التبويب
    _tabController.addListener(() {
      if (_tabController.index == 1 && _departmentTasks.isEmpty) {
        _loadDepartmentTasks();
      }
    });
  }

  /// تحميل مهام القسم
  Future<void> _loadDepartmentTasks() async {
    if (_department == null) return;

    setState(() {
      _isLoadingTasks = true;
    });

    try {
      // تحميل مهام القسم
      _departmentTasks = await _taskRepository.getTasksByDepartment(_department!.id);

      // تطبيق الفلترة إذا كانت مفعلة
      _applyFilters();

      // حساب عدد المهام حسب الحالة
      _taskStatusCounts = {};
      for (var task in _departmentTasks) {
        final status = task.status.toString().split('.').last;
        _taskStatusCounts[status] = (_taskStatusCounts[status] ?? 0) + 1;
      }

      // حساب إنتاجية المستخدمين
      _userProductivity = {};
      for (var member in _members) {
        // حساب عدد المهام المكتملة لكل مستخدم
        final userTasks = _departmentTasks.where((task) => task.assigneeId == member.id).toList();
        final completedTasks = userTasks.where((task) => task.status == TaskStatus.completed).length;

        if (userTasks.isNotEmpty) {
          _userProductivity[member.id] = (completedTasks / userTasks.length) * 100;
        } else {
          _userProductivity[member.id] = 0;
        }
      }
    } catch (e) {
      debugPrint('Error loading department tasks: $e');
    } finally {
      setState(() {
        _isLoadingTasks = false;
      });
    }
  }

  /// تطبيق الفلترة على المهام
  void _applyFilters() {
    if (!_isFiltered) {
      _filteredTasks = List.from(_departmentTasks);
      return;
    }

    _filteredTasks = _departmentTasks.where((task) {
      // فلترة حسب الحالة
      if (_filterStatus != null && task.status != _filterStatus) {
        return false;
      }

      // فلترة حسب المستخدم المسند إليه
      if (_filterAssigneeId != null && _filterAssigneeId!.isNotEmpty &&
          task.assigneeId != _filterAssigneeId) {
        return false;
      }

      // فلترة حسب نسبة الإكمال
      if (task.completionPercentage < _filterMinCompletion ||
          task.completionPercentage > _filterMaxCompletion) {
        return false;
      }

      return true;
    }).toList();
  }

  /// عرض مربع حوار الفلترة
  void _showTaskFilterDialog() {
    // نسخ قيم الفلترة الحالية لاستخدامها في الحوار
    TaskStatus? tempFilterStatus = _filterStatus;
    String? tempFilterAssigneeId = _filterAssigneeId;
    double tempMinCompletion = _filterMinCompletion;
    double tempMaxCompletion = _filterMaxCompletion;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('فلترة المهام'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // فلترة حسب الحالة
                    const Text('حالة المهمة:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<TaskStatus?>(
                      value: tempFilterStatus,
                      decoration: AppStyles.inputDecoration(
                        labelText: 'اختر الحالة',
                        prefixIcon: const Icon(Icons.filter_list),
                      ),
                      items: [
                        const DropdownMenuItem<TaskStatus?>(
                          value: null,
                          child: Text('الكل'),
                        ),
                        ...TaskStatus.values.map((status) {
                          return DropdownMenuItem<TaskStatus?>(
                            value: status,
                            child: Text(_getStatusText(status)),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          tempFilterStatus = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // فلترة حسب المستخدم المسند إليه
                    const Text('المسند إليه:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String?>(
                      value: tempFilterAssigneeId,
                      decoration: AppStyles.inputDecoration(
                        labelText: 'اختر المستخدم',
                        prefixIcon: const Icon(Icons.person),
                      ),
                      items: [
                        const DropdownMenuItem<String?>(
                          value: null,
                          child: Text('الكل'),
                        ),
                        ..._members.map((member) {
                          return DropdownMenuItem<String?>(
                            value: member.id,
                            child: Text(member.name),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          tempFilterAssigneeId = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // فلترة حسب نسبة الإكمال
                    const Text('نسبة الإكمال:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Text('${tempMinCompletion.toInt()}%'),
                        Expanded(
                          child: RangeSlider(
                            values: RangeValues(tempMinCompletion, tempMaxCompletion),
                            min: 0,
                            max: 100,
                            divisions: 10,
                            labels: RangeLabels(
                              '${tempMinCompletion.toInt()}%',
                              '${tempMaxCompletion.toInt()}%',
                            ),
                            onChanged: (values) {
                              setState(() {
                                tempMinCompletion = values.start;
                                tempMaxCompletion = values.end;
                              });
                            },
                          ),
                        ),
                        Text('${tempMaxCompletion.toInt()}%'),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    // إعادة تعيين الفلترة
                    Navigator.of(context).pop();

                    this.setState(() {
                      _filterStatus = null;
                      _filterAssigneeId = null;
                      _filterMinCompletion = 0.0;
                      _filterMaxCompletion = 100.0;
                      _isFiltered = false;
                      _applyFilters();
                    });
                  },
                  child: const Text('إعادة تعيين'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();

                    this.setState(() {
                      _filterStatus = tempFilterStatus;
                      _filterAssigneeId = tempFilterAssigneeId;
                      _filterMinCompletion = tempMinCompletion;
                      _filterMaxCompletion = tempMaxCompletion;
                      _isFiltered = true;
                      _applyFilters();
                    });
                  },
                  child: const Text('تطبيق'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  void dispose() {
    // إزالة متحكم التبويبات من GetX
    if (Get.isRegistered<TabController>(tag: 'department_detail_tab_controller')) {
      Get.delete<TabController>(tag: 'department_detail_tab_controller');
    }

    // إرسال إشعار بتحديث البيانات قبل الخروج من الشاشة
    // هذا يضمن تحديث البيانات في الشاشة السابقة
    Get.find<DepartmentService>().notifyDepartmentUpdated();

    _tabController.dispose();
    super.dispose();
  }

  /// تحميل تفاصيل القسم
  Future<void> _loadDepartmentDetails() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل بيانات القسم
      _department = await _departmentRepository.getDepartmentById(widget.departmentId);
      if (_department == null) {
        _errorMessage = 'القسم غير موجود';
        return;
      }

      // تحميل مدير القسم
      if (_department!.managerId != null) {
        _manager = await _userRepository.getUserById(_department!.managerId!);
      }

      // تحميل أعضاء القسم
      _members = await _departmentService.getDepartmentMembers(widget.departmentId);

      // تحميل مهام القسم إذا كان التبويب الحالي هو تبويب المهام
      if (_tabController.index == 1) {
        _loadDepartmentTasks();
      }
    } catch (e) {
      _errorMessage = 'حدث خطأ أثناء تحميل تفاصيل القسم: $e';
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// تعيين مدير للقسم
  Future<void> _assignManager(User user) async {
    if (_department == null) return;

    try {
      final success = await _departmentService.assignDepartmentManager(
        _department!.id,
        user.id,
      );

      if (success) {
        Get.snackbar(
          'تم بنجاح',
          'تم تعيين ${user.name} كمدير للقسم',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );
        _loadDepartmentDetails();
      } else {
        Get.snackbar(
          'خطأ',
          'فشل تعيين المدير',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تعيين المدير: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// إضافة مستخدم للقسم
  Future<void> _addUserToDepartment() async {
    if (_department == null) return;

    // التحقق من صلاحيات المستخدم
    if (!_authController.isAdmin && !(_authController.isDepartmentManager && _authController.currentUser.value?.departmentId == _department!.id)) {
      Get.snackbar(
        'غير مصرح',
        'ليس لديك صلاحية لإضافة مستخدمين للقسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    // تحميل جميع المستخدمين
    final allUsers = await _userRepository.getAllUsers();

    // استبعاد المستخدمين الموجودين في أي قسم (عرض فقط المستخدمين غير الموزعين على أقسام)
    final availableUsers = allUsers.where((user) =>
      (user.departmentId == null || user.departmentId!.isEmpty) && user.isActive).toList();

    if (availableUsers.isEmpty) {
      Get.snackbar(
        'تنبيه',
        'لا يوجد مستخدمين غير موزعين على أقسام متاحين للإضافة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.amber.shade100,
        colorText: Colors.amber.shade800,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    // عرض قائمة المستخدمين المتاحين
    final selectedUser = await Get.dialog<User>(
      AlertDialog(
        title: const Text('إضافة مستخدم للقسم'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'القائمة تعرض فقط المستخدمين غير الموزعين على أقسام',
                        style: TextStyle(color: Colors.blue),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: ListView.builder(
                  itemCount: availableUsers.length,
                  itemBuilder: (context, index) {
                    final user = availableUsers[index];
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: AppColors.primary,
                        child: Text(
                          user.name.substring(0, 1).toUpperCase(),
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                      title: Text(user.name),
                      subtitle: Text(user.email),
                      onTap: () => Get.back(result: user),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );

    if (selectedUser == null) return;

    try {
      final success = await _departmentService.addUserToDepartment(
        selectedUser.id,
        _department!.id,
      );

      if (success) {
        Get.snackbar(
          'تم بنجاح',
          'تم إضافة ${selectedUser.name} للقسم',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );
        _loadDepartmentDetails();
      } else {
        Get.snackbar(
          'خطأ',
          'فشل إضافة المستخدم للقسم',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إضافة المستخدم للقسم: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// إزالة مستخدم من القسم
  Future<void> _removeUserFromDepartment(User user) async {
    if (_department == null) return;

    // التحقق من صلاحيات المستخدم
    if (!_authController.isAdmin && !(_authController.isDepartmentManager && _authController.currentUser.value?.departmentId == _department!.id)) {
      Get.snackbar(
        'غير مصرح',
        'ليس لديك صلاحية لإزالة مستخدمين من القسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    // التأكيد قبل الإزالة
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تأكيد الإزالة'),
        content: Text('هل أنت متأكد من رغبتك في إزالة ${user.name} من القسم؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('إزالة'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      final success = await _departmentService.removeUserFromDepartment(user.id);

      if (success) {
        Get.snackbar(
          'تم بنجاح',
          'تم إزالة ${user.name} من القسم',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );
        _loadDepartmentDetails();
      } else {
        Get.snackbar(
          'خطأ',
          'فشل إزالة المستخدم من القسم',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إزالة المستخدم من القسم: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// تعديل بيانات القسم
  Future<void> _editDepartment() async {
    if (_department == null) return;

    // التحقق من صلاحيات المستخدم
    if (!_authController.isAdmin) {
      Get.snackbar(
        'غير مصرح',
        'ليس لديك صلاحية لتعديل بيانات القسم',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
      return;
    }

    final nameController = TextEditingController(text: _department!.name);
    final descriptionController = TextEditingController(text: _department!.description ?? '');
    bool isActive = _department!.isActive;

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تعديل بيانات القسم'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: AppStyles.inputDecoration(
                  labelText: 'اسم القسم',
                  prefixIcon: const Icon(Icons.business),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: AppStyles.inputDecoration(
                  labelText: 'وصف القسم',
                  prefixIcon: const Icon(Icons.description),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              StatefulBuilder(
                builder: (context, setState) {
                  return SwitchListTile(
                    title: const Text('نشط'),
                    value: isActive,
                    onChanged: (value) {
                      setState(() {
                        isActive = value;
                      });
                    },
                  );
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يرجى إدخال اسم القسم',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
                return;
              }
              Get.back(result: true);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result != true) return;

    try {
      final updatedDepartment = _department!.copyWith(
        name: nameController.text.trim(),
        description: descriptionController.text.trim(),
        isActive: isActive,
      );

      final rowsAffected = await _departmentRepository.updateDepartment(updatedDepartment);

      if (rowsAffected > 0) {
        Get.snackbar(
          'تم بنجاح',
          'تم تحديث بيانات القسم',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.shade100,
          colorText: Colors.green.shade800,
        );
        _loadDepartmentDetails();
      } else {
        Get.snackbar(
          'خطأ',
          'فشل تحديث بيانات القسم',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث بيانات القسم: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_department?.name ?? 'تفاصيل القسم'),
        actions: [
          if (_department != null && _authController.isAdmin)
            IconButton(
              icon: const Icon(Icons.edit),
              tooltip: 'تعديل القسم',
              onPressed: _editDepartment,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? _buildErrorWidget()
              : _department == null
                  ? const Center(child: Text('القسم غير موجود'))
                  : _buildDepartmentDetails(),
    );
  }

  /// بناء واجهة عرض الخطأ
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ',
            style: AppStyles.headingMedium,
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage ?? 'حدث خطأ غير معروف',
            style: AppStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _loadDepartmentDetails,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء واجهة عرض تفاصيل القسم
  Widget _buildDepartmentDetails() {
    return Column(
      children: [
        // معلومات القسم
        _buildDepartmentInfo(),

        // تبويبات
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'الأعضاء'),
            Tab(text: 'المهام'),
            Tab(text: 'الإحصائيات'),
          ],
        ),

        // محتوى التبويبات
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              // تبويب الأعضاء
              _buildMembersTab(),

              // تبويب المهام
              _buildTasksTab(),

              // تبويب الإحصائيات
              _buildStatsTab(),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء معلومات القسم
  Widget _buildDepartmentInfo() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.business, color: AppColors.primary, size: 32),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _department!.name,
                        style: AppStyles.headingMedium,
                      ),
                      if (_department!.description != null && _department!.description!.isNotEmpty)
                        Text(
                          _department!.description!,
                          style: AppStyles.bodyMedium,
                        ),
                    ],
                  ),
                ),
                if (!_department!.isActive)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red.shade100,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'غير نشط',
                      style: TextStyle(
                        color: Colors.red.shade800,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            ),
            const Divider(height: 32),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'مدير القسم',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _manager != null
                          ? Row(
                              children: [
                                CircleAvatar(
                                  backgroundColor: AppColors.primary,
                                  radius: 16,
                                  child: Text(
                                    _manager!.name.substring(0, 1).toUpperCase(),
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(_manager!.name),
                              ],
                            )
                          : const Text('لا يوجد مدير'),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'عدد الأعضاء',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text('${_members.length} عضو'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء تبويب الأعضاء
  Widget _buildMembersTab() {
    final canManageMembers = _authController.isAdmin ||
                            (_authController.isDepartmentManager &&
                             _authController.currentUser.value?.departmentId == _department!.id);

    return Column(
      children: [
        if (canManageMembers)
          Padding(
            padding: const EdgeInsets.all(16),
            child: ElevatedButton.icon(
              onPressed: _addUserToDepartment,
              icon: const Icon(Icons.person_add),
              label: const Text('إضافة عضو'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        Expanded(
          child: _members.isEmpty
              ? const Center(child: Text('لا يوجد أعضاء في هذا القسم'))
              : ListView.builder(
                  itemCount: _members.length,
                  itemBuilder: (context, index) {
                    final member = _members[index];
                    final isManager = _manager != null && member.id == _manager!.id;

                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: isManager ? AppColors.accent : AppColors.primary,
                        child: Text(
                          member.name.substring(0, 1).toUpperCase(),
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                      title: Text(member.name),
                      subtitle: Text(member.email),
                      trailing: canManageMembers
                          ? Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (!isManager && _authController.isAdmin)
                                  IconButton(
                                    icon: const Icon(Icons.admin_panel_settings),
                                    tooltip: 'تعيين كمدير',
                                    onPressed: () => _assignManager(member),
                                  ),
                                IconButton(
                                  icon: const Icon(Icons.person_remove),
                                  tooltip: 'إزالة من القسم',
                                  onPressed: () => _removeUserFromDepartment(member),
                                ),
                              ],
                            )
                          : null,
                    );
                  },
                ),
        ),
      ],
    );
  }

  /// بناء تبويب المهام
  Widget _buildTasksTab() {
    return _isLoadingTasks
        ? const Center(child: CircularProgressIndicator())
        : _departmentTasks.isEmpty
            ? _buildEmptyTasksWidget()
            : _buildTasksList();
  }

  /// بناء واجهة عرض القائمة الفارغة للمهام
  Widget _buildEmptyTasksWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.task_alt,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مهام',
            style: AppStyles.headingMedium,
          ),
          const SizedBox(height: 8),
          Text(
            'لا توجد مهام مرتبطة بهذا القسم حالياً',
            style: AppStyles.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () {
              // التحقق من صلاحيات المستخدم
              if (_authController.isAdmin ||
                  (_authController.isDepartmentManager &&
                   _authController.currentUser.value?.departmentId == _department!.id)) {
                // الانتقال إلى شاشة إنشاء مهمة جديدة
                Get.toNamed(AppRoutes.createTask);
              } else {
                Get.snackbar(
                  'غير مصرح',
                  'ليس لديك صلاحية لإنشاء مهام جديدة',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                );
              }
            },
            icon: const Icon(Icons.add),
            label: const Text('إنشاء مهمة جديدة'),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة المهام
  Widget _buildTasksList() {
    return Column(
      children: [
        // شريط الفلترة
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Text(
                      _isFiltered
                          ? 'المهام (${_filteredTasks.length} من ${_departmentTasks.length})'
                          : 'المهام (${_departmentTasks.length})',
                      style: AppStyles.titleMedium,
                    ),
                    if (_isFiltered)
                      Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade100,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.filter_list, size: 16, color: Colors.blue.shade800),
                              const SizedBox(width: 4),
                              Text(
                                'مفلتر',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.blue.shade800,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              IconButton(
                icon: const Icon(Icons.filter_list),
                tooltip: 'فلترة',
                onPressed: _showTaskFilterDialog,
              ),
              IconButton(
                icon: const Icon(Icons.refresh),
                tooltip: 'تحديث',
                onPressed: _loadDepartmentTasks,
              ),
            ],
          ),
        ),

        // قائمة المهام
        Expanded(
          child: _isFiltered && _filteredTasks.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.filter_list_off,
                        size: 64,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد مهام تطابق الفلترة',
                        style: AppStyles.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton.icon(
                        onPressed: () {
                          setState(() {
                            _filterStatus = null;
                            _filterAssigneeId = null;
                            _filterMinCompletion = 0.0;
                            _filterMaxCompletion = 100.0;
                            _isFiltered = false;
                            _applyFilters();
                          });
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('إعادة تعيين الفلترة'),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: _isFiltered ? _filteredTasks.length : _departmentTasks.length,
                  itemBuilder: (context, index) {
                    final task = _isFiltered ? _filteredTasks[index] : _departmentTasks[index];
                    return _buildTaskItem(task);
                  },
                ),
        ),
      ],
    );
  }

  /// بناء عنصر المهمة
  Widget _buildTaskItem(Task task) {
    // الحصول على اسم المستخدم المسند إليه المهمة
    String assigneeName = 'غير مسند';
    if (task.assigneeId != null) {
      final assignee = _members.firstWhere(
        (member) => member.id == task.assigneeId,
        orElse: () => User(
          id: '',
          name: 'غير معروف',
          email: '',
          password: '',
          role: UserRole.employee,
          isActive: true,
          createdAt: DateTime.now(),
        ),
      );
      assigneeName = assignee.name;
    }

    // تحديد لون حالة المهمة
    Color statusColor;
    switch (task.status) {
      case TaskStatus.pending:
        statusColor = Colors.grey;
        break;
      case TaskStatus.inProgress:
        statusColor = Colors.blue;
        break;
      case TaskStatus.completed:
        statusColor = Colors.green;
        break;
      case TaskStatus.cancelled:
        statusColor = Colors.red;
        break;
      case TaskStatus.waitingForInfo:
        statusColor = Colors.orange;
        break;
      default:
        statusColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        title: Text(
          task.title,
          style: AppStyles.titleSmall,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.person, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(assigneeName),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  task.dueDate != null
                      ? _dateFormat.format(task.dueDate!)
                      : 'لا يوجد موعد نهائي',
                ),
              ],
            ),
          ],
        ),
        leading: CircleAvatar(
          backgroundColor: statusColor.withAlpha(50),
          child: Icon(
            Icons.task_alt,
            color: statusColor,
          ),
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor.withAlpha(50),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                _getStatusText(task.status),
                style: TextStyle(
                  color: statusColor,
                  fontSize: 12,
                ),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${task.completionPercentage.toInt()}%',
              style: TextStyle(
                color: Colors.grey.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        onTap: () {
          // الانتقال إلى شاشة تفاصيل المهمة
          Get.toNamed(AppRoutes.taskDetail, arguments: {'taskId': task.id});
        },
      ),
    );
  }

  /// الحصول على نص حالة المهمة
  String _getStatusText(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return 'قيد الانتظار';
      case TaskStatus.inProgress:
        return 'قيد التنفيذ';
      case TaskStatus.completed:
        return 'مكتملة';
      case TaskStatus.cancelled:
        return 'ملغاة';
      case TaskStatus.waitingForInfo:
        return 'في انتظار معلومات';
      default:
        return 'غير معروف';
    }
  }

  /// بناء تبويب الإحصائيات
  Widget _buildStatsTab() {
    return _isLoadingTasks
        ? const Center(child: CircularProgressIndicator())
        : _departmentTasks.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.bar_chart,
                      size: 80,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد إحصائيات',
                      style: AppStyles.headingMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا توجد مهام لعرض إحصائياتها',
                      style: AppStyles.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // ملخص المهام
                    _buildTasksSummary(),

                    const SizedBox(height: 24),

                    // توزيع المهام حسب الحالة
                    _buildTaskStatusDistribution(),

                    const SizedBox(height: 24),

                    // إنتاجية الأعضاء
                    _buildMemberProductivity(),
                  ],
                ),
              );
  }

  /// بناء ملخص المهام
  Widget _buildTasksSummary() {
    // حساب عدد المهام حسب الحالة
    final pendingTasks = _departmentTasks.where((task) => task.status == TaskStatus.pending).length;
    final inProgressTasks = _departmentTasks.where((task) => task.status == TaskStatus.inProgress).length;
    final completedTasks = _departmentTasks.where((task) => task.status == TaskStatus.completed).length;
    // حساب عدد المهام الملغاة (غير مستخدم حالياً في الواجهة)
    // final cancelledTasks = _departmentTasks.where((task) => task.status == TaskStatus.cancelled).length;
    final waitingForInfoTasks = _departmentTasks.where((task) => task.status == TaskStatus.waitingForInfo).length;

    // حساب متوسط نسبة الإكمال
    final avgCompletion = _departmentTasks.isEmpty
        ? 0.0
        : _departmentTasks.fold(0.0, (sum, task) => sum + task.completionPercentage) / _departmentTasks.length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص المهام',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي المهام',
                    _departmentTasks.length.toString(),
                    Icons.task_alt,
                    AppColors.primary,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'متوسط الإكمال',
                    '${avgCompletion.toStringAsFixed(1)}%',
                    Icons.percent,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'قيد التنفيذ',
                    inProgressTasks.toString(),
                    Icons.play_circle_outline,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'مكتملة',
                    completedTasks.toString(),
                    Icons.check_circle_outline,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'قيد الانتظار',
                    pendingTasks.toString(),
                    Icons.pending_outlined,
                    Colors.grey,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'في انتظار معلومات',
                    waitingForInfoTasks.toString(),
                    Icons.help_outline,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر ملخص
  Widget _buildSummaryItem(String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 32,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  /// بناء توزيع المهام حسب الحالة
  Widget _buildTaskStatusDistribution() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'توزيع المهام حسب الحالة',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: _buildTaskStatusChart(),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء مخطط توزيع المهام حسب الحالة
  Widget _buildTaskStatusChart() {
    // حساب عدد المهام حسب الحالة
    final pendingTasks = _departmentTasks.where((task) => task.status == TaskStatus.pending).length;
    final inProgressTasks = _departmentTasks.where((task) => task.status == TaskStatus.inProgress).length;
    final completedTasks = _departmentTasks.where((task) => task.status == TaskStatus.completed).length;
    // حساب عدد المهام الملغاة (مستخدم في المخطط البياني)
    final cancelledTasks = _departmentTasks.where((task) => task.status == TaskStatus.cancelled).length;
    final waitingForInfoTasks = _departmentTasks.where((task) => task.status == TaskStatus.waitingForInfo).length;

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _departmentTasks.length.toDouble(),
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            // تنسيق التلميح
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              String status;
              switch (groupIndex) {
                case 0:
                  status = 'قيد الانتظار';
                  break;
                case 1:
                  status = 'قيد التنفيذ';
                  break;
                case 2:
                  status = 'مكتملة';
                  break;
                case 3:
                  status = 'ملغاة';
                  break;
                case 4:
                  status = 'في انتظار معلومات';
                  break;
                default:
                  status = '';
              }
              return BarTooltipItem(
                '$status\n${rod.toY.toInt()}',
                const TextStyle(color: Colors.white),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                String text;
                switch (value.toInt()) {
                  case 0:
                    text = 'انتظار';
                    break;
                  case 1:
                    text = 'تنفيذ';
                    break;
                  case 2:
                    text = 'مكتملة';
                    break;
                  case 3:
                    text = 'ملغاة';
                    break;
                  case 4:
                    text = 'معلقة';
                    break;
                  default:
                    text = '';
                }
                return Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    text,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value % 1 != 0) return const Text('');
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                );
              },
            ),
          ),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        gridData: FlGridData(
          show: true,
          horizontalInterval: 1,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.shade300,
              strokeWidth: 1,
            );
          },
        ),
        borderData: FlBorderData(
          show: false,
        ),
        barGroups: [
          BarChartGroupData(
            x: 0,
            barRods: [
              BarChartRodData(
                toY: pendingTasks.toDouble(),
                color: Colors.grey,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          ),
          BarChartGroupData(
            x: 1,
            barRods: [
              BarChartRodData(
                toY: inProgressTasks.toDouble(),
                color: Colors.blue,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          ),
          BarChartGroupData(
            x: 2,
            barRods: [
              BarChartRodData(
                toY: completedTasks.toDouble(),
                color: Colors.green,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          ),
          BarChartGroupData(
            x: 3,
            barRods: [
              BarChartRodData(
                toY: cancelledTasks.toDouble(),
                color: Colors.red,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          ),
          BarChartGroupData(
            x: 4,
            barRods: [
              BarChartRodData(
                toY: waitingForInfoTasks.toDouble(),
                color: Colors.orange,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء إنتاجية الأعضاء
  Widget _buildMemberProductivity() {
    if (_members.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إنتاجية الأعضاء',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _members.length,
              itemBuilder: (context, index) {
                final member = _members[index];
                final productivity = _userProductivity[member.id] ?? 0.0;

                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: AppColors.primary,
                            radius: 16,
                            child: Text(
                              member.name.substring(0, 1).toUpperCase(),
                              style: const TextStyle(color: Colors.white),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              member.name,
                              style: AppStyles.titleSmall,
                            ),
                          ),
                          Text(
                            '${productivity.toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _getProductivityColor(productivity),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: productivity / 100,
                        backgroundColor: Colors.grey.shade200,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _getProductivityColor(productivity),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على لون الإنتاجية
  Color _getProductivityColor(double productivity) {
    if (productivity >= 75) {
      return Colors.green;
    } else if (productivity >= 50) {
      return Colors.blue;
    } else if (productivity >= 25) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
