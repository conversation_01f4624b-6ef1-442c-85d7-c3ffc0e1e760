import 'package:get/get.dart';
import '../services/notification_service.dart';

class NotificationController extends GetxController {
  final NotificationService _notificationService = NotificationService();

  final RxList<Notification> notifications = <Notification>[].obs;
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;

  int get unreadCount => notifications.where((n) => !n.isRead).length;

  // Load notifications for user
  Future<void> loadNotifications(String userId) async {
    isLoading.value = true;

    try {
      notifications.value =
          await _notificationService.getNotificationsForUser(userId);
      error.value = '';
    } catch (e) {
      error.value = e.toString();
      notifications.value = [];
    } finally {
      isLoading.value = false;
    }
  }

  // Mark notification as read
  Future<bool> markAsRead(String notificationId) async {
    isLoading.value = true;
    error.value = '';

    try {
      final result =
          await _notificationService.markNotificationAsRead(notificationId);
      if (result) {
        final index = notifications.indexWhere((n) => n.id == notificationId);
        if (index >= 0) {
          final notification = notifications[index];
          notifications[index] = notification.copyWith(isRead: true);
        }
        return true;
      } else {
        error.value = 'Failed to mark notification as read';
        return false;
      }
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Mark all notifications as read
  Future<bool> markAllAsRead(String userId) async {
    isLoading.value = true;
    error.value = '';

    try {
      final result =
          await _notificationService.markAllNotificationsAsRead(userId);
      if (result) {
        final updatedNotifications =
            notifications.map((n) => n.copyWith(isRead: true)).toList();
        notifications.value = updatedNotifications;
        return true;
      } else {
        error.value = 'Failed to mark all notifications as read';
        return false;
      }
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Delete notification
  Future<bool> deleteNotification(String notificationId) async {
    isLoading.value = true;
    error.value = '';

    try {
      final result =
          await _notificationService.deleteNotification(notificationId);
      if (result) {
        notifications.removeWhere((n) => n.id == notificationId);
        return true;
      } else {
        error.value = 'Failed to delete notification';
        return false;
      }
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Delete all notifications for a user
  Future<bool> deleteAllNotifications(String userId) async {
    isLoading.value = true;
    error.value = '';

    try {
      final result = await _notificationService.deleteAllNotifications(userId);
      if (result) {
        notifications.clear();
        return true;
      } else {
        error.value = 'Failed to delete notifications';
        return false;
      }
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Clear error
  void clearError() {
    error.value = '';
  }
}
