import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../models/custom_role_model.dart';
import '../../models/permission_model.dart';
import '../../database/custom_role_repository.dart';
import '../../database/custom_permission_repository.dart';
import 'interface_permissions_tab.dart';

/// تبويب إدارة الأدوار
///
/// يتيح للمدير إنشاء وتعديل وحذف الأدوار المخصصة
class RoleManagementTab extends StatefulWidget {
  const RoleManagementTab({super.key});

  @override
  State<RoleManagementTab> createState() => _RoleManagementTabState();
}

class _RoleManagementTabState extends State<RoleManagementTab>
    with SingleTickerProviderStateMixin {
  final CustomRoleRepository _roleRepository = CustomRoleRepository();
  final CustomPermissionRepository _permissionRepository =
      CustomPermissionRepository();

  late TabController _tabController;
  List<CustomRole> _roles = [];
  CustomRole? _selectedRole;
  bool _isLoading = true;
  String? _errorMessage;

  // خرائط لتخزين حالة الصلاحيات
  final Map<String, bool> _permissionStatus = <String, bool>{};
  final Map<String, bool> _interfacePermissions = <String, bool>{};

  // قائمة الواجهات مع أسمائها العربية
  final Map<String, String> _interfaceNames = {
    'tasks': 'المهام',
    'dashboard': 'لوحة المعلومات',
    'messages': 'الرسائل',
    'notifications': 'الإشعارات',
    'departments': 'الأقسام',
    'users': 'المستخدمين',
    'reports': 'التقارير',
    'settings': 'الإعدادات',
    'admin': 'الإدارة',
    'calendar': 'التقويم',
    'power_bi': 'تقارير Power BI',
    'database': 'إدارة قاعدة البيانات',
    'user-dashboard': 'لوحة تحكم المستخدم',
    'gantt_chart': 'مخطط جانت',
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadRoles();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل الأدوار
  Future<void> _loadRoles() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final roles = await _roleRepository.getAllRoles();
      setState(() {
        _roles = roles;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل الأدوار: $e';
        _isLoading = false;
      });
    }
  }

  /// تحميل صلاحيات الدور
  Future<void> _loadRolePermissions(String roleId) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل صلاحيات الدور
      final permissions =
          await _permissionRepository.getCustomRolePermissions(roleId);

      // إعادة تعيين الخرائط
      _permissionStatus.clear();
      _interfacePermissions.clear();

      // تهيئة حالة الصلاحيات
      for (var type in PermissionType.values) {
        for (var scope in PermissionScope.values) {
          final key = '${type.index}_${scope.index}';
          final permission = permissions.firstWhere(
            (p) =>
                p.type == type && p.scope == scope && p.customRoleId == roleId,
            orElse: () => Permission(
              id: '',
              customRoleId: roleId,
              type: type,
              scope: scope,
              isGranted: false,
              createdAt: DateTime.now(),
            ),
          );

          _permissionStatus[key] = permission.isGranted;

          // إذا كانت صلاحية واجهة، نخزنها في قائمة منفصلة
          if (scope == PermissionScope.interfaces &&
              type == PermissionType.view) {
            // استخدام الوصف كمفتاح للواجهة
            if (permission.description != null) {
              _interfacePermissions[permission.description!] =
                  permission.isGranted;
            }
          }
        }
      }

      // تهيئة صلاحيات الواجهات غير الموجودة
      for (final interface in _interfaceNames.keys) {
        if (!_interfacePermissions.containsKey(interface)) {
          _interfacePermissions[interface] = false;
        }
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل صلاحيات الدور: $e';
        _isLoading = false;
      });
    }
  }

  /// حفظ صلاحيات الدور
  Future<void> _saveRolePermissions() async {
    if (_selectedRole == null) return;

    // عرض مؤشر التحميل
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    try {
      int changesCount = 0;

      // حفظ الصلاحيات العادية
      for (var type in PermissionType.values) {
        for (var scope in PermissionScope.values) {
          final key = '${type.index}_${scope.index}';
          final isGranted = _permissionStatus[key] ?? false;

          if (isGranted) {
            await _permissionRepository.grantCustomRolePermission(
              _selectedRole!.id,
              type,
              scope,
            );
          } else {
            await _permissionRepository.revokeCustomRolePermission(
              _selectedRole!.id,
              type,
              scope,
            );
          }
          changesCount++;
        }
      }

      // حفظ صلاحيات الواجهات
      for (final entry in _interfacePermissions.entries) {
        final interface = entry.key;
        final isGranted = entry.value;

        if (isGranted) {
          await _permissionRepository.grantCustomRolePermission(
            _selectedRole!.id,
            PermissionType.view,
            PermissionScope.interfaces,
            description: interface,
          );
        } else {
          await _permissionRepository.revokeCustomRolePermission(
            _selectedRole!.id,
            PermissionType.view,
            PermissionScope.interfaces,
          );
        }
        changesCount++;
      }

      // إعادة تحميل صلاحيات الدور
      await _loadRolePermissions(_selectedRole!.id);

      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة نجاح
      Get.snackbar(
        'تم بنجاح',
        'تم حفظ صلاحيات الدور بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حفظ صلاحيات الدور: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// إنشاء دور جديد
  Future<void> _createRole() async {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('إنشاء دور جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم الدور',
                hintText: 'أدخل اسم الدور',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف الدور',
                hintText: 'أدخل وصف الدور',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يجب إدخال اسم الدور',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }
              Get.back(result: true);
            },
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        final role = CustomRole.create(
          name: nameController.text,
          description: descriptionController.text,
        );

        await _roleRepository.createRole(role);
        await _loadRoles();

        Get.snackbar(
          'تم بنجاح',
          'تم إنشاء الدور بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء إنشاء الدور: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// تعديل دور
  Future<void> _editRole(CustomRole role) async {
    final TextEditingController nameController =
        TextEditingController(text: role.name);
    final TextEditingController descriptionController =
        TextEditingController(text: role.description);

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('تعديل الدور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'اسم الدور',
                hintText: 'أدخل اسم الدور',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف الدور',
                hintText: 'أدخل وصف الدور',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.isEmpty) {
                Get.snackbar(
                  'خطأ',
                  'يجب إدخال اسم الدور',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
                return;
              }
              Get.back(result: true);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        final updatedRole = role.copyWith(
          name: nameController.text,
          description: descriptionController.text,
          updatedAt: DateTime.now(),
        );

        await _roleRepository.updateRole(updatedRole);
        await _loadRoles();

        Get.snackbar(
          'تم بنجاح',
          'تم تعديل الدور بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء تعديل الدور: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// حذف دور
  Future<void> _deleteRole(CustomRole role) async {
    if (role.isSystem) {
      Get.snackbar(
        'خطأ',
        'لا يمكن حذف أدوار النظام',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('حذف الدور'),
        content: Text('هل أنت متأكد من حذف الدور "${role.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (result == true) {
      try {
        await _roleRepository.deleteRole(role.id);

        // إذا كان الدور المحذوف هو المحدد حاليًا، نقوم بإلغاء تحديده
        if (_selectedRole?.id == role.id) {
          setState(() {
            _selectedRole = null;
          });
        }

        await _loadRoles();

        Get.snackbar(
          'تم بنجاح',
          'تم حذف الدور بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء حذف الدور: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // قائمة الأدوار
        SizedBox(
          width: 300,
          child: Card(
            margin: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'الأدوار',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.add),
                        tooltip: 'إنشاء دور جديد',
                        onPressed: _createRole,
                      ),
                    ],
                  ),
                ),
                const Divider(),
                Expanded(
                  child: _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : _errorMessage != null
                          ? Center(
                              child: Text(_errorMessage!,
                                  style: const TextStyle(color: Colors.red)))
                          : _roles.isEmpty
                              ? const Center(child: Text('لا توجد أدوار'))
                              : ListView.builder(
                                  itemCount: _roles.length,
                                  itemBuilder: (context, index) {
                                    final role = _roles[index];
                                    final isSelected =
                                        _selectedRole?.id == role.id;

                                    return ListTile(
                                      leading: CircleAvatar(
                                        backgroundColor: isSelected
                                            ? Colors.blue
                                            : Colors.grey.shade200,
                                        child: Text(
                                          role.name.substring(0, 1),
                                          style: TextStyle(
                                            color: isSelected
                                                ? Colors.white
                                                : Colors.black,
                                          ),
                                        ),
                                      ),
                                      title: Text(role.name),
                                      subtitle: Text(
                                        role.isSystem ? 'دور نظام' : 'دور مخصص',
                                        style: TextStyle(
                                          color: role.isSystem
                                              ? Colors.blue
                                              : Colors.grey,
                                          fontSize: 12,
                                        ),
                                      ),
                                      selected: isSelected,
                                      trailing: role.isSystem
                                          ? null
                                          : PopupMenuButton<String>(
                                              onSelected: (value) {
                                                if (value == 'edit') {
                                                  _editRole(role);
                                                } else if (value == 'delete') {
                                                  _deleteRole(role);
                                                }
                                              },
                                              itemBuilder: (context) => [
                                                const PopupMenuItem<String>(
                                                  value: 'edit',
                                                  child: Row(
                                                    children: [
                                                      Icon(Icons.edit),
                                                      SizedBox(width: 8),
                                                      Text('تعديل'),
                                                    ],
                                                  ),
                                                ),
                                                const PopupMenuItem<String>(
                                                  value: 'delete',
                                                  child: Row(
                                                    children: [
                                                      Icon(Icons.delete,
                                                          color: Colors.red),
                                                      SizedBox(width: 8),
                                                      Text('حذف',
                                                          style: TextStyle(
                                                              color:
                                                                  Colors.red)),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                      onTap: () {
                                        setState(() {
                                          _selectedRole = role;
                                        });
                                        _loadRolePermissions(role.id);
                                      },
                                    );
                                  },
                                ),
                ),
              ],
            ),
          ),
        ),

        // تفاصيل الدور وصلاحياته
        Expanded(
          child: _selectedRole == null
              ? const Center(
                  child: Text('اختر دور لإدارة صلاحياته'),
                )
              : Card(
                  margin: const EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // معلومات الدور
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                CircleAvatar(
                                  radius: 24,
                                  backgroundColor: Colors.blue,
                                  child: Text(
                                    _selectedRole!.name.substring(0, 1),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 20,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _selectedRole!.name,
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      _selectedRole!.description ?? '',
                                      style: const TextStyle(
                                        color: Colors.grey,
                                      ),
                                    ),
                                    Text(
                                      _selectedRole!.isSystem
                                          ? 'دور نظام'
                                          : 'دور مخصص',
                                      style: TextStyle(
                                        color: _selectedRole!.isSystem
                                            ? Colors.blue
                                            : Colors.grey,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            // زر حفظ الصلاحيات
                            IconButton(
                              icon: const Icon(Icons.save),
                              tooltip: 'حفظ الصلاحيات',
                              onPressed: _saveRolePermissions,
                            ),
                          ],
                        ),
                      ),
                      const Divider(),

                      // تبويبات الصلاحيات
                      TabBar(
                        controller: _tabController,
                        labelColor: Get.isDarkMode ? Colors.white : Colors.blue,
                        unselectedLabelColor: Get.isDarkMode
                            ? Colors.grey[300]
                            : Colors.grey[700],
                        indicatorColor:
                            Get.isDarkMode ? Colors.white : Colors.blue,
                        tabs: const [
                          Tab(text: 'صلاحيات المهام والبيانات'),
                          Tab(text: 'صلاحيات الواجهات'),
                        ],
                      ),

                      // محتوى التبويبات
                      Expanded(
                        child: TabBarView(
                          controller: _tabController,
                          children: [
                            _buildDataPermissionsTab(),
                            _buildInterfacePermissionsTab(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
        ),
      ],
    );
  }

  /// بناء تبويب صلاحيات البيانات
  Widget _buildDataPermissionsTab() {
    // تصفية أنواع الصلاحيات المهمة للبيانات
    final relevantTypes = [
      PermissionType.view,
      PermissionType.create,
      PermissionType.edit,
      PermissionType.delete,
      PermissionType.approve,
      PermissionType.viewAll,
      PermissionType.viewOwn,
    ];

    // تصفية مجالات الصلاحيات المهمة للبيانات
    final relevantScopes = [
      PermissionScope.tasks,
      PermissionScope.users,
      PermissionScope.departments,
      PermissionScope.messages,
      PermissionScope.reports,
      PermissionScope.ganttChart,
      PermissionScope.powerBI,
      PermissionScope.calendar,
    ];

    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'صلاحيات البيانات',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Get.isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                    Row(
                      children: [
                        ElevatedButton.icon(
                          icon:
                              const Icon(Icons.check_circle_outline, size: 16),
                          label: const Text('منح الكل'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Get.isDarkMode
                                ? Colors.green[700]
                                : Colors.green,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            textStyle: const TextStyle(fontSize: 12),
                          ),
                          onPressed: () {
                            setState(() {
                              for (var type in relevantTypes) {
                                for (var scope in relevantScopes) {
                                  final key = '${type.index}_${scope.index}';
                                  _permissionStatus[key] = true;
                                }
                              }
                            });
                          },
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          icon: const Icon(Icons.cancel_outlined, size: 16),
                          label: const Text('إلغاء الكل'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Get.isDarkMode ? Colors.red[700] : Colors.red,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            textStyle: const TextStyle(fontSize: 12),
                          ),
                          onPressed: () {
                            setState(() {
                              for (var type in relevantTypes) {
                                for (var scope in relevantScopes) {
                                  final key = '${type.index}_${scope.index}';
                                  _permissionStatus[key] = false;
                                }
                              }
                            });
                          },
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // جدول الصلاحيات
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: DataTable(
                    columnSpacing: 16,
                    columns: [
                      const DataColumn(label: Text('المجال')),
                      ...relevantTypes.map((type) {
                        return DataColumn(
                            label: Text(_getPermissionTypeText(type)));
                      }),
                    ],
                    rows: relevantScopes.map((scope) {
                      return DataRow(
                        cells: [
                          DataCell(Text(_getPermissionScopeText(scope))),
                          ...relevantTypes.map((type) {
                            final key = '${type.index}_${scope.index}';
                            return DataCell(
                              Checkbox(
                                value: _permissionStatus[key] ?? false,
                                onChanged: (value) {
                                  setState(() {
                                    _permissionStatus[key] = value ?? false;
                                  });
                                },
                              ),
                            );
                          }),
                        ],
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
          );
  }

  /// بناء تبويب صلاحيات الواجهات
  Widget _buildInterfacePermissionsTab() {
    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'الوصول إلى الواجهات',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Get.isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                    Row(
                      children: [
                        ElevatedButton.icon(
                          icon:
                              const Icon(Icons.check_circle_outline, size: 16),
                          label: const Text('منح الكل'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Get.isDarkMode
                                ? Colors.green[700]
                                : Colors.green,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            textStyle: const TextStyle(fontSize: 12),
                          ),
                          onPressed: () {
                            setState(() {
                              for (final interface in _interfaceNames.keys) {
                                _interfacePermissions[interface] = true;
                              }
                            });
                          },
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          icon: const Icon(Icons.cancel_outlined, size: 16),
                          label: const Text('إلغاء الكل'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Get.isDarkMode ? Colors.red[700] : Colors.red,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            textStyle: const TextStyle(fontSize: 12),
                          ),
                          onPressed: () {
                            setState(() {
                              for (final interface in _interfaceNames.keys) {
                                _interfacePermissions[interface] = false;
                              }
                            });
                          },
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // قائمة الواجهات
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _interfaceNames.length,
                  itemBuilder: (context, index) {
                    final interface = _interfaceNames.keys.elementAt(index);
                    final interfaceName = _interfaceNames[interface];

                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: CheckboxListTile(
                        title: Text(interfaceName ?? interface),
                        value: _interfacePermissions[interface] ?? false,
                        onChanged: (value) {
                          setState(() {
                            _interfacePermissions[interface] = value ?? false;
                          });
                        },
                        secondary: Icon(_getInterfaceIcon(interface)),
                      ),
                    );
                  },
                ),
              ],
            ),
          );
  }

  /// الحصول على نص نوع الصلاحية
  String _getPermissionTypeText(PermissionType type) {
    switch (type) {
      case PermissionType.view:
        return 'عرض';
      case PermissionType.create:
        return 'إنشاء';
      case PermissionType.edit:
        return 'تعديل';
      case PermissionType.delete:
        return 'حذف';
      case PermissionType.approve:
        return 'موافقة';
      case PermissionType.viewAll:
        return 'عرض الكل';
      case PermissionType.viewOwn:
        return 'عرض الخاص';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على نص مجال الصلاحية
  String _getPermissionScopeText(PermissionScope scope) {
    switch (scope) {
      case PermissionScope.tasks:
        return 'المهام';
      case PermissionScope.users:
        return 'المستخدمين';
      case PermissionScope.departments:
        return 'الأقسام';
      case PermissionScope.messages:
        return 'الرسائل';
      case PermissionScope.reports:
        return 'التقارير';
      case PermissionScope.ganttChart:
        return 'مخطط جانت';
      case PermissionScope.powerBI:
        return 'تقارير باور بي آي';
      case PermissionScope.calendar:
        return 'التقويم';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على أيقونة الواجهة
  IconData _getInterfaceIcon(String interface) {
    switch (interface) {
      case 'tasks':
        return Icons.task;
      case 'dashboard':
        return Icons.dashboard;
      case 'messages':
        return Icons.message;
      case 'notifications':
        return Icons.notifications;
      case 'departments':
        return Icons.business;
      case 'users':
        return Icons.people;
      case 'reports':
        return Icons.assessment;
      case 'settings':
        return Icons.settings;
      case 'admin':
        return Icons.admin_panel_settings;
      case 'calendar':
        return Icons.calendar_today;
      case 'power_bi':
        return Icons.bar_chart;
      case 'gantt_chart':
        return Icons.stacked_bar_chart;
      default:
        return Icons.web;
    }
  }
}
