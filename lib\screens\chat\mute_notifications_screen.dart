import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../database/notification_settings_repository.dart';
import '../../models/chat_group_model.dart';
import '../../models/notification_settings_model.dart';
import 'package:uuid/uuid.dart';

class MuteNotificationsScreen extends StatefulWidget {
  final ChatGroup chatGroup;

  const MuteNotificationsScreen({
    super.key,
    required this.chatGroup,
  });

  @override
  State<MuteNotificationsScreen> createState() => _MuteNotificationsScreenState();
}

class _MuteNotificationsScreenState extends State<MuteNotificationsScreen> {
  final _authController = Get.find<AuthController>();
  final _notificationSettingsRepository = NotificationSettingsRepository();
  final _uuid = const Uuid();

  NotificationMuteType _selectedMuteType = NotificationMuteType.none;
  bool _isLoading = true;
  NotificationSettings? _currentSettings;
  final _dateFormat = DateFormat('dd/MM/yyyy HH:mm');

  @override
  void initState() {
    super.initState();
    _loadCurrentSettings();
  }

  Future<void> _loadCurrentSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) return;

      _currentSettings = await _notificationSettingsRepository.getNotificationSettingsForUserAndGroup(
        currentUser.id,
        widget.chatGroup.id,
      );

      if (_currentSettings != null && _currentSettings!.isActive) {
        _selectedMuteType = _currentSettings!.muteType;
      } else {
        _selectedMuteType = NotificationMuteType.none;
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load notification settings: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) return;

      DateTime? muteUntil;
      final now = DateTime.now();

      // Calculate mute until date based on selected type
      switch (_selectedMuteType) {
        case NotificationMuteType.oneHour:
          muteUntil = now.add(const Duration(hours: 1));
          break;
        case NotificationMuteType.eightHours:
          muteUntil = now.add(const Duration(hours: 8));
          break;
        case NotificationMuteType.oneDay:
          muteUntil = now.add(const Duration(days: 1));
          break;
        case NotificationMuteType.oneWeek:
          muteUntil = now.add(const Duration(days: 7));
          break;
        case NotificationMuteType.always:
        case NotificationMuteType.none:
          muteUntil = null;
          break;
      }

      final settings = NotificationSettings(
        id: _currentSettings?.id ?? _uuid.v4(),
        userId: currentUser.id,
        groupId: widget.chatGroup.id,
        muteType: _selectedMuteType,
        muteUntil: muteUntil,
        isActive: _selectedMuteType != NotificationMuteType.none,
        createdAt: _currentSettings?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _notificationSettingsRepository.saveNotificationSettings(settings);

      Get.back();
      Get.snackbar(
        'Success',
        'Notification settings updated',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to save notification settings: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('كتم الإشعارات'.tr),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            tooltip: 'معلومات'.tr,
            onPressed: _showInfoDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // معلومات المجموعة
                _buildGroupInfo(),

                // خيارات كتم الإشعارات
                Expanded(
                  child: ListView(
                    children: [
                      // عنوان القسم
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Text(
                          'خيارات كتم الإشعارات'.tr,
                          style: AppStyles.titleMedium,
                        ),
                      ),

                      // خيارات كتم الإشعارات
                      _buildMuteOption(
                        title: 'عدم الكتم'.tr,
                        subtitle: 'استلام جميع الإشعارات'.tr,
                        value: NotificationMuteType.none,
                        icon: Icons.notifications_active,
                      ),
                      const Divider(height: 1),
                      _buildMuteOption(
                        title: 'لمدة ساعة واحدة'.tr,
                        subtitle: _getMuteUntilText(NotificationMuteType.oneHour),
                        value: NotificationMuteType.oneHour,
                        icon: Icons.access_time,
                      ),
                      const Divider(height: 1),
                      _buildMuteOption(
                        title: 'لمدة 8 ساعات'.tr,
                        subtitle: _getMuteUntilText(NotificationMuteType.eightHours),
                        value: NotificationMuteType.eightHours,
                        icon: Icons.access_time,
                      ),
                      const Divider(height: 1),
                      _buildMuteOption(
                        title: 'لمدة يوم واحد'.tr,
                        subtitle: _getMuteUntilText(NotificationMuteType.oneDay),
                        value: NotificationMuteType.oneDay,
                        icon: Icons.calendar_today,
                      ),
                      const Divider(height: 1),
                      _buildMuteOption(
                        title: 'لمدة أسبوع'.tr,
                        subtitle: _getMuteUntilText(NotificationMuteType.oneWeek),
                        value: NotificationMuteType.oneWeek,
                        icon: Icons.date_range,
                      ),
                      const Divider(height: 1),
                      _buildMuteOption(
                        title: 'دائماً'.tr,
                        subtitle: 'لن يتم استلام أي إشعارات من هذه المحادثة'.tr,
                        value: NotificationMuteType.always,
                        icon: Icons.notifications_off,
                      ),

                      // معلومات الكتم الحالي
                      if (_currentSettings != null && _currentSettings!.isActive)
                        _buildCurrentMuteInfo(),
                    ],
                  ),
                ),

                // زر الحفظ
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: ElevatedButton(
                    onPressed: _saveSettings,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white, // الأبيض مناسب هنا لأن الخلفية ملونة
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      minimumSize: const Size(double.infinity, 48),
                    ),
                    child: Text('حفظ الإعدادات'.tr),
                  ),
                ),
              ],
            ),
    );
  }

  /// بناء معلومات المجموعة
  Widget _buildGroupInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.card,
        boxShadow: [
          BoxShadow(
            color: AppColors.getShadowColor(0.04),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // صورة المجموعة
          CircleAvatar(
            radius: 24,
            backgroundColor: widget.chatGroup.isDirectMessage
                ? AppColors.accent
                : AppColors.primary,
            child: widget.chatGroup.avatarUrl != null
                ? Image.network(widget.chatGroup.avatarUrl!)
                : Text(
                    widget.chatGroup.name.substring(0, 1).toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white, // الأبيض مناسب هنا لأن الخلفية ملونة
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
          const SizedBox(width: 16),

          // معلومات المجموعة
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.chatGroup.name,
                  style: AppStyles.titleMedium,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  widget.chatGroup.isDirectMessage
                      ? 'محادثة مباشرة'.tr
                      : 'مجموعة محادثة'.tr,
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // مؤشر حالة الكتم
          if (_currentSettings != null && _currentSettings!.isActive)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Get.isDarkMode ? const Color(0xFF3E2723) : const Color(0xFFFFEBEE),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.notifications_off,
                    size: 16,
                    color: Get.isDarkMode ? const Color(0xFFEF9A9A) : const Color(0xFFB71C1C),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'مكتوم'.tr,
                    style: TextStyle(
                      fontSize: 12,
                      color: Get.isDarkMode ? const Color(0xFFEF9A9A) : const Color(0xFFB71C1C),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// بناء خيار كتم الإشعارات
  Widget _buildMuteOption({
    required String title,
    required String subtitle,
    required NotificationMuteType value,
    required IconData icon,
  }) {
    return ListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      leading: Icon(icon, color: AppColors.primary),
      trailing: Radio<NotificationMuteType>(
        value: value,
        groupValue: _selectedMuteType,
        activeColor: AppColors.primary,
        onChanged: (newValue) {
          setState(() {
            _selectedMuteType = newValue!;
          });
        },
      ),
      onTap: () {
        setState(() {
          _selectedMuteType = value;
        });
      },
    );
  }

  /// بناء معلومات الكتم الحالي
  Widget _buildCurrentMuteInfo() {
    if (_currentSettings == null || !_currentSettings!.isActive) {
      return const SizedBox.shrink();
    }

    // استخدام حالة الكتم من المستودع

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? const Color(0xFF0D2C40) : const Color(0xFFE3F2FD),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Get.isDarkMode ? const Color(0xFF1565C0) : const Color(0xFFBBDEFB),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Get.isDarkMode ? const Color(0xFF64B5F6) : const Color(0xFF1565C0),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'معلومات الكتم الحالي'.tr,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Get.isDarkMode ? const Color(0xFF64B5F6) : const Color(0xFF1565C0),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _currentSettings!.muteType == NotificationMuteType.always
                ? 'الإشعارات مكتومة دائماً لهذه المحادثة'.tr
                : _currentSettings!.muteUntil != null
                    ? 'الإشعارات مكتومة حتى: ${_dateFormat.format(_currentSettings!.muteUntil!)}'.tr
                    : 'الإشعارات مكتومة'.tr,
            style: TextStyle(
              color: Get.isDarkMode ? const Color(0xFF64B5F6) : const Color(0xFF1565C0),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار المعلومات
  void _showInfoDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('معلومات عن كتم الإشعارات'.tr),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'عند كتم الإشعارات:'.tr,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            _buildInfoItem(
              'لن تتلقى إشعارات للرسائل الجديدة في هذه المحادثة'.tr,
            ),
            _buildInfoItem(
              'ستظل الرسائل مرئية في المحادثة'.tr,
            ),
            _buildInfoItem(
              'يمكنك إلغاء الكتم في أي وقت'.tr,
            ),
            const SizedBox(height: 16),
            Text(
              'ملاحظة: سيتم تطبيق إعدادات الكتم على جميع أجهزتك'.tr,
              style: TextStyle(
                fontStyle: FontStyle.italic,
                color: AppColors.textSecondary,
                fontSize: 12,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('إغلاق'.tr),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر معلومات
  Widget _buildInfoItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }

  /// الحصول على نص وقت انتهاء الكتم
  String _getMuteUntilText(NotificationMuteType muteType) {
    final now = DateTime.now();
    DateTime? muteUntil;

    switch (muteType) {
      case NotificationMuteType.oneHour:
        muteUntil = now.add(const Duration(hours: 1));
        break;
      case NotificationMuteType.eightHours:
        muteUntil = now.add(const Duration(hours: 8));
        break;
      case NotificationMuteType.oneDay:
        muteUntil = now.add(const Duration(days: 1));
        break;
      case NotificationMuteType.oneWeek:
        muteUntil = now.add(const Duration(days: 7));
        break;
      default:
        return '';
    }

    return 'حتى ${_dateFormat.format(muteUntil)}'.tr;
  }
}
