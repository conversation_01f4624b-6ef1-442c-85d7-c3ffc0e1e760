import 'package:get/get.dart';
import '../models/task_model.dart';
import '../controllers/task_controller.dart';

/// امتدادات وحدة تحكم المهام
/// تضيف دوال مفقودة مطلوبة لمكتبات Syncfusion
extension TaskControllerExtensions on TaskController {
  /// الحصول على جميع المهام
  Future<List<Task>> getAllTasks() async {
    try {
      // استخدام الدالة الموجودة لتحميل جميع المهام
      await loadAllTasks();

      // إرجاع المهام المحملة
      return tasks;
    } catch (e) {
      // إرجاع قائمة فارغة في حالة حدوث خطأ
      return [];
    }
  }

  /// الحصول على مهمة بواسطة المعرف
  Future<Task?> getTaskById(String taskId) async {
    try {
      // التحقق مما إذا كانت المهمة الحالية هي المطلوبة
      if (currentTask.value?.id == taskId) {
        return currentTask.value;
      }

      // البحث في قائمة المهام المحملة
      final task = tasks.firstWhereOrNull((task) => task.id == taskId);
      if (task != null) {
        return task;
      }

      // تحميل تفاصيل المهمة إذا لم تكن موجودة
      await loadTaskDetails(taskId);
      return currentTask.value;
    } catch (e) {
      // إرجاع null في حالة حدوث خطأ
      return null;
    }
  }

  /// تحديث مهمة
  Future<bool> updateTask(Task task) async {
    try {
      // استخدام الدالة الموجودة لتحديث المهمة
      final result = await updateTaskStatus(
        task.id,
        task.assigneeId ?? '',
        task.status,
        null,
      );

      if (result) {
        // تحديث المهمة في القائمة
        final index = tasks.indexWhere((t) => t.id == task.id);
        if (index >= 0) {
          tasks[index] = task;
        }

        // تحديث المهمة الحالية إذا كانت هي نفسها
        if (currentTask.value?.id == task.id) {
          currentTask.value = task;
        }

        return true;
      }

      return false;
    } catch (e) {
      // إرجاع false في حالة حدوث خطأ
      return false;
    }
  }
}
