import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import '../models/task_type_model.dart';
import '../services/task_type_service.dart';

/// متحكم أنواع المهام
///
/// يوفر وظائف للتعامل مع أنواع المهام في واجهة المستخدم
class TaskTypeController extends GetxController {
  final TaskTypeService _taskTypeService = TaskTypeService();
  final Uuid _uuid = const Uuid();

  // قائمة أنواع المهام
  final RxList<TaskType> taskTypes = <TaskType>[].obs;
  
  // حالة التحميل
  final RxBool isLoading = false.obs;
  
  // رسالة الخطأ
  final RxString errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadTaskTypes();
  }

  /// تحميل جميع أنواع المهام
  Future<void> loadTaskTypes({bool includeInactive = false}) async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final types = await _taskTypeService.getAllTaskTypes(includeInactive: includeInactive);
      taskTypes.assignAll(types);
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل أنواع المهام: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// إنشاء نوع مهمة جديد
  Future<bool> createTaskType({
    required String name,
    required String description,
    String? color,
    String? icon,
  }) async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final newTaskType = TaskType(
        id: _uuid.v4(),
        name: name,
        description: description,
        color: color,
        icon: icon,
        isActive: true,
        createdAt: DateTime.now(),
      );
      
      final createdType = await _taskTypeService.createTaskType(newTaskType);
      taskTypes.add(createdType);
      return true;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إنشاء نوع المهمة: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديث نوع مهمة
  Future<bool> updateTaskType(TaskType taskType) async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final updatedTaskType = taskType.copyWith(
        updatedAt: DateTime.now(),
      );
      
      final success = await _taskTypeService.updateTaskType(updatedTaskType);
      
      if (success) {
        final index = taskTypes.indexWhere((type) => type.id == updatedTaskType.id);
        if (index != -1) {
          taskTypes[index] = updatedTaskType;
        }
      }
      
      return success;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحديث نوع المهمة: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// حذف نوع مهمة
  Future<bool> deleteTaskType(String id) async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final success = await _taskTypeService.deleteTaskType(id);
      
      if (success) {
        taskTypes.removeWhere((type) => type.id == id);
      }
      
      return success;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء حذف نوع المهمة: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// البحث عن أنواع المهام
  Future<List<TaskType>> searchTaskTypes(String query) async {
    if (query.isEmpty) {
      return taskTypes;
    }
    
    try {
      return await _taskTypeService.searchTaskTypes(query);
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء البحث عن أنواع المهام: $e';
      return [];
    }
  }

  /// الحصول على نوع مهمة بواسطة المعرف
  TaskType? getTaskTypeById(String? id) {
    if (id == null) return null;
    
    try {
      return taskTypes.firstWhere((type) => type.id == id);
    } catch (e) {
      return null;
    }
  }
}
