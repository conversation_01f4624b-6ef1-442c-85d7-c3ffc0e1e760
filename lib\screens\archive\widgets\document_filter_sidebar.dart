import 'package:flutter/material.dart';
import 'package:flutter_application_2/controllers/archive_controller.dart';
import 'package:flutter_application_2/models/archive_document_model.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

/// شريط تصفية وثائق الأرشيف
class DocumentFilterSidebar extends StatefulWidget {
  /// وحدة تحكم الأرشيف
  final ArchiveController controller;

  /// هل هو في شكل شريط سفلي
  final bool isBottomSheet;

  const DocumentFilterSidebar({
    super.key,
    required this.controller,
    this.isBottomSheet = false,
  });

  @override
  State<DocumentFilterSidebar> createState() => _DocumentFilterSidebarState();
}

class _DocumentFilterSidebarState extends State<DocumentFilterSidebar> {
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'عوامل التصفية',
                  style: AppStyles.subtitle1,
                ),
                TextButton(
                  onPressed: () {
                    widget.controller.resetFilters();
                  },
                  child: const Text('إعادة تعيين'),
                ),
              ],
            ),
            const Divider(),

            // التصنيفات
            _buildSectionTitle('التصنيف'),
            _buildCategoryFilter(),
            const SizedBox(height: 16),

            // الوسوم
            _buildSectionTitle('الوسوم'),
            _buildTagsFilter(),
            const SizedBox(height: 16),

            // مستوى السرية
            _buildSectionTitle('مستوى السرية'),
            _buildConfidentialityFilter(),
            const SizedBox(height: 16),

            // مستوى الأهمية
            _buildSectionTitle('مستوى الأهمية'),
            _buildImportanceFilter(),
            const SizedBox(height: 16),

            // نطاق التاريخ
            _buildSectionTitle('نطاق التاريخ'),
            _buildDateRangeFilter(),
            const SizedBox(height: 16),

            // الترتيب
            _buildSectionTitle('ترتيب النتائج'),
            _buildSortingFilter(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildCategoryFilter() {
    return GetBuilder<ArchiveController>(
      builder: (controller) {
        if (controller.categories.isEmpty) {
          return const Text('لا توجد تصنيفات');
        }

        return DropdownButtonFormField<String?>(
          value: controller.selectedCategory.value?.id,
          decoration: const InputDecoration(
            hintText: 'اختر تصنيفًا',
            isDense: true,
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            border: OutlineInputBorder(),
          ),
          items: [
            const DropdownMenuItem<String?>(
              value: null,
              child: Text('جميع التصنيفات'),
            ),
            ...controller.categories.map((category) => DropdownMenuItem<String?>(
              value: category.id,
              child: Text(category.name),
            )),
          ],
          onChanged: (value) {
            final category = value != null
                ? controller.categories.firstWhere(
                    (c) => c.id == value,
                    orElse: () => controller.categories.first,
                  )
                : null;
            controller.setSelectedCategory(category);
          },
        );
      },
    );
  }

  Widget _buildTagsFilter() {
    return GetBuilder<ArchiveController>(
      builder: (controller) {
        if (controller.tags.isEmpty) {
          return const Text('لا توجد وسوم');
        }

        return Wrap(
          spacing: 8,
          runSpacing: 8,
          children: controller.tags.map((tag) {
            final isSelected = controller.selectedTags.contains(tag);

            return FilterChip(
              label: Text(tag.name),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  controller.addSelectedTag(tag);
                } else {
                  controller.removeSelectedTag(tag);
                }
              },
              selectedColor: _getTagColor(tag.color).withAlpha(51),
              checkmarkColor: _getTagColor(tag.color),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _buildConfidentialityFilter() {
    return GetBuilder<ArchiveController>(
      builder: (controller) {
        return Wrap(
          spacing: 8,
          runSpacing: 8,
          children: ArchiveDocumentConfidentiality.values.map((confidentiality) {
            final isSelected = controller.selectedConfidentiality.value == confidentiality;

            return FilterChip(
              label: Text(_getConfidentialityText(confidentiality)),
              selected: isSelected,
              onSelected: (selected) {
                controller.setSelectedConfidentiality(
                  selected ? confidentiality : null,
                );
              },
              selectedColor: _getConfidentialityColor(confidentiality).withAlpha(51),
              checkmarkColor: _getConfidentialityColor(confidentiality),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _buildImportanceFilter() {
    return GetBuilder<ArchiveController>(
      builder: (controller) {
        return Wrap(
          spacing: 8,
          runSpacing: 8,
          children: ArchiveDocumentImportance.values.map((importance) {
            final isSelected = controller.selectedImportance.value == importance;

            return FilterChip(
              label: Text(_getImportanceText(importance)),
              selected: isSelected,
              onSelected: (selected) {
                controller.setSelectedImportance(
                  selected ? importance : null,
                );
              },
              selectedColor: _getImportanceColor(importance).withAlpha(51),
              checkmarkColor: _getImportanceColor(importance),
            );
          }).toList(),
        );
      },
    );
  }

  Widget _buildDateRangeFilter() {
    return GetBuilder<ArchiveController>(
      builder: (controller) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // من تاريخ
            InkWell(
              onTap: () => _selectDate(true),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'من تاريخ',
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  border: OutlineInputBorder(),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      controller.fromDate.value != null
                          ? DateFormat('yyyy/MM/dd').format(controller.fromDate.value!)
                          : 'اختر تاريخ',
                      style: controller.fromDate.value != null
                          ? AppStyles.body2
                          : AppStyles.body2.copyWith(color: Colors.grey),
                    ),
                    const Icon(Icons.calendar_today, size: 16),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 8),

            // إلى تاريخ
            InkWell(
              onTap: () => _selectDate(false),
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'إلى تاريخ',
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  border: OutlineInputBorder(),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      controller.toDate.value != null
                          ? DateFormat('yyyy/MM/dd').format(controller.toDate.value!)
                          : 'اختر تاريخ',
                      style: controller.toDate.value != null
                          ? AppStyles.body2
                          : AppStyles.body2.copyWith(color: Colors.grey),
                    ),
                    const Icon(Icons.calendar_today, size: 16),
                  ],
                ),
              ),
            ),

            if (controller.fromDate.value != null || controller.toDate.value != null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: TextButton(
                  onPressed: () {
                    controller.setDateRange(null, null);
                  },
                  child: const Text('مسح التاريخ'),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildSortingFilter() {
    return GetBuilder<ArchiveController>(
      builder: (controller) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // حقل الترتيب
            DropdownButtonFormField<String>(
              value: controller.sortBy.value,
              decoration: const InputDecoration(
                labelText: 'ترتيب حسب',
                isDense: true,
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                border: OutlineInputBorder(),
              ),
              items: [
                const DropdownMenuItem<String>(
                  value: 'createdAt',
                  child: Text('تاريخ الإضافة'),
                ),
                const DropdownMenuItem<String>(
                  value: 'title',
                  child: Text('العنوان'),
                ),
                const DropdownMenuItem<String>(
                  value: 'documentDate',
                  child: Text('تاريخ الوثيقة'),
                ),
                const DropdownMenuItem<String>(
                  value: 'confidentiality',
                  child: Text('مستوى السرية'),
                ),
                const DropdownMenuItem<String>(
                  value: 'importance',
                  child: Text('مستوى الأهمية'),
                ),
              ],
              onChanged: (value) {
                if (value != null) {
                  controller.setSorting(value, controller.sortAscending.value);
                }
              },
            ),
            const SizedBox(height: 8),

            // اتجاه الترتيب
            Row(
              children: [
                Checkbox(
                  value: controller.sortAscending.value,
                  onChanged: (value) {
                    if (value != null) {
                      controller.setSorting(
                        controller.sortBy.value,
                        value,
                      );
                    }
                  },
                ),
                const Text('ترتيب تصاعدي'),
              ],
            ),
          ],
        );
      },
    );
  }

  Future<void> _selectDate(bool isFromDate) async {
    final initialDate = isFromDate
        ? widget.controller.fromDate.value ?? DateTime.now()
        : widget.controller.toDate.value ?? DateTime.now();

    final date = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      if (isFromDate) {
        widget.controller.setDateRange(date, widget.controller.toDate.value);
      } else {
        widget.controller.setDateRange(widget.controller.fromDate.value, date);
      }
    }
  }

  Color _getTagColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceAll('#', '0xff')));
    } catch (e) {
      return Colors.blue;
    }
  }

  String _getConfidentialityText(ArchiveDocumentConfidentiality confidentiality) {
    switch (confidentiality) {
      case ArchiveDocumentConfidentiality.normal:
        return 'عادي';
      case ArchiveDocumentConfidentiality.confidential:
        return 'سري';
      case ArchiveDocumentConfidentiality.highlyConfidential:
        return 'سري للغاية';
      case ArchiveDocumentConfidentiality.topSecret:
        return 'سري جداً';
    }
  }

  Color _getConfidentialityColor(ArchiveDocumentConfidentiality confidentiality) {
    switch (confidentiality) {
      case ArchiveDocumentConfidentiality.normal:
        return Colors.grey;
      case ArchiveDocumentConfidentiality.confidential:
        return Colors.blue;
      case ArchiveDocumentConfidentiality.highlyConfidential:
        return Colors.orange;
      case ArchiveDocumentConfidentiality.topSecret:
        return Colors.red;
    }
  }

  String _getImportanceText(ArchiveDocumentImportance importance) {
    switch (importance) {
      case ArchiveDocumentImportance.low:
        return 'منخفضة';
      case ArchiveDocumentImportance.normal:
        return 'عادية';
      case ArchiveDocumentImportance.high:
        return 'مرتفعة';
      case ArchiveDocumentImportance.urgent:
        return 'عاجلة';
    }
  }

  Color _getImportanceColor(ArchiveDocumentImportance importance) {
    switch (importance) {
      case ArchiveDocumentImportance.low:
        return Colors.grey;
      case ArchiveDocumentImportance.normal:
        return Colors.blue;
      case ArchiveDocumentImportance.high:
        return Colors.orange;
      case ArchiveDocumentImportance.urgent:
        return Colors.red;
    }
  }
}
