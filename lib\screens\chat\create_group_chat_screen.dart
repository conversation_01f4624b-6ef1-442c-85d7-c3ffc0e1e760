import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/message_controller.dart';
import '../../controllers/user_controller.dart';
import '../../models/user_model.dart';
import 'chat_detail_screen.dart';

class CreateGroupChatScreen extends StatefulWidget {
  const CreateGroupChatScreen({super.key});

  @override
  State<CreateGroupChatScreen> createState() => _CreateGroupChatScreenState();
}

class _CreateGroupChatScreenState extends State<CreateGroupChatScreen> {
  final _messageController = Get.find<MessageController>();
  final _authController = Get.find<AuthController>();
  final _userController = Get.find<UserController>();

  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _searchController = TextEditingController();

  final _selectedUsers = <User>[].obs;
  final _isLoading = false.obs;
  final _users = <User>[].obs;
  final _filteredUsers = <User>[].obs;

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    _isLoading.value = true;

    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) return;

      await _userController.loadAllUsers();
      final allUsers = _userController.users;

      // Remove current user from the list
      _users.value = allUsers.where((user) => user.id != currentUser.id).toList();
      _filteredUsers.value = _users;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load users: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  void _filterUsers(String query) {
    if (query.isEmpty) {
      _filteredUsers.value = _users;
    } else {
      _filteredUsers.value = _users.where((user) {
        return user.name.toLowerCase().contains(query.toLowerCase()) ||
               user.email.toLowerCase().contains(query.toLowerCase());
      }).toList();
    }
  }

  void _toggleUserSelection(User user) {
    if (_selectedUsers.contains(user)) {
      _selectedUsers.remove(user);
    } else {
      _selectedUsers.add(user);
    }
  }

  Future<void> _createGroupChat() async {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return;

    final name = _nameController.text.trim();
    final description = _descriptionController.text.trim();

    if (name.isEmpty) {
      Get.snackbar(
        'Error',
        'Group name cannot be empty',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    if (_selectedUsers.isEmpty) {
      Get.snackbar(
        'Error',
        'Please select at least one user',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    _isLoading.value = true;

    try {
      final memberIds = _selectedUsers.map((user) => user.id).toList();
      memberIds.add(currentUser.id); // Add current user

      final group = await _messageController.createGroupChat(
        name,
        description.isEmpty ? null : description,
        currentUser.id,
        memberIds,
      );

      if (group != null) {
        Get.back();
        Get.to(() => ChatDetailScreen(chatGroup: group));

        Get.snackbar(
          'Success',
          'Group chat created successfully',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to create group chat: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> _startDirectChat(User user) async {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return;

    _isLoading.value = true;

    try {
      final group = await _messageController.getOrCreateDirectMessageGroup(
        currentUser.id,
        user.id,
      );

      if (group != null) {
        Get.back();
        Get.to(() => ChatDetailScreen(chatGroup: group));
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to start direct chat: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('New Conversation'),
          bottom: const TabBar(
            tabs: [
              Tab(text: 'Direct Message'),
              Tab(text: 'Group Chat'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            // Direct Message Tab
            _buildDirectMessageTab(),

            // Group Chat Tab
            _buildGroupChatTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildDirectMessageTab() {
    return Column(
      children: [
        // Search bar
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search users',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.getBorderColor()),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onChanged: _filterUsers,
          ),
        ),

        // Users list
        Expanded(
          child: Obx(() {
            if (_isLoading.value) {
              return const Center(child: CircularProgressIndicator());
            }

            if (_filteredUsers.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.person_search,
                      size: 64,
                      color: Get.isDarkMode ? const Color(0xFF424242) : const Color(0xFFDDDDDD),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No users found',
                      style: AppStyles.titleMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: _filteredUsers.length,
              itemBuilder: (context, index) {
                final user = _filteredUsers[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: AppColors.accent,
                    backgroundImage: user.profileImage != null
                        ? NetworkImage(user.profileImage!)
                        : null,
                    child: user.profileImage == null
                        ? Text(
                            user.name.substring(0, 1).toUpperCase(),
                            style: const TextStyle(color: Colors.white), // الأبيض مناسب هنا لأن الخلفية ملونة
                          )
                        : null,
                  ),
                  title: Text(user.name),
                  subtitle: Text(user.email),
                  onTap: () {
                    _startDirectChat(user);
                  },
                );
              },
            );
          }),
        ),
      ],
    );
  }

  Widget _buildGroupChatTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Group info
          TextField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Group Name',
              hintText: 'Enter group name',
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description (Optional)',
              hintText: 'Enter group description',
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 24),

          // Selected users
          Text(
            'Selected Users (${_selectedUsers.length})',
            style: AppStyles.titleSmall,
          ),
          const SizedBox(height: 8),

          Obx(() {
            if (_selectedUsers.isEmpty) {
              return const Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
                child: Text('No users selected'),
              );
            }

            return SizedBox(
              height: 60,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedUsers.length,
                itemBuilder: (context, index) {
                  final user = _selectedUsers[index];
                  return Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: Chip(
                      avatar: CircleAvatar(
                        backgroundColor: AppColors.accent,
                        backgroundImage: user.profileImage != null
                            ? NetworkImage(user.profileImage!)
                            : null,
                        child: user.profileImage == null
                            ? Text(
                                user.name.substring(0, 1).toUpperCase(),
                                style: const TextStyle(color: Colors.white), // الأبيض مناسب هنا لأن الخلفية ملونة
                              )
                            : null,
                      ),
                      label: Text(user.name),
                      deleteIcon: const Icon(Icons.close, size: 16),
                      onDeleted: () {
                        _toggleUserSelection(user);
                      },
                    ),
                  );
                },
              ),
            );
          }),

          const SizedBox(height: 16),

          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search users to add',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.getBorderColor()),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onChanged: _filterUsers,
          ),

          const SizedBox(height: 16),

          // Users list
          Expanded(
            child: Obx(() {
              if (_isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              if (_filteredUsers.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.person_search,
                        size: 64,
                        color: Get.isDarkMode ? const Color(0xFF424242) : const Color(0xFFDDDDDD),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No users found',
                        style: AppStyles.titleMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.symmetric(vertical: 8),
                itemCount: _filteredUsers.length,
                itemBuilder: (context, index) {
                  final user = _filteredUsers[index];
                  final isSelected = _selectedUsers.contains(user);

                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppColors.accent,
                      backgroundImage: user.profileImage != null
                          ? NetworkImage(user.profileImage!)
                          : null,
                      child: user.profileImage == null
                          ? Text(
                              user.name.substring(0, 1).toUpperCase(),
                              style: const TextStyle(color: Colors.white), // الأبيض مناسب هنا لأن الخلفية ملونة
                            )
                          : null,
                    ),
                    title: Text(user.name),
                    subtitle: Text(user.email),
                    trailing: Checkbox(
                      value: isSelected,
                      onChanged: (value) {
                        _toggleUserSelection(user);
                      },
                    ),
                    onTap: () {
                      _toggleUserSelection(user);
                    },
                  );
                },
              );
            }),
          ),

          // Create button
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: SizedBox(
              width: double.infinity,
              child: Obx(() => ElevatedButton(
                onPressed: _isLoading.value ? null : _createGroupChat,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white, // الأبيض مناسب هنا لأن الخلفية ملونة
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading.value
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white), // الأبيض مناسب هنا لأن الخلفية ملونة
                        ),
                      )
                    : const Text('Create Group Chat'),
              )),
            ),
          ),
        ],
      ),
    );
  }
}
