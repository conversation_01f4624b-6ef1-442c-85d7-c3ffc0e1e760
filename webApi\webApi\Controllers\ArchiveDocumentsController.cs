using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة وثائق الأرشيف
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class ArchiveDocumentsController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly IWebHostEnvironment _environment;

        public ArchiveDocumentsController(TasksDbContext context, IWebHostEnvironment environment)
        {
            _context = context;
            _environment = environment;
        }

        /// <summary>
        /// الحصول على جميع وثائق الأرشيف
        /// </summary>
        /// <returns>قائمة بجميع وثائق الأرشيف</returns>
        /// <response code="200">إرجاع قائمة وثائق الأرشيف</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveDocument>>> GetArchiveDocuments()
        {
            return await _context.ArchiveDocuments
                .Include(ad => ad.Category)
                .Include(ad => ad.CreatedByNavigation)
                .Include(ad => ad.ArchiveDocumentTags)
                .ThenInclude(adt => adt.Tag)
                .Where(ad => !ad.IsDeleted)
                .OrderByDescending(ad => ad.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على وثيقة أرشيف محددة
        /// </summary>
        /// <param name="id">معرف وثيقة الأرشيف</param>
        /// <returns>وثيقة الأرشيف المطلوبة</returns>
        /// <response code="200">إرجاع وثيقة الأرشيف</response>
        /// <response code="404">وثيقة الأرشيف غير موجودة</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<ArchiveDocument>> GetArchiveDocument(int id)
        {
            var archiveDocument = await _context.ArchiveDocuments
                .Include(ad => ad.Category)
                .Include(ad => ad.CreatedByNavigation)
                .Include(ad => ad.ArchiveDocumentTags)
                .ThenInclude(adt => adt.Tag)
                .FirstOrDefaultAsync(ad => ad.Id == id && !ad.IsDeleted);

            if (archiveDocument == null)
            {
                return NotFound();
            }

            return archiveDocument;
        }

        /// <summary>
        /// الحصول على وثائق فئة أرشيف محددة
        /// </summary>
        /// <param name="categoryId">معرف فئة الأرشيف</param>
        /// <returns>قائمة وثائق الفئة</returns>
        /// <response code="200">إرجاع قائمة وثائق الفئة</response>
        [HttpGet("category/{categoryId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveDocument>>> GetDocumentsByCategory(int categoryId)
        {
            return await _context.ArchiveDocuments
                .Include(ad => ad.CreatedByNavigation)
                .Include(ad => ad.ArchiveDocumentTags)
                .ThenInclude(adt => adt.Tag)
                .Where(ad => ad.CategoryId == categoryId && !ad.IsDeleted)
                .OrderByDescending(ad => ad.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// البحث في وثائق الأرشيف
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة وثائق الأرشيف المطابقة</returns>
        /// <response code="200">إرجاع قائمة وثائق الأرشيف المطابقة</response>
        [HttpGet("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveDocument>>> SearchDocuments([FromQuery] string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await GetArchiveDocuments();
            }

            return await _context.ArchiveDocuments
                .Include(ad => ad.Category)
                .Include(ad => ad.CreatedByNavigation)
                .Where(ad => !ad.IsDeleted && 
                           (ad.Title.Contains(searchTerm) || 
                            (ad.Description != null && ad.Description.Contains(searchTerm)) ||
                            (ad.Content != null && ad.Content.Contains(searchTerm))))
                .OrderByDescending(ad => ad.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// رفع وثيقة أرشيف جديدة
        /// </summary>
        /// <param name="file">الملف المرفق</param>
        /// <param name="title">عنوان الوثيقة</param>
        /// <param name="description">وصف الوثيقة</param>
        /// <param name="categoryId">معرف فئة الأرشيف</param>
        /// <param name="createdBy">معرف المستخدم الذي أنشأ الوثيقة</param>
        /// <returns>وثيقة الأرشيف المُنشأة</returns>
        /// <response code="201">تم رفع وثيقة الأرشيف بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost("upload")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ArchiveDocument>> UploadArchiveDocument(
            IFormFile file,
            [FromForm] string title,
            [FromForm] string? description,
            [FromForm] int categoryId,
            [FromForm] int createdBy)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("لم يتم تحديد ملف للرفع");
            }

            // التحقق من وجود الفئة
            var categoryExists = await _context.ArchiveCategories.AnyAsync(ac => ac.Id == categoryId && !ac.IsDeleted);
            if (!categoryExists)
            {
                return BadRequest("فئة الأرشيف غير موجودة");
            }

            // إنشاء مجلد وثائق الأرشيف إذا لم يكن موجوداً
            var uploadsFolder = Path.Combine(_environment.WebRootPath ?? _environment.ContentRootPath, "uploads", "archive-documents");
            if (!Directory.Exists(uploadsFolder))
            {
                Directory.CreateDirectory(uploadsFolder);
            }

            // إنشاء اسم ملف فريد
            var fileName = $"{Guid.NewGuid()}_{file.FileName}";
            var filePath = Path.Combine(uploadsFolder, fileName);

            // حفظ الملف
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            // إنشاء سجل وثيقة الأرشيف في قاعدة البيانات
            var archiveDocument = new ArchiveDocument
            {
                Title = title,
                Description = description,
                CategoryId = categoryId,
                FileName = file.FileName,
                FilePath = $"/uploads/archive-documents/{fileName}",
                FileSize = file.Length,
                FileType = file.ContentType,
                UploadedBy = createdBy,
                UploadedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                CreatedBy = createdBy,
                CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                IsDeleted = false
            };

            _context.ArchiveDocuments.Add(archiveDocument);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetArchiveDocument", new { id = archiveDocument.Id }, archiveDocument);
        }

        /// <summary>
        /// إنشاء وثيقة أرشيف جديدة (بدون رفع ملف)
        /// </summary>
        /// <param name="archiveDocument">بيانات وثيقة الأرشيف</param>
        /// <returns>وثيقة الأرشيف المُنشأة</returns>
        /// <response code="201">تم إنشاء وثيقة الأرشيف بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ArchiveDocument>> PostArchiveDocument(ArchiveDocument archiveDocument)
        {
            archiveDocument.UploadedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            archiveDocument.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            archiveDocument.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            archiveDocument.IsDeleted = false;

            _context.ArchiveDocuments.Add(archiveDocument);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetArchiveDocument", new { id = archiveDocument.Id }, archiveDocument);
        }

        /// <summary>
        /// تحديث وثيقة أرشيف
        /// </summary>
        /// <param name="id">معرف وثيقة الأرشيف</param>
        /// <param name="archiveDocument">بيانات وثيقة الأرشيف المحدثة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث وثيقة الأرشيف بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">وثيقة الأرشيف غير موجودة</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutArchiveDocument(int id, ArchiveDocument archiveDocument)
        {
            if (id != archiveDocument.Id)
            {
                return BadRequest();
            }

            archiveDocument.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            _context.Entry(archiveDocument).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ArchiveDocumentExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// تحميل ملف وثيقة أرشيف
        /// </summary>
        /// <param name="id">معرف وثيقة الأرشيف</param>
        /// <returns>الملف للتحميل</returns>
        /// <response code="200">إرجاع الملف</response>
        /// <response code="404">وثيقة الأرشيف غير موجودة</response>
        [HttpGet("{id}/download")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DownloadArchiveDocument(int id)
        {
            var archiveDocument = await _context.ArchiveDocuments.FindAsync(id);
            if (archiveDocument == null || archiveDocument.IsDeleted)
            {
                return NotFound();
            }

            if (string.IsNullOrEmpty(archiveDocument.FilePath))
            {
                return NotFound("لا يوجد ملف مرتبط بهذه الوثيقة");
            }

            var filePath = Path.Combine(_environment.WebRootPath ?? _environment.ContentRootPath, archiveDocument.FilePath.TrimStart('/'));
            
            if (!System.IO.File.Exists(filePath))
            {
                return NotFound("الملف غير موجود على الخادم");
            }

            var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
            return File(fileBytes, archiveDocument.FileType ?? "application/octet-stream", archiveDocument.FileName);
        }

        /// <summary>
        /// حذف وثيقة أرشيف (حذف منطقي)
        /// </summary>
        /// <param name="id">معرف وثيقة الأرشيف</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف وثيقة الأرشيف بنجاح</response>
        /// <response code="404">وثيقة الأرشيف غير موجودة</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteArchiveDocument(int id)
        {
            var archiveDocument = await _context.ArchiveDocuments.FindAsync(id);
            if (archiveDocument == null || archiveDocument.IsDeleted)
            {
                return NotFound();
            }

            // حذف منطقي
            archiveDocument.IsDeleted = true;
            archiveDocument.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool ArchiveDocumentExists(int id)
        {
            return _context.ArchiveDocuments.Any(e => e.Id == id && !e.IsDeleted);
        }
    }
}
