import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/admin_controller.dart';
import '../../models/permission_model.dart';
import '../../models/user_model.dart';

/// شاشة إدارة الصلاحيات
///
/// تتيح للمدير إدارة صلاحيات المستخدمين بشكل مركزي
class PermissionsManagementScreen extends StatefulWidget {
  /// إذا كانت الشاشة تستخدم كتبويب داخل لوحة التحكم الإدارية
  final bool isTab;

  const PermissionsManagementScreen({
    super.key,
    this.isTab = true, // افتراضيًا تستخدم كتبويب
  });

  // الحصول على حالة الشاشة - جعل الدالة خاصة لتجنب استخدام النوع الخاص في واجهة عامة
  static State<PermissionsManagementScreen>? of(BuildContext context) {
    return context.findAncestorStateOfType<State<PermissionsManagementScreen>>();
  }

  @override
  State<PermissionsManagementScreen> createState() => _PermissionsManagementScreenState();
}

class _PermissionsManagementScreenState extends State<PermissionsManagementScreen> with SingleTickerProviderStateMixin {
  final AdminController _adminController = Get.find<AdminController>();
  // AuthController removed as it was unused

  late TabController _tabController;
  User? _selectedUser;

  // خرائط لتخزين حالة الصلاحيات
  final Map<String, bool> _permissionStatus = <String, bool>{};
  final Map<String, bool> _interfacePermissions = <String, bool>{};

  // حالة العرض الموحد
  bool _unifiedView = false;

  // قائمة الواجهات مع أسمائها العربية
  final Map<String, String> _interfaceNames = {
    'tasks': 'المهام',
    'dashboard': 'لوحة المعلومات',
    'messages': 'الرسائل',
    'notifications': 'الإشعارات',
    'departments': 'الأقسام',
    'users': 'المستخدمين',
    'reports': 'التقارير',
    'settings': 'الإعدادات',
    'admin': 'الإدارة',
    'calendar': 'التقويم',
    'power_bi': 'تقارير Power BI',
    'database': 'إدارة قاعدة البيانات',
    'user-dashboard': 'لوحة تحكم المستخدم',
    'archive': 'نظام الأرشفة الإلكترونية',
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // تحميل المستخدمين
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _adminController.loadAllUsers();

      // التحقق من وجود مستخدم محدد من لوحة الإدارة
      final adminController = Get.find<AdminController>();
      if (adminController.selectedPermissionUser.value != null && mounted) {
        setState(() {
          _selectedUser = adminController.selectedPermissionUser.value;
        });
        // استخدام try-catch لتجنب الأخطاء إذا تم التنقل بعيدًا عن الشاشة
        try {
          await _loadUserPermissions(_selectedUser!.id);
        } catch (e) {
          // تجاهل الأخطاء إذا تم التنقل بعيدًا عن الشاشة
          debugPrint('Error loading user permissions: $e');
        }
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل صلاحيات المستخدم
  Future<void> _loadUserPermissions(String userId) async {
    // تحميل صلاحيات المستخدم
    await _adminController.loadUserPermissions(userId);

    // إعادة تعيين الخرائط
    _permissionStatus.clear();
    _interfacePermissions.clear();

    // تهيئة حالة الصلاحيات
    for (var type in PermissionType.values) {
      for (var scope in PermissionScope.values) {
        final key = '${type.index}_${scope.index}';
        final permission = _adminController.permissions.firstWhere(
          (p) => p.type == type && p.scope == scope && p.userId == userId,
          orElse: () => Permission(
            id: '',
            userId: userId,
            type: type,
            scope: scope,
            isGranted: false,
            createdAt: DateTime.now(),
          ),
        );

        _permissionStatus[key] = permission.isGranted;

        // إذا كانت صلاحية واجهة، نخزنها في قائمة منفصلة
        if (scope == PermissionScope.interfaces && type == PermissionType.view) {
          // استخدام الوصف كمفتاح للواجهة
          if (permission.description != null) {
            _interfacePermissions[permission.description!] = permission.isGranted;
          }
        }
      }
    }

    // تهيئة صلاحيات الواجهات غير الموجودة
    for (final interface in _interfaceNames.keys) {
      if (!_interfacePermissions.containsKey(interface)) {
        _interfacePermissions[interface] = false;
      }
    }

    // تحديث الحالة الأصلية للصلاحيات
    _updateOriginalPermissions();

    // تحديث الواجهة فقط إذا كان العنصر لا يزال موجودًا في شجرة العناصر
    if (mounted) {
      setState(() {});
    }
  }

  // خرائط لتخزين حالة الصلاحيات الأصلية
  final Map<String, bool> _originalPermissionStatus = <String, bool>{};
  final Map<String, bool> _originalInterfacePermissions = <String, bool>{};

  /// حفظ صلاحيات المستخدم
  Future<void> _saveUserPermissions() async {
    if (_selectedUser == null) return;

    debugPrint('بدء حفظ صلاحيات المستخدم: ${_selectedUser!.name} (${_selectedUser!.id})');

    // عرض مؤشر التحميل
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    try {
      int changesCount = 0;

      // حفظ الصلاحيات العادية المتغيرة فقط
      debugPrint('حفظ الصلاحيات العادية...');
      for (var type in PermissionType.values) {
        for (var scope in PermissionScope.values) {
          final key = '${type.index}_${scope.index}';
          final currentValue = _permissionStatus[key] ?? false;
          final originalValue = _originalPermissionStatus[key] ?? false;

          // تحقق مما إذا كانت الصلاحية قد تغيرت
          if (currentValue != originalValue) {
            debugPrint('تغيير الصلاحية: النوع=${type.index}, المجال=${scope.index}, القيمة=$currentValue');
            if (currentValue) {
              await _adminController.grantPermission(_selectedUser!.id, type, scope);
              debugPrint('تم منح الصلاحية: ${_getPermissionTypeText(type)} - ${_getPermissionScopeText(scope)}');
            } else {
              await _adminController.revokePermission(_selectedUser!.id, type, scope);
              debugPrint('تم إلغاء الصلاحية: ${_getPermissionTypeText(type)} - ${_getPermissionScopeText(scope)}');
            }
            changesCount++;
          }
        }
      }

      // حفظ صلاحيات الواجهات المتغيرة فقط
      debugPrint('حفظ صلاحيات الواجهات...');
      for (final entry in _interfacePermissions.entries) {
        final interface = entry.key;
        final currentValue = entry.value;
        final originalValue = _originalInterfacePermissions[interface] ?? false;

        // تحقق مما إذا كانت الصلاحية قد تغيرت
        if (currentValue != originalValue) {
          debugPrint('تغيير صلاحية الواجهة: $interface, القيمة=$currentValue');
          if (currentValue) {
            await _adminController.grantPermission(
              _selectedUser!.id,
              PermissionType.view,
              PermissionScope.interfaces,
              description: interface,
            );
            debugPrint('تم منح صلاحية الواجهة: $interface (${_interfaceNames[interface] ?? interface})');
          } else {
            await _adminController.revokePermission(
              _selectedUser!.id,
              PermissionType.view,
              PermissionScope.interfaces,
              description: interface,
            );
            debugPrint('تم إلغاء صلاحية الواجهة: $interface (${_interfaceNames[interface] ?? interface})');
          }
          changesCount++;
        }
      }

      // تحديث الحالة الأصلية بعد الحفظ
      _updateOriginalPermissions();
      debugPrint('تم تحديث الحالة الأصلية للصلاحيات');

      // إعادة تحميل صلاحيات المستخدم للتأكد من تطبيق التغييرات
      await _loadUserPermissions(_selectedUser!.id);
      debugPrint('تم إعادة تحميل صلاحيات المستخدم بعد الحفظ');

      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة نجاح
      Get.snackbar(
        'تم بنجاح',
        'تم حفظ $changesCount تغييرات في صلاحيات المستخدم بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      debugPrint('تم الانتهاء من حفظ الصلاحيات بنجاح. عدد التغييرات: $changesCount');
    } catch (e) {
      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حفظ الصلاحيات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );

      debugPrint('حدث خطأ أثناء حفظ الصلاحيات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // بناء المحتوى الرئيسي
    Widget content = Row(
      children: [
        // قائمة المستخدمين
        SizedBox(
          width: 300,
          child: Card(
            margin: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text(
                    'المستخدمين',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Divider(),
                Expanded(
                  child: Obx(() {
                    if (_adminController.isLoading.value) {
                      return const Center(
                        child: CircularProgressIndicator(),
                      );
                    }

                    if (_adminController.users.isEmpty) {
                      return const Center(
                        child: Text('لا يوجد مستخدمين'),
                      );
                    }

                    return ListView.builder(
                      itemCount: _adminController.users.length,
                      itemBuilder: (context, index) {
                        final user = _adminController.users[index];
                        final isSelected = _selectedUser?.id == user.id;

                        return ListTile(
                          leading: CircleAvatar(
                            backgroundColor: isSelected ? Colors.blue : Colors.grey.shade200,
                            child: Text(
                              user.name.substring(0, 1),
                              style: TextStyle(
                                color: isSelected ? Colors.white : Colors.black,
                              ),
                            ),
                          ),
                          title: Text(user.name),
                          subtitle: Text(_getRoleText(user.role)),
                          selected: isSelected,
                          onTap: () async {
                            // تعيين المستخدم المحدد أولاً
                            setState(() {
                              _selectedUser = user;
                            });

                            // ثم تحميل الصلاحيات
                            await _loadUserPermissions(user.id);

                            // تحديث الواجهة مرة أخرى بعد تحميل الصلاحيات
                            if (mounted) {
                              setState(() {});
                            }
                          },
                        );
                      },
                    );
                  }),
                ),
              ],
            ),
          ),
        ),

        // تفاصيل الصلاحيات
        Expanded(
          child: _selectedUser == null
              ? const Center(
                  child: Text('اختر مستخدم لإدارة صلاحياته'),
                )
              : Card(
                  margin: const EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // معلومات المستخدم
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                CircleAvatar(
                                  radius: 24,
                                  backgroundColor: Colors.blue,
                                  child: Text(
                                    _selectedUser!.name.substring(0, 1),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 20,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _selectedUser!.name,
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      _selectedUser!.email,
                                      style: const TextStyle(
                                        color: Colors.grey,
                                      ),
                                    ),
                                    Text(
                                      _getRoleText(_selectedUser!.role),
                                      style: const TextStyle(
                                        color: Colors.blue,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            // زر حفظ الصلاحيات
                            IconButton(
                              icon: const Icon(Icons.save),
                              tooltip: 'حفظ الصلاحيات',
                              onPressed: _saveUserPermissions,
                            ),
                          ],
                        ),
                      ),
                      const Divider(),

                      // خيار العرض الموحد
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Text(
                              'عرض موحد للصلاحيات',
                              style: TextStyle(
                                color: Get.isDarkMode ? Colors.grey[300] : Colors.grey[700],
                              ),
                            ),
                            Switch(
                              value: _unifiedView,
                              onChanged: (value) {
                                setState(() {
                                  _unifiedView = value;
                                });
                              },
                              activeColor: Colors.blue,
                            ),
                          ],
                        ),
                      ),

                      // تبويبات الصلاحيات
                      if (!_unifiedView)
                        TabBar(
                          controller: _tabController,
                          labelColor: Get.isDarkMode ? Colors.white : Colors.blue,
                          unselectedLabelColor: Get.isDarkMode ? Colors.grey[300] : Colors.grey[700],
                          indicatorColor: Get.isDarkMode ? Colors.white : Colors.blue,
                          tabs: const [
                            Tab(text: 'صلاحيات المهام والبيانات'),
                            Tab(text: 'صلاحيات الواجهات'),
                          ],
                        ),

                      // محتوى التبويبات
                      Expanded(
                        child: _unifiedView
                            ? _buildUnifiedPermissionsView()
                            : TabBarView(
                                controller: _tabController,
                                children: [
                                  _buildDataPermissionsTab(),
                                  _buildInterfacePermissionsTab(),
                                ],
                              ),
                      ),
                    ],
                  ),
                ),
        ),
      ],
    );

    // إذا كانت الشاشة تستخدم كتبويب، نعيد المحتوى فقط
    if (widget.isTab) {
      return content;
    }

    // وإلا نضيف Scaffold مع AppBar
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الصلاحيات'),
        actions: [
          // زر حفظ الصلاحيات
          if (_selectedUser != null)
            IconButton(
              icon: const Icon(Icons.save),
              tooltip: 'حفظ الصلاحيات',
              onPressed: _saveUserPermissions,
            ),
        ],
      ),
      body: content,
    );
  }

  /// بناء تبويب صلاحيات البيانات
  Widget _buildDataPermissionsTab() {
    // تصفية أنواع الصلاحيات المهمة للبيانات
    final relevantTypes = [
      PermissionType.view,
      PermissionType.create,
      PermissionType.edit,
      PermissionType.delete,
      PermissionType.approve,
      PermissionType.viewAll,
      PermissionType.viewOwn,
    ];

    // تصفية مجالات الصلاحيات المهمة للبيانات
    final relevantScopes = [
      PermissionScope.tasks,
      PermissionScope.users,
      PermissionScope.departments,
      PermissionScope.messages,
      PermissionScope.reports,
      PermissionScope.archive,
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'صلاحيات البيانات',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Get.isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              Row(
                children: [
                  ElevatedButton.icon(
                    icon: const Icon(Icons.check_circle_outline, size: 16),
                    label: const Text('منح الكل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Get.isDarkMode ? Colors.green[700] : Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      textStyle: const TextStyle(fontSize: 12),
                      elevation: Get.isDarkMode ? 4 : 2,
                      shadowColor: Get.isDarkMode ? Colors.black : Colors.green.withAlpha(100),
                    ),
                    onPressed: _grantAllDataPermissions,
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.cancel_outlined, size: 16),
                    label: const Text('إلغاء الكل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Get.isDarkMode ? Colors.red[700] : Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      textStyle: const TextStyle(fontSize: 12),
                      elevation: Get.isDarkMode ? 4 : 2,
                      shadowColor: Get.isDarkMode ? Colors.black : Colors.red.withAlpha(100),
                    ),
                    onPressed: _revokeAllDataPermissions,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'حدد الصلاحيات التي يمتلكها المستخدم للتعامل مع البيانات المختلفة في النظام.',
            style: TextStyle(
              color: Get.isDarkMode ? Colors.grey[400]! : Colors.grey,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 16),

          // جدول الصلاحيات
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Theme(
              data: Theme.of(context).copyWith(
                dataTableTheme: DataTableTheme.of(context).copyWith(
                  headingTextStyle: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Get.isDarkMode ? Colors.white : Colors.black,
                  ),
                  dataTextStyle: TextStyle(
                    color: Get.isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                dividerColor: Get.isDarkMode ? Colors.grey[700] : null,
                checkboxTheme: CheckboxThemeData(
                  fillColor: WidgetStateProperty.resolveWith<Color>((states) {
                    if (states.contains(WidgetState.selected)) {
                      return Get.isDarkMode ? Colors.blue[300]! : Colors.blue;
                    }
                    return Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!;
                  }),
                  checkColor: WidgetStateProperty.all(Get.isDarkMode ? Colors.black : Colors.white),
                ),
              ),
              child: DataTable(
                columnSpacing: 16,
                headingRowColor: WidgetStateProperty.resolveWith<Color>(
                  (states) => Get.isDarkMode ? Colors.grey[800]! : Colors.grey[200]!,
                ),
                columns: [
                  DataColumn(
                    label: Text(
                      'المجال',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Get.isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                  ),
                  ...relevantTypes.map((type) {
                    return DataColumn(
                      label: Text(
                        _getPermissionTypeText(type),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Get.isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                    );
                  }),
                ],
                rows: relevantScopes.map((scope) {
                  return DataRow(
                    color: WidgetStateProperty.resolveWith<Color?>(
                      (Set<WidgetState> states) {
                        // تلوين الصفوف بالتناوب لتحسين القراءة
                        int index = relevantScopes.indexOf(scope);
                        if (index % 2 == 0) {
                          return Get.isDarkMode ? Colors.grey[900] : Colors.grey[50];
                        }
                        return Get.isDarkMode ? Colors.black : Colors.white;
                      },
                    ),
                    cells: [
                      DataCell(Text(
                        _getPermissionScopeText(scope),
                        style: TextStyle(
                          color: Get.isDarkMode ? Colors.white : Colors.black,
                        ),
                      )),
                      ...relevantTypes.map((type) {
                        final key = '${type.index}_${scope.index}';
                        return DataCell(
                          Checkbox(
                            value: _permissionStatus[key] ?? false,
                            onChanged: (value) {
                              setState(() {
                                _permissionStatus[key] = value ?? false;
                              });
                            },
                          ),
                        );
                      }),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),

          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 16),

          // صلاحيات إضافية
          Text(
            'صلاحيات إضافية',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Get.isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 16),

          // صلاحيات التصدير والاستيراد
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Theme(
              data: Theme.of(context).copyWith(
                dataTableTheme: DataTableTheme.of(context).copyWith(
                  headingTextStyle: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Get.isDarkMode ? Colors.white : Colors.black,
                  ),
                  dataTextStyle: TextStyle(
                    color: Get.isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                dividerColor: Get.isDarkMode ? Colors.grey[700] : null,
                checkboxTheme: CheckboxThemeData(
                  fillColor: WidgetStateProperty.resolveWith<Color>((states) {
                    if (states.contains(WidgetState.selected)) {
                      return Get.isDarkMode ? Colors.blue[300]! : Colors.blue;
                    }
                    return Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!;
                  }),
                  checkColor: WidgetStateProperty.all(Get.isDarkMode ? Colors.black : Colors.white),
                ),
              ),
              child: DataTable(
                columnSpacing: 16,
                headingRowColor: WidgetStateProperty.resolveWith<Color>(
                  (states) => Get.isDarkMode ? Colors.grey[800]! : Colors.grey[200]!,
                ),
                columns: [
                  DataColumn(
                    label: Text(
                      'المجال',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Get.isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'تصدير',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Get.isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                  ),
                  DataColumn(
                    label: Text(
                      'استيراد',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Get.isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                  ),
                ],
                rows: [
                  PermissionScope.tasks,
                  PermissionScope.users,
                  PermissionScope.departments,
                  PermissionScope.reports,
                ].asMap().entries.map((entry) {
                  final scope = entry.value;
                  final index = entry.key;
                  return DataRow(
                    color: WidgetStateProperty.resolveWith<Color?>(
                      (Set<WidgetState> states) {
                        if (index % 2 == 0) {
                          return Get.isDarkMode ? Colors.grey[900] : Colors.grey[50];
                        }
                        return Get.isDarkMode ? Colors.black : Colors.white;
                      },
                    ),
                    cells: [
                      DataCell(Text(
                        _getPermissionScopeText(scope),
                        style: TextStyle(
                          color: Get.isDarkMode ? Colors.white : Colors.black,
                        ),
                      )),
                      DataCell(
                        Checkbox(
                          value: _permissionStatus['${PermissionType.export.index}_${scope.index}'] ?? false,
                          onChanged: (value) {
                            setState(() {
                              _permissionStatus['${PermissionType.export.index}_${scope.index}'] = value ?? false;
                            });
                          },
                        ),
                      ),
                      DataCell(
                        Checkbox(
                          value: _permissionStatus['${PermissionType.import.index}_${scope.index}'] ?? false,
                          onChanged: (value) {
                            setState(() {
                              _permissionStatus['${PermissionType.import.index}_${scope.index}'] = value ?? false;
                            });
                          },
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تبويب صلاحيات الواجهات
  Widget _buildInterfacePermissionsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الوصول إلى الواجهات',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Get.isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              Row(
                children: [
                  ElevatedButton.icon(
                    icon: const Icon(Icons.check_circle_outline, size: 16),
                    label: const Text('منح الكل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Get.isDarkMode ? Colors.green[700] : Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      textStyle: const TextStyle(fontSize: 12),
                      elevation: Get.isDarkMode ? 4 : 2,
                      shadowColor: Get.isDarkMode ? Colors.black : Colors.green.withAlpha(100),
                    ),
                    onPressed: _grantAllInterfacePermissions,
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.cancel_outlined, size: 16),
                    label: const Text('إلغاء الكل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Get.isDarkMode ? Colors.red[700] : Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      textStyle: const TextStyle(fontSize: 12),
                      elevation: Get.isDarkMode ? 4 : 2,
                      shadowColor: Get.isDarkMode ? Colors.black : Colors.red.withAlpha(100),
                    ),
                    onPressed: _revokeAllInterfacePermissions,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'حدد الواجهات التي يمكن للمستخدم الوصول إليها.',
            style: TextStyle(
              color: Get.isDarkMode ? Colors.grey[400]! : Colors.grey,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 16),

          // قائمة الواجهات
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _interfaceNames.length,
            itemBuilder: (context, index) {
              final interface = _interfaceNames.keys.elementAt(index);
              final interfaceName = _interfaceNames[interface];

              // تحسين الألوان في الوضع الداكن
              final Color tileColor = index % 2 == 0
                  ? (Get.isDarkMode ? Colors.grey[900]! : Colors.grey[50]!)
                  : (Get.isDarkMode ? Colors.black : Colors.white);

              final Color textColor = Get.isDarkMode ? Colors.white : Colors.black;
              final Color iconColor = Get.isDarkMode ? Colors.blue[300]! : Colors.blue;

              return Container(
                decoration: BoxDecoration(
                  color: tileColor,
                  border: index == 0
                      ? Border(
                          top: BorderSide(color: Get.isDarkMode ? Colors.grey[800]! : Colors.grey[300]!),
                          bottom: BorderSide(color: Get.isDarkMode ? Colors.grey[800]! : Colors.grey[300]!),
                        )
                      : Border(
                          bottom: BorderSide(color: Get.isDarkMode ? Colors.grey[800]! : Colors.grey[300]!),
                        ),
                ),
                child: Theme(
                  data: Theme.of(context).copyWith(
                    checkboxTheme: CheckboxThemeData(
                      fillColor: WidgetStateProperty.resolveWith<Color>((states) {
                        if (states.contains(WidgetState.selected)) {
                          return Get.isDarkMode ? Colors.blue[300]! : Colors.blue;
                        }
                        return Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!;
                      }),
                      checkColor: WidgetStateProperty.all(Get.isDarkMode ? Colors.black : Colors.white),
                    ),
                  ),
                  child: CheckboxListTile(
                    title: Text(
                      interfaceName ?? interface,
                      style: TextStyle(
                        color: textColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    value: _interfacePermissions[interface] ?? false,
                    onChanged: (value) {
                      setState(() {
                        _interfacePermissions[interface] = value ?? false;
                      });
                    },
                    secondary: Icon(
                      _getInterfaceIcon(interface),
                      color: iconColor,
                    ),
                    activeColor: Get.isDarkMode ? Colors.blue[300] : Colors.blue,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// الحصول على نص الدور
  String _getRoleText(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'مدير النظام';
      case UserRole.departmentManager:
        return 'مدير قسم';
      case UserRole.employee:
        return 'موظف';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على نص نوع الصلاحية
  String _getPermissionTypeText(PermissionType type) {
    switch (type) {
      case PermissionType.view:
        return 'عرض';
      case PermissionType.create:
        return 'إنشاء';
      case PermissionType.edit:
        return 'تعديل';
      case PermissionType.delete:
        return 'حذف';
      case PermissionType.approve:
        return 'موافقة';
      case PermissionType.export:
        return 'تصدير';
      case PermissionType.import:
        return 'استيراد';
      case PermissionType.admin:
        return 'إدارة';
      case PermissionType.assign:
        return 'تعيين';
      case PermissionType.transfer:
        return 'نقل';
      case PermissionType.comment:
        return 'تعليق';
      case PermissionType.attach:
        return 'إرفاق';
      case PermissionType.viewAll:
        return 'عرض الكل';
      case PermissionType.viewOwn:
        return 'عرض الخاص';
      case PermissionType.interfaceAccess:
        return 'الوصول إلى واجهة';
      case PermissionType.download:
        return 'تنزيل';
      case PermissionType.upload:
        return 'رفع';
      case PermissionType.share:
        return 'مشاركة';
      case PermissionType.print:
        return 'طباعة';
      case PermissionType.ganttChart:
        return 'مخطط جانت';
      case PermissionType.powerBI:
        return 'تقارير باور بي آي';
      case PermissionType.calendar:
        return 'التقويم';
      default:
        return 'غير معروف';
    }
  }

  /// تحديث حالة الصلاحيات الأصلية
  void _updateOriginalPermissions() {
    // نسخ حالة الصلاحيات الحالية إلى الحالة الأصلية
    _originalPermissionStatus.clear();
    _originalPermissionStatus.addAll(_permissionStatus);

    // نسخ حالة صلاحيات الواجهات الحالية إلى الحالة الأصلية
    _originalInterfacePermissions.clear();
    _originalInterfacePermissions.addAll(_interfacePermissions);
  }

  /// منح جميع صلاحيات البيانات
  void _grantAllDataPermissions() {
    if (_selectedUser == null) return;

    setState(() {
      // تصفية أنواع الصلاحيات المهمة للبيانات
      final relevantTypes = [
        PermissionType.view,
        PermissionType.create,
        PermissionType.edit,
        PermissionType.delete,
        PermissionType.approve,
        PermissionType.viewAll,
        PermissionType.viewOwn,
        PermissionType.export,
        PermissionType.import,
      ];

      // تصفية مجالات الصلاحيات المهمة للبيانات
      final relevantScopes = [
        PermissionScope.tasks,
        PermissionScope.users,
        PermissionScope.departments,
        PermissionScope.messages,
        PermissionScope.reports,
        PermissionScope.archive,
      ];

      // منح جميع الصلاحيات
      for (var type in relevantTypes) {
        for (var scope in relevantScopes) {
          final key = '${type.index}_${scope.index}';
          _permissionStatus[key] = true;
        }
      }
    });

    // عرض رسالة نجاح
    Get.snackbar(
      'تم بنجاح',
      'تم منح جميع صلاحيات البيانات',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  /// إلغاء جميع صلاحيات البيانات
  void _revokeAllDataPermissions() {
    if (_selectedUser == null) return;

    setState(() {
      // تصفية أنواع الصلاحيات المهمة للبيانات
      final relevantTypes = [
        PermissionType.view,
        PermissionType.create,
        PermissionType.edit,
        PermissionType.delete,
        PermissionType.approve,
        PermissionType.viewAll,
        PermissionType.viewOwn,
        PermissionType.export,
        PermissionType.import,
      ];

      // تصفية مجالات الصلاحيات المهمة للبيانات
      final relevantScopes = [
        PermissionScope.tasks,
        PermissionScope.users,
        PermissionScope.departments,
        PermissionScope.messages,
        PermissionScope.reports,
        PermissionScope.archive,
      ];

      // إلغاء جميع الصلاحيات
      for (var type in relevantTypes) {
        for (var scope in relevantScopes) {
          final key = '${type.index}_${scope.index}';
          _permissionStatus[key] = false;
        }
      }
    });

    // عرض رسالة نجاح
    Get.snackbar(
      'تم بنجاح',
      'تم إلغاء جميع صلاحيات البيانات',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  /// منح جميع صلاحيات الواجهات
  void _grantAllInterfacePermissions() {
    if (_selectedUser == null) return;

    setState(() {
      // منح جميع صلاحيات الواجهات
      for (final interface in _interfaceNames.keys) {
        _interfacePermissions[interface] = true;
      }
    });

    // عرض رسالة نجاح
    Get.snackbar(
      'تم بنجاح',
      'تم منح جميع صلاحيات الواجهات',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  /// إلغاء جميع صلاحيات الواجهات
  void _revokeAllInterfacePermissions() {
    if (_selectedUser == null) return;

    setState(() {
      // إلغاء جميع صلاحيات الواجهات
      for (final interface in _interfaceNames.keys) {
        _interfacePermissions[interface] = false;
      }
    });

    // عرض رسالة نجاح
    Get.snackbar(
      'تم بنجاح',
      'تم إلغاء جميع صلاحيات الواجهات',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  /// بناء العرض الموحد للصلاحيات
  Widget _buildUnifiedPermissionsView() {
    if (_selectedUser == null) {
      return const Center(
        child: Text('اختر مستخدم لإدارة صلاحياته'),
      );
    }

    // قائمة الواجهات
    final interfaces = _interfaceNames.keys.toList();

    // أنواع الصلاحيات المهمة للبيانات
    final relevantTypes = [
      PermissionType.view,
      PermissionType.create,
      PermissionType.edit,
      PermissionType.delete,
      PermissionType.approve,
      PermissionType.export,
      PermissionType.import,
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'عرض موحد للصلاحيات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Get.isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'يمكنك إدارة صلاحيات الواجهات وصلاحيات البيانات المرتبطة بها من مكان واحد.',
            style: TextStyle(
              color: Get.isDarkMode ? Colors.grey[400]! : Colors.grey,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 24),

          // بناء قائمة الواجهات مع صلاحياتها
          ...interfaces.map((interface) {
            final hasAccess = _interfacePermissions[interface] ?? false;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان الواجهة مع مفتاح تبديل الوصول
                Container(
                  decoration: BoxDecoration(
                    color: Get.isDarkMode ? Colors.grey[800] : Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.all(12),
                  child: Row(
                    children: [
                      Icon(
                        _getInterfaceIcon(interface),
                        color: hasAccess
                            ? (Get.isDarkMode ? Colors.blue[300] : Colors.blue)
                            : (Get.isDarkMode ? Colors.grey : Colors.grey[600]),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        _interfaceNames[interface] ?? interface,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Get.isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        'الوصول',
                        style: TextStyle(
                          color: Get.isDarkMode ? Colors.grey[400] : Colors.grey[700],
                        ),
                      ),
                      Switch(
                        value: hasAccess,
                        onChanged: (value) {
                          setState(() {
                            _interfacePermissions[interface] = value;
                          });
                        },
                        activeColor: Get.isDarkMode ? Colors.blue[300] : Colors.blue,
                      ),
                    ],
                  ),
                ),

                // صلاحيات البيانات المرتبطة بالواجهة (تظهر فقط إذا كان الوصول مسموحًا)
                if (hasAccess)
                  Padding(
                    padding: const EdgeInsets.only(right: 16, top: 8, bottom: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'صلاحيات البيانات في ${_interfaceNames[interface]}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Get.isDarkMode ? Colors.grey[300] : Colors.grey[700],
                          ),
                        ),
                        const SizedBox(height: 8),

                        // جدول صلاحيات البيانات
                        _buildDataPermissionsForInterface(interface, relevantTypes),
                      ],
                    ),
                  ),

                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 8),
              ],
            );
          }),
        ],
      ),
    );
  }

  /// بناء جدول صلاحيات البيانات لواجهة معينة
  Widget _buildDataPermissionsForInterface(String interface, List<PermissionType> types) {
    // تحديد المجال المناسب للواجهة
    PermissionScope? scope;
    switch (interface) {
      case 'tasks':
        scope = PermissionScope.tasks;
        break;
      case 'users':
        scope = PermissionScope.users;
        break;
      case 'departments':
        scope = PermissionScope.departments;
        break;
      case 'messages':
        scope = PermissionScope.messages;
        break;
      case 'reports':
        scope = PermissionScope.reports;
        break;
      case 'archive':
        scope = PermissionScope.archive;
        break;
      default:
        // بعض الواجهات قد لا يكون لها مجال بيانات مباشر
        return const Text('لا توجد صلاحيات بيانات محددة لهذه الواجهة');
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Theme(
        data: Theme.of(context).copyWith(
          dataTableTheme: DataTableTheme.of(context).copyWith(
            headingTextStyle: TextStyle(
              fontWeight: FontWeight.bold,
              color: Get.isDarkMode ? Colors.white : Colors.black,
            ),
            dataTextStyle: TextStyle(
              color: Get.isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          dividerColor: Get.isDarkMode ? Colors.grey[700] : null,
          checkboxTheme: CheckboxThemeData(
            fillColor: WidgetStateProperty.resolveWith<Color>((states) {
              if (states.contains(WidgetState.selected)) {
                return Get.isDarkMode ? Colors.blue[300]! : Colors.blue;
              }
              return Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!;
            }),
            checkColor: WidgetStateProperty.all(Get.isDarkMode ? Colors.black : Colors.white),
          ),
        ),
        child: DataTable(
          columnSpacing: 16,
          headingRowColor: WidgetStateProperty.resolveWith<Color>(
            (states) => Get.isDarkMode ? Colors.grey[800]! : Colors.grey[200]!,
          ),
          columns: types.map((type) {
            return DataColumn(
              label: Text(
                _getPermissionTypeText(type),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Get.isDarkMode ? Colors.white : Colors.black,
                ),
              ),
            );
          }).toList(),
          rows: [
            DataRow(
              cells: types.map((type) {
                final key = '${type.index}_${scope!.index}';
                return DataCell(
                  Checkbox(
                    value: _permissionStatus[key] ?? false,
                    onChanged: (value) {
                      setState(() {
                        _permissionStatus[key] = value ?? false;
                      });
                    },
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على نص مجال الصلاحية
  String _getPermissionScopeText(PermissionScope scope) {
    switch (scope) {
      case PermissionScope.tasks:
        return 'المهام';
      case PermissionScope.users:
        return 'المستخدمين';
      case PermissionScope.departments:
        return 'الأقسام';
      case PermissionScope.reports:
        return 'التقارير';
      case PermissionScope.settings:
        return 'الإعدادات';
      case PermissionScope.backups:
        return 'النسخ الاحتياطية';
      case PermissionScope.messages:
        return 'الرسائل';
      case PermissionScope.notifications:
        return 'الإشعارات';
      case PermissionScope.logs:
        return 'سجلات النظام';
      case PermissionScope.dashboard:
        return 'لوحة المعلومات';
      case PermissionScope.analytics:
        return 'التحليلات';
      case PermissionScope.interfaces:
        return 'الواجهات';
      case PermissionScope.attachments:
        return 'المرفقات';
      case PermissionScope.files:
        return 'الملفات';
      case PermissionScope.ganttChart:
        return 'مخطط جانت';
      case PermissionScope.powerBI:
        return 'تقارير باور بي آي';
      case PermissionScope.calendar:
        return 'التقويم';
      case PermissionScope.database:
        return 'إدارة قاعدة البيانات';
      case PermissionScope.roles:
        return 'إدارة الأدوار';
      case PermissionScope.archive:
        return 'نظام الأرشفة الإلكترونية';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على أيقونة الواجهة
  IconData _getInterfaceIcon(String interface) {
    switch (interface) {
      case 'tasks':
        return Icons.task;
      case 'dashboard':
        return Icons.dashboard;
      case 'messages':
        return Icons.message;
      case 'notifications':
        return Icons.notifications;
      case 'departments':
        return Icons.business;
      case 'users':
        return Icons.people;
      case 'reports':
        return Icons.assessment;
      case 'settings':
        return Icons.settings;
      case 'admin':
        return Icons.admin_panel_settings;
      case 'calendar':
        return Icons.calendar_today;
      case 'power_bi':
        return Icons.analytics;
      case 'database':
        return Icons.storage;
      case 'archive':
        return Icons.archive;
      default:
        return Icons.web;
    }
  }
}
