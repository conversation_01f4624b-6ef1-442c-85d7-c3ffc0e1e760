import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_application_2/controllers/archive_controller.dart';
import 'package:flutter_application_2/models/archive_document_version_model.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/utils/date_formatter.dart';
import 'package:flutter_application_2/utils/file_processor.dart';
import 'package:flutter_application_2/controllers/auth_controller.dart';

/// عنصر واجهة لعرض قائمة إصدارات الوثائق
class VersionHistoryList extends StatelessWidget {
  /// معرف الوثيقة
  final String documentId;

  /// دالة يتم استدعاؤها عند النقر على إصدار
  final Function(ArchiveDocumentVersion)? onVersionTap;

  /// دالة يتم استدعاؤها عند استعادة إصدار
  final Function(ArchiveDocumentVersion)? onVersionRestore;

  /// دالة يتم استدعاؤها عند حذف إصدار
  final Function(ArchiveDocumentVersion)? onVersionDelete;

  /// دالة يتم استدعاؤها عند تنزيل إصدار
  final Function(ArchiveDocumentVersion)? onVersionDownload;

  /// عدد الإصدارات المعروضة (إذا كانت القيمة null، يتم عرض جميع الإصدارات)
  final int? limit;

  /// هل يتم عرض زر "عرض المزيد"
  final bool showViewMoreButton;

  /// دالة يتم استدعاؤها عند النقر على زر "عرض المزيد"
  final VoidCallback? onViewMoreTap;

  const VersionHistoryList({
    super.key,
    required this.documentId,
    this.onVersionTap,
    this.onVersionRestore,
    this.onVersionDelete,
    this.onVersionDownload,
    this.limit,
    this.showViewMoreButton = false,
    this.onViewMoreTap,
  });

  @override
  Widget build(BuildContext context) {
    final ArchiveController controller = Get.find<ArchiveController>();
    final AuthController authController = Get.find<AuthController>();

    // تحميل إصدارات الوثيقة إذا لم تكن محملة بالفعل
    if (controller.documentVersions.isEmpty) {
      controller.loadDocumentVersions(documentId);
    }

    return GetBuilder<ArchiveController>(
      builder: (controller) {
        if (controller.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (controller.documentVersions.isEmpty) {
          return _buildEmptyState();
        }

        // تحديد الإصدارات التي سيتم عرضها
        final versions = limit != null && controller.documentVersions.length > limit!
            ? controller.documentVersions.sublist(0, limit)
            : controller.documentVersions;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: ListView.separated(
                itemCount: versions.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final version = versions[index];
                  return _buildVersionListItem(
                    version,
                    authController,
                    context,
                  );
                },
              ),
            ),
            if (showViewMoreButton &&
                limit != null &&
                controller.documentVersions.length > limit!)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Center(
                  child: ElevatedButton.icon(
                    onPressed: onViewMoreTap,
                    icon: const Icon(Icons.history),
                    label: const Text('عرض جميع الإصدارات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  /// بناء حالة عدم وجود إصدارات
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.history,
            size: 48,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد إصدارات سابقة',
            style: AppStyles.subtitle1,
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم إنشاء إصدار جديد في كل مرة يتم فيها تعديل الوثيقة',
            style: AppStyles.body2,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إصدار
  Widget _buildVersionListItem(
    ArchiveDocumentVersion version,
    AuthController authController,
    BuildContext context,
  ) {
    final isCurrentUser = version.creatorId == authController.currentUser.value?.id;
    final creatorName = isCurrentUser ? 'أنت' : 'مستخدم آخر'; // يمكن استبدالها بالحصول على اسم المستخدم من قاعدة البيانات

    return ListTile(
      contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      title: Row(
        children: [
          Text(
            'الإصدار ${version.versionNumber}',
            style: AppStyles.subtitle1.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 8),
          Chip(
            label: Text(
              DateFormatter.formatDateTime(version.createdAt),
              style: AppStyles.caption.copyWith(
                color: Colors.white,
              ),
            ),
            backgroundColor: AppColors.primary,
            padding: const EdgeInsets.symmetric(horizontal: 8),
          ),
        ],
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4),
          Text(
            'بواسطة: $creatorName',
            style: AppStyles.body2,
          ),
          if (version.changeNotes != null && version.changeNotes!.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              'ملاحظات التغيير: ${version.changeNotes}',
              style: AppStyles.body2,
            ),
          ],
        ],
      ),
      leading: CircleAvatar(
        backgroundColor: _getFileColor(version.fileType).withAlpha(50),
        child: Icon(
          _getFileIcon(version.fileType),
          color: _getFileColor(version.fileType),
          size: 20,
        ),
      ),
      trailing: PopupMenuButton<String>(
        icon: const Icon(Icons.more_vert),
        onSelected: (value) => _handleVersionAction(value, version),
        itemBuilder: (context) => [
          const PopupMenuItem<String>(
            value: 'view',
            child: Row(
              children: [
                Icon(Icons.visibility),
                SizedBox(width: 8),
                Text('عرض'),
              ],
            ),
          ),
          const PopupMenuItem<String>(
            value: 'download',
            child: Row(
              children: [
                Icon(Icons.download),
                SizedBox(width: 8),
                Text('تنزيل'),
              ],
            ),
          ),
          const PopupMenuItem<String>(
            value: 'restore',
            child: Row(
              children: [
                Icon(Icons.restore),
                SizedBox(width: 8),
                Text('استعادة كإصدار حالي'),
              ],
            ),
          ),
          const PopupMenuItem<String>(
            value: 'delete',
            child: Row(
              children: [
                Icon(Icons.delete, color: Colors.red),
                SizedBox(width: 8),
                Text('حذف', style: TextStyle(color: Colors.red)),
              ],
            ),
          ),
        ],
      ),
      onTap: () => onVersionTap?.call(version),
    );
  }

  /// التعامل مع إجراءات الإصدار
  void _handleVersionAction(String action, ArchiveDocumentVersion version) {
    switch (action) {
      case 'view':
        onVersionTap?.call(version);
        break;
      case 'download':
        onVersionDownload?.call(version);
        break;
      case 'restore':
        onVersionRestore?.call(version);
        break;
      case 'delete':
        onVersionDelete?.call(version);
        break;
    }
  }

  /// الحصول على أيقونة الملف
  IconData _getFileIcon(String fileType) {
    return FileProcessor.getFileIcon(fileType);
  }

  /// الحصول على لون الملف
  Color _getFileColor(String fileType) {
    return FileProcessor.getFileColor(fileType);
  }
}
