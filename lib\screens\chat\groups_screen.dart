import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/realtime_chat_controller.dart';
import '../../models/chat_group_model.dart';
import '../../models/user_model.dart';
import '../../utils/date_formatter.dart';
import '../../constants/app_colors.dart';
import 'chat_screen.dart';

class GroupsScreen extends StatefulWidget {
  const GroupsScreen({Key? key}) : super(key: key);

  @override
  State<GroupsScreen> createState() => _GroupsScreenState();
}

class _GroupsScreenState extends State<GroupsScreen> {
  final RealtimeChatController _chatController = Get.find<RealtimeChatController>();

  @override
  void initState() {
    super.initState();
    // تحميل المجموعات
    _chatController.loadGroups();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المحادثات'),
        actions: [
          Obx(() => _chatController.isConnected.value
              ? const Icon(Icons.wifi, color: Colors.green)
              : const Icon(Icons.wifi_off, color: Colors.red)),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _chatController.loadGroups();
            },
          ),
        ],
      ),
      body: Obx(() {
        final groups = _chatController.groups;
        
        if (groups.isEmpty) {
          return const Center(
            child: Text('لا توجد محادثات بعد'),
          );
        }
        
        return ListView.builder(
          itemCount: groups.length,
          itemBuilder: (context, index) {
            return _buildGroupItem(groups[index]);
          },
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: _showNewChatDialog,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildGroupItem(ChatGroup group) {
    // الحصول على آخر رسالة في المجموعة
    final lastMessage = _chatController.messages.firstWhereOrNull(
      (m) => m.id == group.lastMessageId,
    );
    
    // الحصول على عدد الرسائل غير المقروءة
    final unreadCount = 0; // يمكن تنفيذ هذا لاحقًا
    
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: AppColors.primary,
        child: Text(
          group.name.isNotEmpty ? group.name[0] : '?',
          style: const TextStyle(color: Colors.white),
        ),
      ),
      title: Text(
        group.name,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: lastMessage != null
          ? Text(
              lastMessage.content,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            )
          : const Text('لا توجد رسائل بعد'),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            group.updatedAt != null
                ? DateFormatter.formatMessageTime(group.updatedAt!)
                : '',
            style: const TextStyle(fontSize: 12),
          ),
          if (unreadCount > 0)
            Container(
              padding: const EdgeInsets.all(6),
              decoration: const BoxDecoration(
                color: AppColors.primary,
                shape: BoxShape.circle,
              ),
              child: Text(
                unreadCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                ),
              ),
            ),
        ],
      ),
      onTap: () {
        // فتح شاشة الدردشة
        Get.to(() => ChatScreen(group: group));
      },
    );
  }

  void _showNewChatDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('محادثة جديدة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _showNewDirectMessageDialog();
                },
                child: const Text('محادثة مباشرة'),
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _showNewGroupDialog();
                },
                child: const Text('مجموعة جديدة'),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showNewDirectMessageDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('محادثة مباشرة'),
          content: SizedBox(
            width: double.maxFinite,
            child: Obx(() {
              final users = _chatController.users.where(
                (u) => u.id != _chatController.currentUser.value?.id,
              ).toList();
              
              if (users.isEmpty) {
                return const Center(
                  child: Text('لا يوجد مستخدمون'),
                );
              }
              
              return ListView.builder(
                shrinkWrap: true,
                itemCount: users.length,
                itemBuilder: (context, index) {
                  final user = users[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppColors.primary,
                      child: Text(
                        user.name.isNotEmpty ? user.name[0] : '?',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    title: Text(user.name),
                    onTap: () {
                      Navigator.pop(context);
                      _createDirectMessage(user);
                    },
                  );
                },
              );
            }),
          ),
        );
      },
    );
  }

  void _showNewGroupDialog() {
    final selectedUsers = <User>[].obs;
    final nameController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('مجموعة جديدة'),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'اسم المجموعة',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                const Text('اختر المستخدمين:'),
                const SizedBox(height: 8),
                Expanded(
                  child: Obx(() {
                    final users = _chatController.users.where(
                      (u) => u.id != _chatController.currentUser.value?.id,
                    ).toList();
                    
                    if (users.isEmpty) {
                      return const Center(
                        child: Text('لا يوجد مستخدمون'),
                      );
                    }
                    
                    return ListView.builder(
                      shrinkWrap: true,
                      itemCount: users.length,
                      itemBuilder: (context, index) {
                        final user = users[index];
                        final isSelected = selectedUsers.contains(user);
                        
                        return CheckboxListTile(
                          title: Text(user.name),
                          value: isSelected,
                          onChanged: (value) {
                            if (value == true) {
                              selectedUsers.add(user);
                            } else {
                              selectedUsers.remove(user);
                            }
                          },
                        );
                      },
                    );
                  }),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _createGroup(nameController.text, selectedUsers);
              },
              child: const Text('إنشاء'),
            ),
          ],
        );
      },
    );
  }

  void _createDirectMessage(User user) {
    // إنشاء محادثة مباشرة
    // يمكن تنفيذ هذا لاحقًا
  }

  void _createGroup(String name, List<User> users) {
    // إنشاء مجموعة جديدة
    // يمكن تنفيذ هذا لاحقًا
  }
}
