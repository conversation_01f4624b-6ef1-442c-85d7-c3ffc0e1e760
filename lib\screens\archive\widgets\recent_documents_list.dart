import 'package:flutter/material.dart';
import 'package:flutter_application_2/models/archive_document_model.dart';

import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/utils/date_formatter.dart';
import 'package:flutter_application_2/utils/file_processor.dart';

/// قائمة الوثائق الحديثة
class RecentDocumentsList extends StatelessWidget {
  /// قائمة الوثائق
  final List<ArchiveDocument> documents;

  /// دالة يتم استدعاؤها عند النقر على وثيقة
  final Function(ArchiveDocument) onDocumentTap;

  const RecentDocumentsList({
    super.key,
    required this.documents,
    required this.onDocumentTap,
  });

  @override
  Widget build(BuildContext context) {
    if (documents.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.description_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد وثائق حديثة',
              style: AppStyles.subtitle1.copyWith(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      itemCount: documents.length,
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        final document = documents[index];
        return _buildDocumentItem(document);
      },
    );
  }

  Widget _buildDocumentItem(ArchiveDocument document) {
    final fileType = FileProcessor.getFileType(document.filePath);

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: _getFileColor(fileType).withAlpha(51),
        child: Icon(
          _getFileIcon(fileType),
          color: _getFileColor(fileType),
        ),
      ),
      title: Text(
        document.title,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (document.description != null && document.description!.isNotEmpty)
            Text(
              document.description!,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: AppStyles.body2,
            ),
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 12,
                color: Colors.grey.shade600,
              ),
              const SizedBox(width: 4),
              Text(
                DateFormatter.formatDate(document.createdAt),
                style: AppStyles.caption,
              ),
            ],
          ),
        ],
      ),
      trailing: Icon(
        Icons.chevron_right,
        color: Colors.grey.shade400,
      ),
      onTap: () => onDocumentTap(document),
    );
  }

  IconData _getFileIcon(String fileType) {
    switch (fileType) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'image':
        return Icons.image;
      case 'word':
        return Icons.description;
      case 'excel':
        return Icons.table_chart;
      case 'powerpoint':
        return Icons.slideshow;
      case 'text':
        return Icons.text_snippet;
      case 'video':
        return Icons.video_file;
      case 'audio':
        return Icons.audio_file;
      default:
        return Icons.insert_drive_file;
    }
  }

  Color _getFileColor(String fileType) {
    switch (fileType) {
      case 'pdf':
        return Colors.red;
      case 'image':
        return Colors.blue;
      case 'word':
        return Colors.indigo;
      case 'excel':
        return Colors.green;
      case 'powerpoint':
        return Colors.orange;
      case 'text':
        return Colors.grey;
      case 'video':
        return Colors.purple;
      case 'audio':
        return Colors.teal;
      default:
        return Colors.blueGrey;
    }
  }
}
