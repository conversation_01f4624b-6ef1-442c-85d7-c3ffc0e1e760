import 'package:flutter/material.dart';
import 'app_colors.dart';
import '../utils/responsive_helper.dart';

/// أنماط التطبيق
///
/// ملاحظة مهمة: تم تحويل جميع الأنماط من ثابتة (const) إلى ديناميكية (get)
/// لدعم الوضع الداكن بشكل أفضل. عند استخدام هذه الأنماط في الواجهات،
/// يجب الانتباه إلى عدم استخدام const مع هذه الأنماط لأنها تتغير حسب وضع السمة.
///
/// مثال صحيح:
/// Text('نص', style: AppStyles.bodyMedium)
///
/// مثال خاطئ:
/// const Text('نص', style: AppStyles.bodyMedium)
class AppStyles {
  // Text styles - dynamic to support dark mode
  static TextStyle get headingLarge => TextStyle(
    fontSize: 28.0,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );

  static TextStyle get headingMedium => TextStyle(
    fontSize: 24.0,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );

  static TextStyle get headingSmall => TextStyle(
    fontSize: 20.0,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );

  static TextStyle get titleLarge => TextStyle(
    fontSize: 18.0,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
  );

  static TextStyle get titleMedium => TextStyle(
    fontSize: 16.0,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
  );

  static TextStyle get titleSmall => TextStyle(
    fontSize: 14.0,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
  );

  static TextStyle get bodyLarge => TextStyle(
    fontSize: 16.0,
    color: AppColors.textPrimary,
  );

  static TextStyle get bodyMedium => TextStyle(
    fontSize: 14.0,
    color: AppColors.textPrimary,
  );

  static TextStyle get bodySmall => TextStyle(
    fontSize: 12.0,
    color: AppColors.textSecondary,
  );

  // For compatibility with archive module
  static TextStyle get body1 => bodyLarge;
  static TextStyle get body2 => bodyMedium;
  static TextStyle get caption => captionText;
  static TextStyle get subtitle1 => titleMedium;
  static TextStyle get headline6 => headingSmall;
  static TextStyle get headline4 => headingLarge;

  static TextStyle get labelLarge => TextStyle(
    fontSize: 14.0,
    fontWeight: FontWeight.w500,
    color: AppColors.textSecondary,
  );

  static TextStyle get labelMedium => TextStyle(
    fontSize: 12.0,
    fontWeight: FontWeight.w500,
    color: AppColors.textSecondary,
  );

  static TextStyle get labelSmall => TextStyle(
    fontSize: 10.0,
    fontWeight: FontWeight.w500,
    color: AppColors.textSecondary,
  );

  static TextStyle get captionText => TextStyle(
    fontSize: 11.0,
    color: AppColors.textSecondary,
    fontStyle: FontStyle.italic,
  );

  static TextStyle get captionSmall => TextStyle(
    fontSize: 10.0,
    color: AppColors.textSecondary,
  );

  static TextStyle get captionMedium => TextStyle(
    fontSize: 11.0,
    color: AppColors.textSecondary,
  );

  // Button styles - dynamic to support dark mode
  static ButtonStyle get primaryButtonStyle => ElevatedButton.styleFrom(
    backgroundColor: AppColors.buttonPrimary,
    foregroundColor: Colors.white,
    padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 24.0),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8.0),
    ),
  );

  static ButtonStyle get secondaryButtonStyle => ElevatedButton.styleFrom(
    backgroundColor: AppColors.card,
    foregroundColor: AppColors.buttonPrimary,
    padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 24.0),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8.0),
      side: BorderSide(color: AppColors.buttonPrimary),
    ),
  );

  static ButtonStyle get textButtonStyle => TextButton.styleFrom(
    foregroundColor: AppColors.buttonPrimary,
    padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
  );

  // Input decoration - dynamic to support dark mode
  static InputDecoration inputDecoration({
    required String labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: BorderSide(color: AppColors.border),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: BorderSide(color: AppColors.border),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: BorderSide(color: AppColors.primary, width: 2.0),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: BorderSide(color: AppColors.error),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0),
        borderSide: BorderSide(color: AppColors.error, width: 2.0),
      ),
      contentPadding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
    );
  }

  // Card decoration - dynamic to support dark mode
  static BoxDecoration get cardDecoration => BoxDecoration(
    color: AppColors.card,
    borderRadius: BorderRadius.circular(8.0),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
        blurRadius: 8.0,
        offset: const Offset(0, 2),
      ),
    ],
  );

  // Dialog decoration - dynamic to support dark mode
  static BoxDecoration get dialogDecoration => BoxDecoration(
    color: AppColors.dialog,
    borderRadius: BorderRadius.circular(12.0),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withAlpha(26), // 0.1 * 255 = ~26
        blurRadius: 16.0,
        offset: const Offset(0, 4),
      ),
    ],
  );

  // Padding
  static const EdgeInsets paddingSmall = EdgeInsets.all(8.0);
  static const EdgeInsets paddingMedium = EdgeInsets.all(16.0);
  static const EdgeInsets paddingLarge = EdgeInsets.all(24.0);

  // Margin
  static const EdgeInsets marginSmall = EdgeInsets.all(8.0);
  static const EdgeInsets marginMedium = EdgeInsets.all(16.0);
  static const EdgeInsets marginLarge = EdgeInsets.all(24.0);

  // Border radius
  static final BorderRadius borderRadiusSmall = BorderRadius.circular(4.0);
  static final BorderRadius borderRadiusMedium = BorderRadius.circular(8.0);
  static final BorderRadius borderRadiusLarge = BorderRadius.circular(12.0);

  // Responsive text styles - dynamic to support dark mode
  static TextStyle getResponsiveHeadingLarge(BuildContext context) {
    return TextStyle(
      fontSize: ResponsiveHelper.getAdaptiveFontSize(context, 28.0),
      fontWeight: FontWeight.bold,
      color: AppColors.textPrimary,
    );
  }

  static TextStyle getResponsiveHeadingMedium(BuildContext context) {
    return TextStyle(
      fontSize: ResponsiveHelper.getAdaptiveFontSize(context, 24.0),
      fontWeight: FontWeight.bold,
      color: AppColors.textPrimary,
    );
  }

  static TextStyle getResponsiveHeadingSmall(BuildContext context) {
    return TextStyle(
      fontSize: ResponsiveHelper.getAdaptiveFontSize(context, 20.0),
      fontWeight: FontWeight.bold,
      color: AppColors.textPrimary,
    );
  }

  static TextStyle getResponsiveBodyMedium(BuildContext context) {
    return TextStyle(
      fontSize: ResponsiveHelper.getAdaptiveFontSize(context, 14.0),
      color: AppColors.textPrimary,
    );
  }

  // Responsive padding
  static EdgeInsets getResponsivePadding(BuildContext context) {
    return ResponsiveHelper.getScreenPadding(context);
  }

  // Responsive layout helpers
  static double getContentMaxWidth(BuildContext context) {
    if (ResponsiveHelper.isDesktop(context)) {
      return 1200.0;
    } else if (ResponsiveHelper.isTablet(context)) {
      return 900.0;
    } else {
      return ResponsiveHelper.screenWidth(context);
    }
  }

  // Responsive card width
  static double getCardWidth(BuildContext context) {
    if (ResponsiveHelper.isDesktop(context)) {
      return 350.0;
    } else if (ResponsiveHelper.isTablet(context)) {
      return 300.0;
    } else {
      return ResponsiveHelper.screenWidth(context) - 32.0;
    }
  }

  // Responsive grid column count
  static int getGridColumnCount(BuildContext context) {
    return ResponsiveHelper.getGridColumnCount(context);
  }
}
