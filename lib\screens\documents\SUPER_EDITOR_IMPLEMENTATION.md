# تنفيذ SuperEditor في نظام إدارة المستندات

## نظرة عامة

هذا الملف يشرح كيفية تنفيذ مكتبة SuperEditor بشكل كامل في نظام إدارة المستندات. حالياً، يستخدم النظام `TextField` للتوافق مع الكود الحالي، ولكن في المستقبل سيتم استخدام SuperEditor لتوفير تجربة تحرير نصوص غنية.

## الخطوات المطلوبة

### 1. إضافة المتغيرات اللازمة

```dart
// متغيرات المحرر المتقدم
late MutableDocument _document;
late DocumentComposer _composer;
late Editor _editor;

// تعريف التنسيقات
final boldAttribution = const NamedAttribution('bold');
final italicAttribution = const NamedAttribution('italic');
final underlineAttribution = const NamedAttribution('underline');
```

### 2. تحديث طريقة `_initializeSuperEditor`

```dart
/// تهيئة محرر النصوص
void _initializeSuperEditor(String content) {
  // تحويل النص إلى فقرات
  final List<DocumentNode> nodes = [];
  
  if (content.isEmpty) {
    // إذا كان المحتوى فارغًا، أضف فقرة فارغة
    nodes.add(
      ParagraphNode(
        id: DocumentNode.id(),
        text: AttributedText(),
      ),
    );
  } else {
    // تقسيم المحتوى إلى فقرات
    final paragraphs = content.split('\n');
    for (final paragraph in paragraphs) {
      nodes.add(
        ParagraphNode(
          id: DocumentNode.id(),
          text: AttributedText(paragraph),
        ),
      );
    }
  }
  
  // إنشاء وثيقة جديدة
  _document = MutableDocument(nodes: nodes);
  
  // إنشاء محرر الوثيقة
  _composer = MutableDocumentComposer();
  
  // إنشاء محرر
  _editor = createDefaultDocumentEditor(document: _document, composer: _composer);
  
  // تهيئة المتحكم النصي للتوافق مع الكود القديم
  _documentController.textController.value =
      TextEditingController(text: content);
}
```

### 3. تحديث طريقة `_getDocumentContent`

```dart
/// استخراج محتوى المستند من محرر النصوص
String _getDocumentContent() {
  // استخراج النص من SuperEditor
  final buffer = StringBuffer();
  
  for (final node in _document.nodes) {
    if (node is ParagraphNode) {
      buffer.write(node.text.toPlainText());
      buffer.write('\n');
    }
  }
  
  final text = buffer.toString().trim();
  
  // تحويل النص إلى JSON
  return TextDocument.textToJson(text);
}
```

### 4. تنفيذ وظائف التنسيق

#### تطبيق تنسيق التغميق

```dart
/// تطبيق تنسيق التغميق على النص المحدد
void _applyBoldFormat() {
  final selection = _composer.selection;
  if (selection != null && !selection.isCollapsed) {
    // إنشاء عملية تحرير لتطبيق التنسيق
    _editor.execute([
      AddAttributionToTextRequest(
        documentRange: selection,
        attribution: boldAttribution,
      ),
    ]);
  } else {
    // عرض رسالة للمستخدم إذا لم يتم تحديد نص
    Get.snackbar(
      'تنسيق النص',
      'يرجى تحديد النص أولاً',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );
  }
}
```

#### تطبيق تنسيق المائل

```dart
/// تطبيق تنسيق المائل على النص المحدد
void _applyItalicFormat() {
  final selection = _composer.selection;
  if (selection != null && !selection.isCollapsed) {
    // إنشاء عملية تحرير لتطبيق التنسيق
    _editor.execute([
      AddAttributionToTextRequest(
        documentRange: selection,
        attribution: italicAttribution,
      ),
    ]);
  } else {
    // عرض رسالة للمستخدم إذا لم يتم تحديد نص
    Get.snackbar(
      'تنسيق النص',
      'يرجى تحديد النص أولاً',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );
  }
}
```

#### تطبيق تنسيق التسطير

```dart
/// تطبيق تنسيق التسطير على النص المحدد
void _applyUnderlineFormat() {
  final selection = _composer.selection;
  if (selection != null && !selection.isCollapsed) {
    // إنشاء عملية تحرير لتطبيق التنسيق
    _editor.execute([
      AddAttributionToTextRequest(
        documentRange: selection,
        attribution: underlineAttribution,
      ),
    ]);
  } else {
    // عرض رسالة للمستخدم إذا لم يتم تحديد نص
    Get.snackbar(
      'تنسيق النص',
      'يرجى تحديد النص أولاً',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.blue.shade100,
      colorText: Colors.blue.shade800,
    );
  }
}
```

### 5. تحديث واجهة المستخدم

```dart
SuperEditor.standard(
  editor: _editor,
  focusNode: _editorFocusNode,
  inputSource: _isEditing
      ? TextInputSource.keyboard
      : TextInputSource.none,
  stylesheet: defaultStylesheet.copyWith(
    documentPadding: const EdgeInsets.all(8),
    paragraphStyle: const TextStyle(
      fontSize: 16,
      height: 1.5,
    ),
  ),
  selectionStyle: const SelectionStyles(
    selectionColor: Colors.lightBlueAccent,
  ),
)
```

### 6. تحديث طريقة `dispose`

```dart
@override
void dispose() {
  // تحرير الموارد
  _titleController.dispose();
  _titleFocusNode.dispose();
  _editorFocusNode.dispose();
  
  // تحرير موارد المحرر
  if (_documentController.textController.value != null) {
    _documentController.textController.value!.dispose();
  }
  
  // تحرير موارد SuperEditor
  _composer.dispose();
  
  super.dispose();
}
```

## ملاحظات إضافية

1. يجب التأكد من أن مكتبة SuperEditor متوافقة مع الإصدار الحالي من Flutter.
2. قد تحتاج إلى تعديل الكود بناءً على التغييرات في واجهة برمجة التطبيقات (API) لمكتبة SuperEditor.
3. يمكن إضافة المزيد من وظائف التنسيق مثل تغيير لون النص وحجمه.
4. يمكن إضافة دعم للصور والجداول والروابط.
5. يمكن إضافة دعم للتراجع والإعادة.

## المراجع

- [SuperEditor GitHub](https://github.com/superlistapp/super_editor)
- [SuperEditor Pub.dev](https://pub.dev/packages/super_editor)
