import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/unified_chat_controller.dart';
import '../../controllers/user_controller.dart';
import '../../models/chat_group_model.dart';
import '../../models/user_model.dart';
import '../../routes/app_routes.dart';
import '../../services/message_service.dart';

/// شاشة قائمة المحادثات الموحدة
/// تستخدم وحدة التحكم الموحدة للمحادثات
class UnifiedChatListScreen extends StatefulWidget {
  const UnifiedChatListScreen({super.key});

  @override
  State<UnifiedChatListScreen> createState() => _UnifiedChatListScreenState();
}

class _UnifiedChatListScreenState extends State<UnifiedChatListScreen> {
  final _searchController = TextEditingController();
  final _chatController = Get.find<UnifiedChatController>();
  final _authController = Get.find<AuthController>();
  final _userController = Get.find<UserController>();

  // قائمة المحادثات المفلترة للبحث
  final RxList<ChatGroup> _filteredGroups = <ChatGroup>[].obs;
  // حالة البحث
  final RxBool _isSearching = false.obs;

  @override
  void initState() {
    super.initState();
    _loadChatGroups();

    // إضافة مستمع للبحث
    _searchController.addListener(_filterChatGroups);
  }

  /// تحميل مجموعات المحادثة
  Future<void> _loadChatGroups() async {
    try {
      final currentUser = _authController.currentUser.value;
      if (currentUser == null) {
        Get.snackbar(
          'خطأ',
          'يجب تسجيل الدخول أولاً',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      // تعيين المستخدم الحالي في وحدة التحكم الموحدة إذا لم يكن معينًا بالفعل
      if (_chatController.currentUser.value == null) {
        await _chatController.setCurrentUser(currentUser);
      } else {
        // تحميل المجموعات فقط
        await _chatController.loadGroups();
      }

      // تحديث قائمة المحادثات المفلترة
      _filteredGroups.value = _chatController.groups;
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل المحادثات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }

  /// تصفية مجموعات المحادثة بناءً على نص البحث
  void _filterChatGroups() {
    final searchText = _searchController.text.toLowerCase();

    if (searchText.isEmpty) {
      _filteredGroups.value = _chatController.groups;
      _isSearching.value = false;
      return;
    }

    _isSearching.value = true;
    _filteredGroups.value = _chatController.groups
        .where((group) => group.name.toLowerCase().contains(searchText))
        .toList();
  }

  @override
  void dispose() {
    _searchController.removeListener(_filterChatGroups);
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('المحادثات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              _isSearching.value = true;
              FocusScope.of(context).requestFocus(FocusNode());
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadChatGroups,
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showNewChatOptions();
        },
        child: const Icon(Icons.add_comment),
      ),
      body: Column(
        children: [
          // شريط البحث
          Obx(() => Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'بحث في المحادثات',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _isSearching.value
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _isSearching.value = false;
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade100,
                  ),
                ),
              )),

          // قائمة المحادثات
          Expanded(
            child: RefreshIndicator(
              onRefresh: _loadChatGroups,
              child: Obx(() {
                // عرض مؤشر التحميل عند تحميل المجموعات
                if (_chatController.isLoadingGroups.value) {
                  return const Center(child: CircularProgressIndicator());
                }

                // عرض رسالة عندما لا توجد محادثات
                if (_chatController.groups.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.chat_bubble_outline,
                          size: 64,
                          color: Colors.grey.shade300,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد محادثات بعد',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'ابدأ محادثة جديدة بالضغط على زر الإضافة',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                }

                // عرض رسالة عندما لا توجد نتائج للبحث
                if (_isSearching.value && _filteredGroups.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: Colors.grey.shade300,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد نتائج للبحث',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                // عرض قائمة المحادثات
                return ListView.builder(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: _filteredGroups.length,
                  itemBuilder: (context, index) {
                    final group = _filteredGroups[index];
                    return _buildChatGroupItem(group);
                  },
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض خيارات المحادثة الجديدة
  void _showNewChatOptions() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const CircleAvatar(
                backgroundColor: AppColors.primary,
                child: Icon(Icons.person, color: Colors.white),
              ),
              title: const Text('محادثة مباشرة'),
              subtitle: const Text('بدء محادثة مع مستخدم واحد'),
              onTap: () {
                Get.back();
                _showNewDirectMessageDialog();
              },
            ),
            const Divider(),
            ListTile(
              leading: const CircleAvatar(
                backgroundColor: AppColors.accent,
                child: Icon(Icons.group, color: Colors.white),
              ),
              title: const Text('مجموعة جديدة'),
              subtitle: const Text('إنشاء مجموعة محادثة مع عدة مستخدمين'),
              onTap: () {
                Get.back();
                Get.toNamed(AppRoutes.createGroupChat);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// عرض مربع حوار لاختيار مستخدم للمحادثة المباشرة
  void _showNewDirectMessageDialog() async {
    // عرض مؤشر التحميل
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    try {
      // تحميل المستخدمين
      await _userController.loadAllUsers();
      Get.back();

      // إنشاء قائمة المستخدمين المفلترة (استبعاد المستخدم الحالي)
      final currentUserId = _authController.currentUser.value?.id;
      final filteredUsers = _userController.users
          .where((user) => user.id != currentUserId && user.isActive)
          .toList();

      if (filteredUsers.isEmpty) {
        Get.snackbar(
          'تنبيه',
          'لا يوجد مستخدمين متاحين للمحادثة',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      // عرض مربع حوار لاختيار مستخدم
      Get.dialog(
        AlertDialog(
          title: const Text('اختر مستخدم للمحادثة'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: ListView.builder(
              itemCount: filteredUsers.length,
              itemBuilder: (context, index) {
                final user = filteredUsers[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: AppColors.primary,
                    child: Text(
                      user.name.substring(0, 1).toUpperCase(),
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                  title: Text(user.name),
                  subtitle: Text(user.email),
                  onTap: () {
                    Get.back();
                    _startDirectChat(user);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('إلغاء'),
            ),
          ],
        ),
      );
    } catch (e) {
      Get.back();
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل المستخدمين: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// بدء محادثة مباشرة مع مستخدم
  Future<void> _startDirectChat(User user) async {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return;

    // عرض مؤشر التحميل
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    try {
      // استخدام MessageService مباشرة
      final messageService = MessageService();
      final chatGroup = await messageService.getOrCreateDirectMessageGroup(
        currentUser.id,
        user.id,
      );

      // تحديث قائمة المجموعات
      if (!_chatController.groups.any((g) => g.id == chatGroup.id)) {
        _chatController.groups.add(chatGroup);
      }

      Get.back(); // إغلاق مؤشر التحميل

      // الانتقال إلى شاشة المحادثة
      Get.toNamed(
        AppRoutes.unifiedChatDetail,
        arguments: {'chatGroup': chatGroup},
      );
    } catch (e) {
      Get.back(); // إغلاق مؤشر التحميل
      Get.snackbar(
        'خطأ',
        'فشل إنشاء المحادثة: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// بناء عنصر مجموعة محادثة
  Widget _buildChatGroupItem(ChatGroup group) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor:
            group.isDirectMessage ? AppColors.accent : AppColors.primary,
        child: Text(
          group.name.substring(0, 1).toUpperCase(),
          style: const TextStyle(color: Colors.white),
        ),
      ),
      title: Text(group.name),
      subtitle: Text(
        group.description ?? 'محادثة',
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: group.isDirectMessage
          ? Icon(
              Icons.circle,
              size: 12,
              color: Colors.green,
            )
          : Text(
              '${group.memberCount} عضو',
              style: TextStyle(color: Colors.grey.shade600),
            ),
      onTap: () {
        Get.toNamed(
          AppRoutes.unifiedChatDetail,
          arguments: {'chatGroup': group},
        );
      },
    );
  }
}
