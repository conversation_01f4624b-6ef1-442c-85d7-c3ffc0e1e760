import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/admin_controller.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/user_controller.dart';
import '../../models/task_model.dart';
import '../../models/task_status_enum.dart'; // إضافة استيراد نموذج حالة المهمة
import '../../models/user_model.dart';
import '../../utils/responsive_helper.dart';

/// تبويب التحليلات
///
/// يوفر واجهة لعرض إحصائيات وتحليلات النظام
class AnalyticsTab extends StatefulWidget {
  const AnalyticsTab({super.key});

  @override
  State<AnalyticsTab> createState() => _AnalyticsTabState();
}

class _AnalyticsTabState extends State<AnalyticsTab> {
  final AdminController _adminController = Get.find<AdminController>();
  final TaskController _taskController = Get.find<TaskController>();
  final UserController _userController = Get.find<UserController>();

  @override
  void initState() {
    super.initState();

    // تحميل البيانات
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    await _taskController.loadAllTasks();
    await _userController.loadAllUsers();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          Expanded(
            child: _buildAnalyticsContent(),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الصفحة
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'التحليلات والإحصائيات',
          style: AppStyles.titleLarge,
        ),
        ElevatedButton.icon(
          onPressed: _loadData,
          icon: const Icon(Icons.refresh),
          label: const Text('تحديث البيانات'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }

  /// بناء محتوى التحليلات
  Widget _buildAnalyticsContent() {
    return Obx(() {
      if (_taskController.isLoading.value || _userController.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      // استخدام ListView بدلاً من SingleChildScrollView مع Column
      // Using ListView instead of SingleChildScrollView with Column
      return ListView(
        children: [
          ResponsiveHelper.isDesktop(context)
              ? _buildDesktopLayout()
              : _buildMobileLayout(),
        ],
      );
    });
  }

  /// بناء تخطيط سطح المكتب
  Widget _buildDesktopLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min, // استخدام الحد الأدنى من المساحة
      children: [
        // صف الإحصائيات العامة
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'إجمالي المستخدمين',
                _userController.users.length.toString(),
                Icons.people,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'إجمالي المهام',
                _taskController.tasks.length.toString(),
                Icons.task,
                Colors.green,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'المهام المكتملة',
                _countTasksByStatus(TaskStatus.completed).toString(),
                Icons.check_circle,
                Colors.teal,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                'المهام المتأخرة',
                _countOverdueTasks().toString(),
                Icons.warning,
                Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // صف الرسوم البيانية
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسم بياني دائري لحالة المهام
            Expanded(
              flex: 1,
              child: SizedBox(
                height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
                child: _buildCard(
                  'توزيع المهام حسب الحالة',
                  _buildTaskStatusPieChart(), // إزالة SizedBox لمنع تحديد ارتفاع ثابت
                ),
              ),
            ),
            const SizedBox(width: 16),

            // رسم بياني شريطي للمهام حسب الشهر
            Expanded(
              flex: 2,
              child: SizedBox(
                height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
                child: _buildCard(
                  'المهام حسب الشهر',
                  _buildTasksByMonthBarChart(), // إزالة SizedBox لمنع تحديد ارتفاع ثابت
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // صف الرسوم البيانية الإضافية
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسم بياني للمستخدمين النشطين
            Expanded(
              child: SizedBox(
                height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
                child: _buildCard(
                  'المستخدمين النشطين وغير النشطين',
                  _buildActiveUsersChart(), // إزالة SizedBox لمنع تحديد ارتفاع ثابت
                ),
              ),
            ),
            const SizedBox(width: 16),

            // رسم بياني للمهام حسب الأولوية
            Expanded(
              child: SizedBox(
                height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
                child: _buildCard(
                  'توزيع المهام حسب الأولوية',
                  _buildTasksByPriorityChart(), // إزالة SizedBox لمنع تحديد ارتفاع ثابت
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // جدول أحدث النشاطات
        SizedBox(
          height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
          child: _buildCard(
            'أحدث النشاطات',
            _buildRecentActivitiesTable(), // إزالة SizedBox لمنع تحديد ارتفاع ثابت
          ),
        ),
      ],
    );
  }

  /// بناء تخطيط الجوال
  Widget _buildMobileLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min, // استخدام الحد الأدنى من المساحة
      children: [
        // الإحصائيات العامة
        _buildStatCard(
          'إجمالي المستخدمين',
          _userController.users.length.toString(),
          Icons.people,
          Colors.blue,
        ),
        const SizedBox(height: 16),
        _buildStatCard(
          'إجمالي المهام',
          _taskController.tasks.length.toString(),
          Icons.task,
          Colors.green,
        ),
        const SizedBox(height: 16),
        _buildStatCard(
          'المهام المكتملة',
          _countTasksByStatus(TaskStatus.completed).toString(),
          Icons.check_circle,
          Colors.teal,
        ),
        const SizedBox(height: 16),
        _buildStatCard(
          'المهام المتأخرة',
          _countOverdueTasks().toString(),
          Icons.warning,
          Colors.red,
        ),
        const SizedBox(height: 24),

        // رسم بياني دائري لحالة المهام
        SizedBox(
          height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
          child: _buildCard(
            'توزيع المهام حسب الحالة',
            _buildTaskStatusPieChart(), // إزالة SizedBox لمنع تحديد ارتفاع ثابت
          ),
        ),
        const SizedBox(height: 24),

        // رسم بياني شريطي للمهام حسب الشهر
        SizedBox(
          height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
          child: _buildCard(
            'المهام حسب الشهر',
            _buildTasksByMonthBarChart(), // إزالة SizedBox لمنع تحديد ارتفاع ثابت
          ),
        ),
        const SizedBox(height: 24),

        // رسم بياني للمستخدمين النشطين
        SizedBox(
          height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
          child: _buildCard(
            'المستخدمين النشطين وغير النشطين',
            _buildActiveUsersChart(), // إزالة SizedBox لمنع تحديد ارتفاع ثابت
          ),
        ),
        const SizedBox(height: 24),

        // رسم بياني للمهام حسب الأولوية
        SizedBox(
          height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
          child: _buildCard(
            'توزيع المهام حسب الأولوية',
            _buildTasksByPriorityChart(), // إزالة SizedBox لمنع تحديد ارتفاع ثابت
          ),
        ),
        const SizedBox(height: 24),

        // جدول أحدث النشاطات
        SizedBox(
          height: 350, // زيادة الارتفاع لاستيعاب العنوان والمحتوى
          child: _buildCard(
            'أحدث النشاطات',
            _buildRecentActivitiesTable(), // إزالة SizedBox لمنع تحديد ارتفاع ثابت
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: AppStyles.titleSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              value,
              style: AppStyles.titleLarge.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة
  Widget _buildCard(String title, Widget content) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // استخدام الحد الأدنى من المساحة
          children: [
            Text(
              title,
              style: AppStyles.titleMedium,
            ),
            const Divider(),
            const SizedBox(height: 8),
            // استخدام Expanded بدلاً من Flexible لضمان أخذ المساحة المتاحة
            Expanded(
              child: content,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء رسم بياني دائري لحالة المهام
  Widget _buildTaskStatusPieChart() {
    // التحقق من وجود مهام
    if (_taskController.tasks.isEmpty) {
      return const Center(
        child: Text('لا توجد مهام لعرضها'),
      );
    }

    // حساب عدد المهام لكل حالة
    final pendingCount = _countTasksByStatus(TaskStatus.pending);
    final inProgressCount = _countTasksByStatus(TaskStatus.inProgress);
    final waitingCount = _countTasksByStatus(TaskStatus.waitingForInfo);
    final completedCount = _countTasksByStatus(TaskStatus.completed);

    // التحقق من وجود بيانات للعرض
    if (pendingCount == 0 && inProgressCount == 0 && waitingCount == 0 && completedCount == 0) {
      return const Center(
        child: Text('لا توجد بيانات كافية لعرض الرسم البياني'),
      );
    }

    // استخدام SizedBox لتحديد ارتفاع ثابت للرسم البياني
    // Using SizedBox to set a fixed height for the chart
    return SizedBox(
      height: 250, // ارتفاع ثابت للرسم البياني
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: AspectRatio(
              aspectRatio: 1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على شكل دائري
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: pendingCount.toDouble(),
                      title: '$pendingCount',
                      color: AppColors.statusPending,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: inProgressCount.toDouble(),
                      title: '$inProgressCount',
                      color: AppColors.statusInProgress,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: waitingCount.toDouble(),
                      title: '$waitingCount',
                      color: AppColors.statusWaitingForInfo,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: completedCount.toDouble(),
                      title: '$completedCount',
                      color: AppColors.statusCompleted,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                  startDegreeOffset: 180,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLegendItem('قيد الانتظار', AppColors.statusPending, pendingCount),
              const SizedBox(height: 8),
              _buildLegendItem('قيد التنفيذ', AppColors.statusInProgress, inProgressCount),
              const SizedBox(height: 8),
              _buildLegendItem('في انتظار المراجعة', AppColors.statusWaitingForInfo, waitingCount),
              const SizedBox(height: 8),
              _buildLegendItem('مكتملة', AppColors.statusCompleted, completedCount),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عنصر وسيلة الإيضاح
  Widget _buildLegendItem(String label, Color color, int count) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          color: color,
        ),
        const SizedBox(width: 8),
        Text('$label ($count)'),
      ],
    );
  }

  /// بناء رسم بياني شريطي للمهام حسب الشهر
  Widget _buildTasksByMonthBarChart() {
    final tasks = _taskController.tasks;

    // التحقق من وجود مهام
    if (tasks.isEmpty) {
      return const Center(
        child: Text('لا توجد مهام لعرضها'),
      );
    }

    // تجميع المهام حسب الشهر
    final Map<int, int> tasksByMonth = {};

    // تهيئة الأشهر
    for (int i = 1; i <= 12; i++) {
      tasksByMonth[i] = 0;
    }

    // حساب عدد المهام لكل شهر
    for (final task in tasks) {
      final month = task.createdAt.month;
      tasksByMonth[month] = (tasksByMonth[month] ?? 0) + 1;
    }

    // حساب القيمة القصوى للمحور Y مع التأكد من أنها ليست صفرًا
    // Calculate the maximum Y value ensuring it's not zero
    final maxValue = tasksByMonth.values.isEmpty
        ? 10.0 // قيمة افتراضية إذا كانت القائمة فارغة
        : tasksByMonth.values.reduce((a, b) => a > b ? a : b).toDouble();

    // التأكد من أن القيمة القصوى ليست صفرًا أو قيمة غير محددة لتجنب مشاكل العرض
    // Ensure the maximum value is not zero, NaN, or Infinity to avoid display issues
    double safeMaxY;
    if (maxValue <= 0 || maxValue.isNaN || maxValue.isInfinite) {
      safeMaxY = 10.0; // قيمة افتراضية آمنة
    } else {
      safeMaxY = maxValue * 1.2;
    }

    // استخدام AspectRatio لضمان نسبة عرض إلى ارتفاع ثابتة للرسم البياني
    return AspectRatio(
      aspectRatio: 1.5, // نسبة العرض إلى الارتفاع 1.5:1
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: safeMaxY,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                final month = group.x + 1;
                final monthName = DateFormat('MMMM').format(DateTime(DateTime.now().year, month));
                // التأكد من أن القيمة ليست NaN أو Infinity قبل تحويلها إلى عدد صحيح
                final value = rod.toY.isFinite ? rod.toY.toInt() : 0;
                return BarTooltipItem(
                  '$monthName: $value',
                  const TextStyle(color: Colors.white),
                );
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final month = value.toInt() + 1;
                  return SideTitleWidget(
                    meta: meta,
                    child: Text(
                      DateFormat('MMM').format(DateTime(DateTime.now().year, month)),
                      style: const TextStyle(fontSize: 10),
                    ),
                  );
                },
                reservedSize: 30,
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  if (value == 0) return const SizedBox.shrink();
                  // التأكد من أن القيمة ليست NaN أو Infinity قبل تحويلها إلى عدد صحيح
                  final intValue = value.isFinite ? value.toInt() : 0;
                  return SideTitleWidget(
                    meta: meta,
                    child: Text(
                      intValue.toString(),
                      style: const TextStyle(fontSize: 10),
                    ),
                  );
                },
                reservedSize: 30,
              ),
            ),
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false),
          barGroups: tasksByMonth.entries.map((entry) {
            return BarChartGroupData(
              x: entry.key - 1,
              barRods: [
                BarChartRodData(
                  toY: entry.value.toDouble(),
                  color: AppColors.primary,
                  width: 16,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(4),
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  /// بناء رسم بياني للمستخدمين النشطين
  Widget _buildActiveUsersChart() {
    final users = _userController.users;

    // التحقق من وجود مستخدمين
    if (users.isEmpty) {
      return const Center(
        child: Text('لا يوجد مستخدمين لعرضهم'),
      );
    }

    // حساب عدد المستخدمين النشطين وغير النشطين
    final activeCount = users.where((user) => user.isActive).length;
    final inactiveCount = users.length - activeCount;

    // التحقق من وجود بيانات للعرض
    if (activeCount == 0 && inactiveCount == 0) {
      return const Center(
        child: Text('لا توجد بيانات كافية لعرض الرسم البياني'),
      );
    }

    // استخدام SizedBox لتحديد ارتفاع ثابت للرسم البياني
    // Using SizedBox to set a fixed height for the chart
    return SizedBox(
      height: 250, // ارتفاع ثابت للرسم البياني
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: AspectRatio(
              aspectRatio: 1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على شكل دائري
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: activeCount.toDouble(),
                      title: '$activeCount',
                      color: Colors.green,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: inactiveCount.toDouble(),
                      title: '$inactiveCount',
                      color: Colors.red,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                  startDegreeOffset: 180,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLegendItem('نشط', Colors.green, activeCount),
              const SizedBox(height: 8),
              _buildLegendItem('غير نشط', Colors.red, inactiveCount),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء رسم بياني للمهام حسب الأولوية
  Widget _buildTasksByPriorityChart() {
    final tasks = _taskController.tasks;

    // التحقق من وجود مهام
    if (tasks.isEmpty) {
      return const Center(
        child: Text('لا توجد مهام لعرضها'),
      );
    }

    // حساب عدد المهام لكل أولوية
    final lowCount = tasks.where((task) => task.priority == TaskPriority.low).length;
    final mediumCount = tasks.where((task) => task.priority == TaskPriority.medium).length;
    final highCount = tasks.where((task) => task.priority == TaskPriority.high).length;
    final urgentCount = tasks.where((task) => task.priority == TaskPriority.urgent).length;

    // التحقق من وجود بيانات للعرض
    if (lowCount == 0 && mediumCount == 0 && highCount == 0 && urgentCount == 0) {
      return const Center(
        child: Text('لا توجد بيانات كافية لعرض الرسم البياني'),
      );
    }

    // استخدام SizedBox لتحديد ارتفاع ثابت للرسم البياني
    // Using SizedBox to set a fixed height for the chart
    return SizedBox(
      height: 250, // ارتفاع ثابت للرسم البياني
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: AspectRatio(
              aspectRatio: 1, // نسبة العرض إلى الارتفاع 1:1 للحفاظ على شكل دائري
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: lowCount.toDouble(),
                      title: '$lowCount',
                      color: Colors.blue,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: mediumCount.toDouble(),
                      title: '$mediumCount',
                      color: Colors.green,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: highCount.toDouble(),
                      title: '$highCount',
                      color: Colors.orange,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: urgentCount.toDouble(),
                      title: '$urgentCount',
                      color: Colors.red,
                      radius: 60,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                  startDegreeOffset: 180,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLegendItem('منخفضة', Colors.blue, lowCount),
              const SizedBox(height: 8),
              _buildLegendItem('متوسطة', Colors.green, mediumCount),
              const SizedBox(height: 8),
              _buildLegendItem('عالية', Colors.orange, highCount),
              const SizedBox(height: 8),
              _buildLegendItem('عاجلة', Colors.red, urgentCount),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء جدول أحدث النشاطات
  Widget _buildRecentActivitiesTable() {
    final logs = _adminController.logs;

    if (logs.isEmpty) {
      return const Center(
        child: Text('لا توجد نشاطات حديثة'),
      );
    }

    // التأكد من وجود سجلات قبل إنشاء الجدول
    final logsToShow = logs.take(10).toList();
    if (logsToShow.isEmpty) {
      return const Center(
        child: Text('لا توجد نشاطات حديثة'),
      );
    }

    return SingleChildScrollView(
      child: DataTable(
        columns: const [
          DataColumn(label: Text('المستخدم')),
          DataColumn(label: Text('الإجراء')),
          DataColumn(label: Text('التفاصيل')),
          DataColumn(label: Text('التاريخ')),
        ],
        rows: logsToShow.map((log) {
          // محاولة العثور على المستخدم، أو استخدام مستخدم افتراضي إذا لم يتم العثور عليه
          final user = _userController.users.isNotEmpty
              ? _userController.users.firstWhere(
                  (user) => user.id == log.userId,
                  orElse: () => _userController.users.first,
                )
              : User(
                  id: 'unknown',
                  name: 'مستخدم غير معروف',
                  email: '',
                  password: '',
                  role: UserRole.employee,
                  isActive: true,
                  createdAt: DateTime.now(),
                );

          return DataRow(
            cells: [
              DataCell(Text(user.name)),
              DataCell(Text(log.action)),
              DataCell(Text(log.details)),
              DataCell(Text(_formatDateTime(log.timestamp))),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// حساب عدد المهام حسب الحالة
  int _countTasksByStatus(TaskStatus status) {
    return _taskController.tasks.where((task) => task.status == status).length;
  }

  /// حساب عدد المهام المتأخرة
  int _countOverdueTasks() {
    final now = DateTime.now();
    return _taskController.tasks.where((task) {
      return task.status != TaskStatus.completed &&
             task.dueDate != null &&
             task.dueDate!.isBefore(now);
    }).length;
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }
}
