import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import '../models/user_model.dart';

import '../services/auth_service.dart';

class AuthController extends GetxController {
  final Rx<User?> currentUser = Rx<User?>(null);
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;

  final AuthService _authService = AuthService();

  // Initialize with a mock admin user
  @override
  void onInit() {
    super.onInit();
    currentUser.value = User(
      id: 'kha-123',
      name: 'kha',
      email: '<EMAIL>',
      username: 'kha',
      password: 'password123',
      role: UserRole.superAdmin,
      createdAt: DateTime.now(),
      lastLogin: DateTime.now(),
    );
    // استدعاء update() لإعلام GetBuilder بالتغييرات
    update();
  }

  bool get isLoggedIn => currentUser.value != null;
  bool get isSuperAdmin => currentUser.value?.role == UserRole.superAdmin;
  bool get isAdmin => currentUser.value?.role == UserRole.admin;
  bool get isDepartmentManager =>
      currentUser.value?.role == UserRole.departmentManager;

  /// التحقق من كون المستخدم مدير (أي نوع من المديرين)
  bool get isAnyAdmin => isSuperAdmin || isAdmin || isDepartmentManager;

  /// التحقق من كون المستخدم لديه صلاحيات إدارية عامة (مدير النظام العام فقط)
  bool get hasSystemAdminRights => isSuperAdmin;

  /// التحقق من كون المستخدم لديه صلاحيات إدارة الإدارة (مدير النظام العام أو مدير إدارة)
  bool get hasDepartmentAdminRights => isSuperAdmin || isAdmin;

  /// التحقق من كون المستخدم هو kha (له صلاحيات خاصة لرؤية جميع الصفحات)
  bool get isKhaUser =>
      currentUser.value?.username == 'kha' || currentUser.value?.name == 'kha';

  /// التحقق من كون المستخدم يمكنه رؤية جميع الصفحات (مدير أو kha)
  bool get canSeeAllPages => isAnyAdmin || isKhaUser;

  // Load current user from storage
  Future<void> loadCurrentUser() async {
    isLoading.value = true;

    try {
      currentUser.value = await _authService.getCurrentUser();
      error.value = '';
    } catch (e) {
      error.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  // Login user
  Future<bool> login(String email, String password) async {
    isLoading.value = true;
    error.value = '';
    update();

    try {
      // تسجيل الدخول واستلام بيانات المستخدم
      currentUser.value = await _authService.login(email, password);

      // التحقق من نجاح تسجيل الدخول
      if (currentUser.value != null) {
        // طباعة معلومات تشخيصية
        debugPrint(
            'تم تسجيل الدخول للمستخدم: ${currentUser.value!.name} (${currentUser.value!.id})');
        debugPrint('الدور: ${currentUser.value!.role}');

        // التحقق من صلاحيات المستخدم وإنشائها إذا لزم الأمر
        await _checkAndCreateUserPermissions();

        // تحديث خدمة المزامنة بمعرف المستخدم الحالي (بدون انتظار)
        // هذا لا يجب أن يؤثر على نجاح تسجيل الدخول
        Future.delayed(Duration.zero, () async {
          try {
            if (Get.isRegistered<SyncService>()) {
              final syncService = Get.find<SyncService>();
              syncService.updateUserId(currentUser.value!.id);
              debugPrint('تم تحديث معرف المستخدم في خدمة المزامنة');
            } else {
              // إنشاء خدمة المزامنة إذا لم تكن موجودة
              final syncService = SyncService(currentUser.value!.id);
              Get.put(syncService, permanent: true);
              debugPrint('تم إنشاء خدمة المزامنة بمعرف المستخدم الجديد');
            }
          } catch (syncError) {
            debugPrint('خطأ في تحديث خدمة المزامنة: $syncError');
            // لا نرمي الخطأ هنا لأنه لا يجب أن يؤثر على تسجيل الدخول
          }
        });

        update();
        return true;
      } else {
        error.value = 'بيانات الدخول غير صحيحة';
        update();
        return false;
      }
    } catch (e) {
      error.value = e.toString();
      debugPrint('خطأ في تسجيل الدخول: $e');
      update();
      return false;
    } finally {
      isLoading.value = false;
      update();
    }
  }

  /// التحقق من صلاحيات المستخدم وإنشائها إذا لزم الأمر
  Future<void> _checkAndCreateUserPermissions() async {
    if (currentUser.value == null) return;

    final permissionRepository = PermissionRepository();
    final userId = currentUser.value!.id;
    final userRole = currentUser.value!.role;

    // التحقق من وجود صلاحيات للمستخدم
    final userPermissions =
        await permissionRepository.getUserPermissions(userId);
    debugPrint('عدد صلاحيات المستخدم: ${userPermissions.length}');

    // إذا لم تكن هناك صلاحيات، قم بإنشاء الصلاحيات الافتراضية
    if (userPermissions.isEmpty) {
      debugPrint('إنشاء الصلاحيات الافتراضية للأدوار');
      await permissionRepository.createDefaultRolePermissions();

      // إنشاء صلاحيات للمستخدم بناءً على دوره
      debugPrint('إنشاء صلاحيات للمستخدم بناءً على دوره');
      await _createUserPermissionsBasedOnRole(userId, userRole);
    }

    // التحقق من صلاحيات الواجهات للمستخدم
    final interfacePermissions =
        await permissionRepository.getUserInterfacePermissions(userId);
    debugPrint('عدد صلاحيات الواجهات للمستخدم: ${interfacePermissions.length}');

    // إذا لم تكن هناك صلاحيات واجهات، قم بإنشاء الصلاحيات الافتراضية للواجهات
    if (interfacePermissions.isEmpty) {
      debugPrint('إنشاء صلاحيات الواجهات الافتراضية للمستخدم');
      await _createUserInterfacePermissions(userId, userRole);
    }
  }

  /// إنشاء صلاحيات للمستخدم بناءً على دوره
  Future<void> _createUserPermissionsBasedOnRole(
      String userId, UserRole role) async {
    debugPrint('إنشاء صلاحيات للمستخدم: $userId بناءً على الدور: $role');
    final permissionRepository = PermissionRepository();

    // الحصول على صلاحيات الدور
    final rolePermissions = await permissionRepository.getRolePermissions(role);
    debugPrint('عدد صلاحيات الدور: ${rolePermissions.length}');

    // إنشاء صلاحيات للمستخدم بناءً على صلاحيات الدور
    for (final rolePermission in rolePermissions) {
      if (rolePermission.isGranted) {
        // إنشاء صلاحية للمستخدم
        final userPermission = Permission(
          id: const Uuid().v4(),
          userId: userId,
          type: rolePermission.type,
          scope: rolePermission.scope,
          isGranted: true,
          description: rolePermission.description,
          createdAt: DateTime.now(),
        );

        await permissionRepository.createPermission(userPermission);
        debugPrint(
            'تم إنشاء صلاحية للمستخدم: ${userPermission.type} - ${userPermission.scope}');
      }
    }

    debugPrint('تم إنشاء صلاحيات المستخدم بنجاح');
  }

  /// إنشاء صلاحيات الواجهات للمستخدم
  Future<void> _createUserInterfacePermissions(
      String userId, UserRole role) async {
    debugPrint(
        'إنشاء صلاحيات الواجهات للمستخدم: $userId بناءً على الدور: $role');
    final permissionRepository = PermissionRepository();

    // قائمة الواجهات في النظام
    final interfaces = [
      'tasks', // المهام
      'dashboard', // لوحة المعلومات
      'messages', // الرسائل
      'notifications', // الإشعارات
      'departments', // الأقسام
      'users', // المستخدمين
      'reports', // التقارير
      'settings', // الإعدادات
      'admin', // الإدارة
    ];

    // إنشاء صلاحيات الواجهات بناءً على الدور
    for (final interface in interfaces) {
      bool isGranted = false;

      if (role == UserRole.superAdmin) {
        // مدير النظام العام لديه وصول لجميع الواجهات
        isGranted = true;
      } else if (role == UserRole.admin) {
        // مدير الإدارة يمكنه الوصول لجميع الواجهات عدا الإعدادات العامة
        isGranted = interface != 'settings';
      } else if (role == UserRole.departmentManager) {
        // مدير القسم لا يمكنه الوصول إلى الإعدادات والإدارة
        isGranted = interface != 'settings' && interface != 'admin';
      } else if (role == UserRole.employee) {
        // الموظف يمكنه الوصول إلى بعض الواجهات
        isGranted = interface == 'tasks' ||
            interface == 'dashboard' ||
            interface == 'messages';
      }

      // إنشاء صلاحية الواجهة للمستخدم
      final permission = Permission(
        id: const Uuid().v4(),
        userId: userId,
        type: PermissionType.view,
        scope: PermissionScope.interfaces,
        isGranted: isGranted,
        description: interface,
        createdAt: DateTime.now(),
      );

      await permissionRepository.createPermission(permission);
      debugPrint(
          'تم إنشاء صلاحية الواجهة للمستخدم: $interface، مسموح: $isGranted');
    }

    debugPrint('تم إنشاء صلاحيات الواجهات للمستخدم بنجاح');
  }

  // Register new user
  Future<bool> register(User user) async {
    isLoading.value = true;
    error.value = '';

    try {
      final result = await _authService.register(user);
      if (result) {
        currentUser.value = user;
        return true;
      } else {
        error.value = 'Registration failed';
        return false;
      }
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Logout user
  Future<bool> logout() async {
    isLoading.value = true;

    try {
      final result = await _authService.logout();
      if (result) {
        currentUser.value = null;
        return true;
      } else {
        error.value = 'Logout failed';
        return false;
      }
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Clear error
  void clearError() {
    error.value = '';
    update();
  }

  // Set error message
  void setError(String errorMessage) {
    error.value = errorMessage;
    update();
  }

  // Update user profile
  Future<bool> updateUserProfile(User updatedUser) async {
    isLoading.value = true;
    error.value = '';

    try {
      final user = await _authService.updateUserProfile(updatedUser);
      if (user != null) {
        currentUser.value = user;
        return true;
      } else {
        error.value = 'فشل تحديث الملف الشخصي';
        return false;
      }
    } catch (e) {
      error.value = e.toString();
      return false;
    } finally {
      isLoading.value = false;
    }
  }
}
