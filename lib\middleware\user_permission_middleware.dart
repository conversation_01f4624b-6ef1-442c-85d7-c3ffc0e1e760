import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controllers/auth_controller.dart';
import '../models/user_model.dart';

/// وسيط للتحقق من صلاحيات المستخدم
///
/// يستخدم هذا الوسيط للتحقق من صلاحيات المستخدم قبل الوصول إلى الشاشات المختلفة
/// ويمكن استخدامه للتحقق من صلاحيات المستخدم على مستويات مختلفة
class UserPermissionMiddleware extends GetMiddleware {
  final AuthController _authController = Get.find<AuthController>();
  final UnifiedPermissionService _unifiedPermissionService =
      Get.find<UnifiedPermissionService>();

  @override
  RouteSettings? redirect(String? route) {
    // التحقق من تسجيل الدخول
    if (_authController.currentUser.value == null) {
      return const RouteSettings(name: '/login');
    }

    // التحقق من الصلاحيات حسب المسار
    final currentUser = _authController.currentUser.value!;

    // المسارات المتاحة للجميع
    if (route == '/home' || route == '/login' || route == '/register') {
      return null;
    }

    // مدير النظام العام ومدير الإدارة لديهم وصول لمعظم المسارات
    if (currentUser.role == UserRole.superAdmin ||
        currentUser.role == UserRole.admin) {
      return null;
    }

    // المسارات الخاصة بالمدير
    if (route == '/admin') {
      // التحقق من صلاحية الوصول إلى لوحة التحكم الإدارية من خلال الأدوار المخصصة
      // ملاحظة: لا يمكن استخدام دالة غير متزامنة هنا، لذلك نقوم بالتحقق في الشاشة نفسها
      // سنسمح بالوصول مؤقتًا ونترك التحقق للشاشة نفسها
      return null;
    }

    // المسارات الخاصة بمدير القسم
    if (route == '/department-dashboard') {
      // التحقق من صلاحية الوصول إلى لوحة تحكم القسم من خلال الأدوار المخصصة
      // ملاحظة: لا يمكن استخدام دالة غير متزامنة هنا، لذلك نقوم بالتحقق في الشاشة نفسها
      return null;
    }

    // المسارات الخاصة بالمستخدم العادي
    if (route == '/user-dashboard') {
      // لوحة تحكم المستخدم متاحة لجميع المستخدمين
      return null;
    }

    // نسمح بالوصول مؤقتًا ونترك التحقق من الصلاحيات للشاشة نفسها
    // سيتم التحقق من الصلاحيات في الشاشة باستخدام checkInterfaceAccess
    // ملاحظة: لا يمكن استخدام دالة غير متزامنة هنا، لذلك نقوم بالتحقق في الشاشة نفسها
    return null;
  }

  /// التحقق من صلاحية الوصول إلى الواجهة
  ///
  /// يتم استدعاء هذه الدالة من الشاشة نفسها بعد تحميلها
  /// باستخدام initState أو onInit
  Future<bool> checkInterfaceAccess(String? route) async {
    // إذا كان المستخدم مدير النظام، فلديه وصول إلى جميع الواجهات
    if (_authController.isAdmin) {
      return true;
    }

    final currentUser = _authController.currentUser.value;
    if (currentUser == null) {
      Get.offNamed('/login');
      return false;
    }

    // تحويل المسار إلى اسم الواجهة
    String interfaceName = _getInterfaceNameFromRoute(route) ?? 'unknown';

    // التحقق من صلاحية الوصول إلى الواجهة باستخدام خدمة الصلاحيات الموحدة
    final hasAccess =
        await _unifiedPermissionService.checkInterfaceAccess(interfaceName);

    if (!hasAccess) {
      Get.snackbar(
        'خطأ في الصلاحيات',
        'ليس لديك صلاحية للوصول إلى هذه الصفحة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      Get.offNamed('/home');
      return false;
    }

    return true;
  }

  /// تحويل المسار إلى اسم الواجهة
  String? _getInterfaceNameFromRoute(String? route) {
    if (route == null) return null;

    debugPrint('تحويل المسار إلى اسم الواجهة: $route');

    // قائمة المسارات والواجهات المقابلة لها
    final Map<String, String> routeToInterface = {
      '/tasks': 'tasks',
      '/task/': 'tasks',
      '/admin': 'admin',
      '/admin/roles': 'roles',
      '/admin/permission': 'admin',
      '/department': 'departments',
      '/user': 'users',
      '/report': 'reports',
      '/setting': 'settings',
      '/message': 'messages',
      '/chat': 'messages',
      '/notification': 'notifications',
      '/home': 'dashboard',
      '/dashboard': 'dashboard',
      '/gantt': 'gantt_chart',
      '/power-bi': 'power_bi',
      '/calendar': 'calendar',
      '/database': 'database',
      '/roles': 'roles',
    };

    // البحث عن الواجهة المناسبة للمسار
    for (final entry in routeToInterface.entries) {
      if (route.startsWith(entry.key)) {
        debugPrint('تم تحويل المسار: $route إلى الواجهة: ${entry.value}');
        return entry.value;
      }
    }

    // إذا لم يتم العثور على واجهة مناسبة
    debugPrint('لم يتم العثور على واجهة مناسبة للمسار: $route');
    return null;
  }
}
