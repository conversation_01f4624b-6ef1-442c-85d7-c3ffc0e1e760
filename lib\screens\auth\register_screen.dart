import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_strings.dart';
import '../../constants/app_styles.dart';
import '../../models/user_model.dart';
import '../../controllers/auth_controller.dart';
import '../../utils/validators.dart';
import '../home/<USER>';

/// Registration screen for new users
class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  /// Registers a new user with the provided information
  ///
  /// This method validates the form, creates a new User object,
  /// and calls the AuthController's register method.
  /// If registration is successful, navigates to the HomeScreen.
  Future<void> _register() async {
    if (_formKey.currentState!.validate()) {
      // Get the auth controller
      final authController = Get.find<AuthController>();

      // Create user object
      final user = User(
        id: const Uuid().v4(),
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        username: _usernameController.text.trim(),
        password: _passwordController.text,
        role: UserRole.employee, // Default role
        createdAt: DateTime.now(),
      );

      final success = await authController.register(user);

      if (success && mounted) {
        // Navigate to home screen using GetX navigation
        Get.offAll(() => const HomeScreen());
      }
    }
  }

  /// Builds the registration screen UI
  @override
  Widget build(BuildContext context) {
    // Get the auth controller
    final authController = Get.find<AuthController>();

    return Scaffold(
      appBar: AppBar(
        title: Text(AppStrings.register),
      ),
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // App logo
                  const Icon(
                    Icons.task_alt,
                    size: 60,
                    color: AppColors.primary,
                  ),
                  const SizedBox(height: 16),

                  // App name
                  Text(
                    AppStrings.createAccount,
                    style: AppStyles.headingMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),

                  // Name field
                  TextFormField(
                    controller: _nameController,
                    decoration: AppStyles.inputDecoration(
                      labelText: AppStrings.name,
                      prefixIcon: const Icon(Icons.person),
                    ),
                    textInputAction: TextInputAction.next,
                    validator: (value) {
                      if (!Validators.isNotEmpty(value)) {
                        return AppStrings.requiredField;
                      }
                      if (!Validators.hasMinLength(value!, 2)) {
                        return 'Name must be at least 2 characters';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Email field
                  TextFormField(
                    controller: _emailController,
                    decoration: AppStyles.inputDecoration(
                      labelText: AppStrings.email,
                      prefixIcon: const Icon(Icons.email),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    textInputAction: TextInputAction.next,
                    validator: (value) {
                      if (!Validators.isNotEmpty(value)) {
                        return AppStrings.requiredField;
                      }
                      if (!Validators.isValidEmail(value!)) {
                        return AppStrings.invalidEmail;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Username field
                  TextFormField(
                    controller: _usernameController,
                    decoration: AppStyles.inputDecoration(
                      labelText: 'اسم المستخدم',
                      hintText: 'أدخل اسم المستخدم للتسجيل',
                      prefixIcon: const Icon(Icons.account_circle),
                    ),
                    textInputAction: TextInputAction.next,
                    validator: (value) {
                      if (value != null && value.isNotEmpty && !Validators.hasMinLength(value, 3)) {
                        return 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Password field
                  TextFormField(
                    controller: _passwordController,
                    decoration: AppStyles.inputDecoration(
                      labelText: AppStrings.password,
                      prefixIcon: const Icon(Icons.lock),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscurePassword ? Icons.visibility : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscurePassword = !_obscurePassword;
                          });
                        },
                      ),
                    ),
                    obscureText: _obscurePassword,
                    textInputAction: TextInputAction.next,
                    validator: (value) {
                      if (!Validators.isNotEmpty(value)) {
                        return AppStrings.requiredField;
                      }
                      if (!Validators.hasMinLength(value!, 6)) {
                        return AppStrings.invalidPassword;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Confirm password field
                  TextFormField(
                    controller: _confirmPasswordController,
                    decoration: AppStyles.inputDecoration(
                      labelText: AppStrings.confirmPassword,
                      prefixIcon: const Icon(Icons.lock_outline),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscureConfirmPassword = !_obscureConfirmPassword;
                          });
                        },
                      ),
                    ),
                    obscureText: _obscureConfirmPassword,
                    textInputAction: TextInputAction.done,
                    validator: (value) {
                      if (!Validators.isNotEmpty(value)) {
                        return AppStrings.requiredField;
                      }
                      if (value != _passwordController.text) {
                        return AppStrings.passwordsDoNotMatch;
                      }
                      return null;
                    },
                    onFieldSubmitted: (_) => _register(),
                  ),
                  const SizedBox(height: 24),

                  // Error message
                  Obx(() => authController.error.value.isNotEmpty
                    ? Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        authController.error.value,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                    )
                    : const SizedBox.shrink()),

                  // Register button
                  Obx(() => ElevatedButton(
                    onPressed: authController.isLoading.value ? null : _register,
                    style: AppStyles.primaryButtonStyle,
                    child: authController.isLoading.value
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(AppStrings.register),
                  )),
                  const SizedBox(height: 16),

                  // Login link
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(AppStrings.alreadyHaveAccount),
                      TextButton(
                        onPressed: () {
                          // Use GetX navigation
                          Get.back();
                        },
                        child: Text(AppStrings.login),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
