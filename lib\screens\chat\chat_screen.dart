import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/realtime_chat_controller.dart';
import '../../models/message_model.dart';
import '../../models/chat_group_model.dart';
import '../../utils/date_formatter.dart';
import '../../constants/app_colors.dart';

class ChatScreen extends StatefulWidget {
  final ChatGroup group;

  const ChatScreen({super.key, required this.group});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final RealtimeChatController _chatController = Get.find<RealtimeChatController>();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    debugPrint('🔍 تم استدعاء initState في ChatScreen');

    // اختيار المجموعة
    _chatController.selectGroup(widget.group);
    debugPrint('🔍 تم اختيار المجموعة: ${widget.group.name}');

    // التمرير إلى أسفل عند تحميل الرسائل
    _chatController.messages.listen((messages) {
      debugPrint('🔍 تم تغيير قائمة الرسائل، عدد الرسائل: ${messages.length}');
      _scrollToBottom();
    });

    // الاستماع لوصول رسائل جديدة
    ever(_chatController.newMessageReceived, (value) {
      debugPrint('🔍 تم استلام رسالة جديدة، قيمة المتغير: $value');
      // تأخير قصير لضمان إضافة الرسالة إلى القائمة أولاً
      Future.delayed(const Duration(milliseconds: 100), () {
        debugPrint('🔍 تنفيذ التمرير بعد استلام رسالة جديدة');
        _scrollToBottom();
      });
    });

    // إضافة مستمع للتمرير
    _scrollController.addListener(() {
      debugPrint('🔍 تم تغيير موضع التمرير: ${_scrollController.position.pixels} / ${_scrollController.position.maxScrollExtent}');
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    debugPrint('🔍 تم استدعاء _scrollToBottom()');
    if (_scrollController.hasClients) {
      debugPrint('🔍 ScrollController جاهز، سيتم التمرير للأسفل');

      // استخدام SchedulerBinding لضمان تنفيذ التمرير بعد اكتمال بناء الواجهة
      WidgetsBinding.instance.addPostFrameCallback((_) {
        try {
          debugPrint('🔍 تنفيذ التمرير داخل PostFrameCallback');

          // محاولة استخدام jumpTo بدلاً من animateTo للتمرير الفوري
          _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
          debugPrint('🔍 تم التمرير الفوري باستخدام jumpTo');

          // في حالة الحاجة إلى تمرير إضافي (للتأكد)
          Future.delayed(const Duration(milliseconds: 50), () {
            if (_scrollController.hasClients) {
              _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
              debugPrint('🔍 تم التمرير الإضافي بعد 50 مللي ثانية');
            }
          });
        } catch (error) {
          debugPrint('🔍 حدث خطأ أثناء التمرير: $error');

          // محاولة بديلة باستخدام animateTo
          try {
            _scrollController.animateTo(
              _scrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            ).then((_) {
              debugPrint('🔍 اكتمل التمرير البديل بنجاح');
            });
          } catch (secondError) {
            debugPrint('🔍 فشلت المحاولة البديلة أيضًا: $secondError');
          }
        }
      });
    } else {
      debugPrint('🔍 ScrollController غير جاهز، سيتم المحاولة مرة أخرى بعد 50 مللي ثانية');
      // إذا لم يكن ScrollController جاهزًا بعد، حاول مرة أخرى بعد فترة قصيرة
      Future.delayed(const Duration(milliseconds: 50), () {
        _scrollToBottom();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() {
          final group = _chatController.selectedGroup.value;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(group?.name ?? ''),
              _buildTypingIndicator(),
            ],
          );
        }),
        actions: [
          Obx(() => _chatController.isConnected.value
              ? const Icon(Icons.wifi, color: Colors.green)
              : const Icon(Icons.wifi_off, color: Colors.red)),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              // عرض معلومات المجموعة
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // قائمة الرسائل
          Expanded(
            child: Obx(() {
              final messages = _chatController.messages;

              if (messages.isEmpty) {
                return const Center(
                  child: Text('لا توجد رسائل بعد. ابدأ المحادثة!'),
                );
              }

              // استخدام ListView.builder مع key لإعادة بناء القائمة عند تغيير الرسائل
              return ListView.builder(
                key: ValueKey('messages-list-${messages.length}'),
                controller: _scrollController,
                padding: const EdgeInsets.all(8),
                // استخدام addAutomaticKeepAlives لضمان الاحتفاظ بحالة التمرير
                addAutomaticKeepAlives: false,
                // استخدام cacheExtent أكبر لتحسين الأداء
                cacheExtent: 1000,
                itemCount: messages.length,
                itemBuilder: (context, index) {
                  // إذا كان هذا آخر عنصر، قم بالتمرير إلى الأسفل بعد بنائه
                  if (index == messages.length - 1) {
                    debugPrint('🔍 بناء آخر رسالة في القائمة، سيتم التمرير للأسفل');
                    // استخدام WidgetsBinding للتمرير بعد اكتمال البناء
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      _scrollToBottom();
                    });
                  }
                  return _buildMessageItem(messages[index]);
                },
              );
            }),
          ),

          // مدخل الرسالة
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildTypingIndicator() {
    return Obx(() {
      final typingUsers = _chatController.typingUsers;

      if (typingUsers.isEmpty) {
        return const SizedBox.shrink();
      }

      // الحصول على أسماء المستخدمين الذين يكتبون
      final typingUserNames = <String>[];

      for (final userId in typingUsers.keys) {
        if (typingUsers[userId] == true) {
          final user = _chatController.users.firstWhereOrNull((u) => u.id == userId);
          if (user != null) {
            typingUserNames.add(user.name);
          }
        }
      }

      if (typingUserNames.isEmpty) {
        return const SizedBox.shrink();
      }

      return Text(
        '${typingUserNames.join(', ')} يكتب...',
        style: const TextStyle(
          fontSize: 12,
          fontStyle: FontStyle.italic,
        ),
      );
    });
  }

  Widget _buildMessageItem(Message message) {
    final currentUser = _chatController.currentUser.value;
    final isMe = currentUser != null && message.senderId == currentUser.id;

    // الحصول على معلومات المرسل
    final sender = _chatController.users.firstWhereOrNull((u) => u.id == message.senderId);
    final senderName = sender?.name ?? 'مستخدم غير معروف';

    return Align(
      alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isMe ? AppColors.primary.withAlpha(51) : Colors.grey.withAlpha(25),
          borderRadius: BorderRadius.circular(12),
        ),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.7,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // اسم المرسل
            if (!isMe)
              Text(
                senderName,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),

            // محتوى الرسالة
            Text(message.content),

            // الوقت وحالة القراءة
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  DateFormatter.formatMessageTime(message.createdAt),
                  style: const TextStyle(
                    fontSize: 10,
                    color: Colors.grey,
                  ),
                ),
                if (isMe) const SizedBox(width: 4),
                if (isMe)
                  Icon(
                    message.isRead ? Icons.done_all : Icons.done,
                    size: 12,
                    color: message.isRead ? Colors.blue : Colors.grey,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(51),
            blurRadius: 4,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر المرفقات
          IconButton(
            icon: const Icon(Icons.attach_file),
            onPressed: () {
              // إضافة مرفق
            },
          ),

          // حقل إدخال الرسالة
          Expanded(
            child: TextField(
              controller: _messageController,
              textDirection: TextDirection.rtl,
              decoration: const InputDecoration(
                hintText: 'اكتب رسالة...',
                hintTextDirection: TextDirection.rtl,
                border: InputBorder.none,
              ),
              onChanged: (text) {
                // إرسال حالة الكتابة
                _chatController.sendTypingStatus(text.isNotEmpty);
              },
            ),
          ),

          // زر الإرسال
          IconButton(
            icon: const Icon(Icons.send),
            onPressed: _sendMessage,
          ),
        ],
      ),
    );
  }

  void _sendMessage() {
    final text = _messageController.text.trim();

    if (text.isEmpty) {
      return;
    }

    // إرسال الرسالة
    _chatController.sendMessage(text);

    // مسح حقل الإدخال
    _messageController.clear();
  }
}
