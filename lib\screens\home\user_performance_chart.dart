import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/user_controller.dart';
import '../../models/task_model.dart';

/// مكون مخطط أداء المستخدمين في أنواع المهام
class UserPerformanceChart extends StatefulWidget {
  const UserPerformanceChart({super.key});

  @override
  State<UserPerformanceChart> createState() => _UserPerformanceChartState();
}

class _UserPerformanceChartState extends State<UserPerformanceChart> {
  // المتحكمات اللازمة
  final TaskController _taskController = Get.find<TaskController>();
  final UserController _userController = Get.find<UserController>();

  @override
  Widget build(BuildContext context) {
    final tasks = _taskController.tasks;
    final users = _userController.users;

    // التحقق من وجود مهام ومستخدمين
    if (tasks.isEmpty || users.isEmpty) {
      return const Center(
        child: Text('لا توجد بيانات كافية لعرض المخطط'),
      );
    }

    // تحديد أنواع المهام المختلفة (استخدام الأولويات كمثال)
    final taskTypes = TaskPriority.values;
    
    // أسماء الأولويات
    final Map<TaskPriority, String> priorityNames = {
      TaskPriority.low: 'منخفضة',
      TaskPriority.medium: 'متوسطة',
      TaskPriority.high: 'عالية',
      TaskPriority.urgent: 'عاجلة',
    };

    // تجميع أداء المستخدمين حسب نوع المهمة
    final Map<String, Map<TaskPriority, double>> userPerformance = {};

    // تهيئة بيانات الأداء
    for (final user in users) {
      userPerformance[user.id] = {};
      for (final type in taskTypes) {
        userPerformance[user.id]![type] = 0;
      }
    }

    // حساب نسبة إكمال المهام لكل مستخدم حسب نوع المهمة
    for (final task in tasks) {
      if (task.assigneeId == null) continue;

      final userId = task.assigneeId!;
      final priority = task.priority;

      // التحقق من وجود المستخدم في القائمة
      if (!userPerformance.containsKey(userId)) continue;

      // زيادة نسبة الإكمال
      userPerformance[userId]![priority] = (userPerformance[userId]![priority] ?? 0) + task.completionPercentage;
    }

    // حساب متوسط الأداء لكل مستخدم ونوع مهمة
    for (final userId in userPerformance.keys) {
      for (final type in taskTypes) {
        // عدد المهام من هذا النوع المعينة للمستخدم
        final taskCount = tasks.where((task) =>
          task.assigneeId == userId && task.priority == type
        ).length;

        // حساب المتوسط (إذا كان هناك مهام)
        if (taskCount > 0) {
          userPerformance[userId]![type] = (userPerformance[userId]![type] ?? 0) / taskCount;
        }
      }
    }

    // اختيار أفضل 5 مستخدمين لعرضهم في المخطط
    final List<String> topUserIds = userPerformance.keys.toList()
      ..sort((a, b) {
        final aAvg = userPerformance[a]!.values.reduce((sum, value) => sum + value) / taskTypes.length;
        final bAvg = userPerformance[b]!.values.reduce((sum, value) => sum + value) / taskTypes.length;
        return bAvg.compareTo(aAvg); // ترتيب تنازلي
      });

    // اقتصار العدد على 5 مستخدمين كحد أقصى
    final selectedUserIds = topUserIds.take(5).toList();

    // ألوان للمستخدمين
    final List<Color> userColors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
    ];

    // استخدام FutureBuilder للحصول على أسماء المستخدمين
    return FutureBuilder<Map<String, String>>(
      future: _getUserNames(selectedUserIds),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        final userNames = snapshot.data ?? {};
        
        // إنشاء بيانات المخطط الشريطي
        final List<Map<String, dynamic>> chartData = [];
        
        // إضافة بيانات لكل نوع مهمة
        for (final type in taskTypes) {
          final Map<String, dynamic> typeData = {
            'نوع': priorityNames[type]!,
          };
          
          // إضافة أداء كل مستخدم لهذا النوع
          for (int i = 0; i < selectedUserIds.length; i++) {
            final userId = selectedUserIds[i];
            final userName = userNames[userId] ?? 'مستخدم $userId';
            typeData[userName] = userPerformance[userId]![type]!;
          }
          
          chartData.add(typeData);
        }
        
        // إنشاء قائمة بأسماء المستخدمين للمفتاح
        final List<Widget> legendItems = [];
        for (int i = 0; i < selectedUserIds.length; i++) {
          final userId = selectedUserIds[i];
          final userName = userNames[userId] ?? 'مستخدم $userId';
          final color = userColors[i % userColors.length];
          
          legendItems.add(
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(userName),
                ],
              ),
            ),
          );
        }
        
        return Column(
          children: [
            // المفتاح
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Wrap(
                alignment: WrapAlignment.center,
                spacing: 16,
                runSpacing: 8,
                children: legendItems,
              ),
            ),
            
            const SizedBox(height: 8),
            
            // المخطط الشريطي المجمع
            Expanded(
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: 100, // نسبة الإكمال من 0 إلى 100
                  barGroups: List.generate(chartData.length, (index) {
                    final data = chartData[index];
                    final List<BarChartRodData> rods = [];
                    
                    // إنشاء شريط لكل مستخدم
                    for (int i = 0; i < selectedUserIds.length; i++) {
                      final userId = selectedUserIds[i];
                      final userName = userNames[userId] ?? 'مستخدم $userId';
                      final color = userColors[i % userColors.length];
                      
                      rods.add(
                        BarChartRodData(
                          toY: data[userName] ?? 0,
                          color: color,
                          width: 16,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(4),
                            topRight: Radius.circular(4),
                          ),
                        ),
                      );
                    }
                    
                    return BarChartGroupData(
                      x: index,
                      barRods: rods,
                      barsSpace: 4,
                    );
                  }),
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          if (value < 0 || value >= chartData.length) {
                            return const SizedBox.shrink();
                          }
                          
                          final data = chartData[value.toInt()];
                          final typeName = data['نوع'] as String;
                          
                          return SideTitleWidget(
                            meta: meta,
                            child: Text(
                              typeName,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          );
                        },
                        reservedSize: 30,
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            value.toInt().toString(),
                            style: TextStyle(
                              color: Get.isDarkMode ? Colors.white : Colors.black,
                              fontSize: 12,
                            ),
                          );
                        },
                      ),
                      axisNameWidget: const Padding(
                        padding: EdgeInsets.only(bottom: 8.0),
                        child: Text(
                          'نسبة الإكمال (%)',
                          style: TextStyle(fontSize: 12),
                        ),
                      ),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: false,
                    horizontalInterval: 20,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: Get.isDarkMode ? Colors.white10 : Colors.black12,
                        strokeWidth: 1,
                      );
                    },
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border(
                      bottom: BorderSide(
                        color: Get.isDarkMode ? Colors.white70 : Colors.black54,
                        width: 1,
                      ),
                      left: BorderSide(
                        color: Get.isDarkMode ? Colors.white70 : Colors.black54,
                        width: 1,
                      ),
                    ),
                  ),
                  barTouchData: BarTouchData(
                    enabled: true,
                    touchTooltipData: BarTouchTooltipData(
                      getTooltipColor: (_) => Get.isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                      getTooltipItem: (group, groupIndex, rod, rodIndex) {
                        final data = chartData[group.x.toInt()];
                        final typeName = data['نوع'] as String;
                        final userId = selectedUserIds[rodIndex];
                        final userName = userNames[userId] ?? 'مستخدم $userId';
                        final performance = rod.toY.toStringAsFixed(1);
                        
                        return BarTooltipItem(
                          '$userName - $typeName\n',
                          TextStyle(
                            color: Get.isDarkMode ? Colors.white : Colors.black,
                            fontWeight: FontWeight.bold,
                          ),
                          children: [
                            TextSpan(
                              text: '$performance%',
                              style: TextStyle(
                                color: Get.isDarkMode ? Colors.white70 : Colors.black87,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// الحصول على أسماء المستخدمين من معرفاتهم
  Future<Map<String, String>> _getUserNames(List<String> userIds) async {
    final Map<String, String> userNames = {};

    for (final userId in userIds) {
      final name = await _userController.getUserNameById(userId);
      userNames[userId] = name;
    }

    return userNames;
  }
}
