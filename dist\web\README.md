# SQLite Web Setup

This directory contains placeholder files for SQLite Web support. To make the app work properly in a web environment, you need to replace these placeholder files with the actual binary files.

## Required Files

1. `sqlite3.wasm` - The WebAssembly binary for SQLite
2. `sqflite_sw.js` - The service worker script for SQLite

## How to Get the Actual Files

You can get these files from the `sqflite_common_ffi_web` package:

1. Navigate to your Flutter project's `.dart_tool/pub/cache/hosted/pub.dev/sqflite_common_ffi_web-x.x.x/lib/src/web` directory (replace x.x.x with the actual version)
2. Copy `sqlite3.wasm` and `sqflite_sw.js` from that directory to this `web` directory

Alternatively, you can download them from the GitHub repository:
https://github.com/tekartik/sqflite/tree/master/packages_web/sqflite_common_ffi_web/lib/src/web

## Important Note

Without these files, the SQLite database will not work properly in a web environment, and the app will fall back to using an in-memory database that loses data when the page is refreshed.
