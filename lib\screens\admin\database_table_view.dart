import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/database_management_controller.dart';
import '../../models/database_table_model.dart';
import '../../utils/responsive_helper.dart';
import 'database_row_editor.dart';

/// واجهة عرض جدول البيانات
///
/// توفر واجهة لعرض وتحرير بيانات جدول
class DatabaseTableView extends StatefulWidget {
  final DatabaseManagementController controller;
  final DatabaseTable table;

  const DatabaseTableView({
    super.key,
    required this.controller,
    required this.table,
  });

  @override
  State<DatabaseTableView> createState() => _DatabaseTableViewState();
}

class _DatabaseTableViewState extends State<DatabaseTableView> {
  final TextEditingController _searchController = TextEditingController();
  String _searchColumn = '';

  @override
  void initState() {
    super.initState();

    // تعيين عمود البحث الافتراضي
    if (widget.table.columns.isNotEmpty) {
      // التأكد من عدم وجود قيم مكررة في الأعمدة القابلة للبحث
      final searchableColumns = widget.table.columns
          .where((col) => col.isSearchable)
          .toList();

      // استخدام أول عمود قابل للبحث أو أول عمود في الجدول
      if (searchableColumns.isNotEmpty) {
        // استخدام أول عمود قابل للبحث
        _searchColumn = searchableColumns.first.id;
      } else {
        // استخدام أول عمود في الجدول
        _searchColumn = widget.table.columns.first.id;
      }

      // التحقق من وجود قيمة صالحة
      debugPrint('تم تعيين عمود البحث الافتراضي: $_searchColumn');
    }

    // تحميل بيانات الجدول
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.controller.loadTableData();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isLargeScreen = ResponsiveHelper.isTablet(context) || ResponsiveHelper.isDesktop(context);

    return Column(
      children: [
        // شريط الأدوات
        _buildToolbar(isLargeScreen),

        // عرض البيانات
        Expanded(
          child: Obx(() {
            if (widget.controller.isLoading.value) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            if (widget.controller.error.value.isNotEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: AppColors.error,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'حدث خطأ',
                      style: AppStyles.titleMedium.copyWith(
                        color: AppColors.error,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.controller.error.value,
                      textAlign: TextAlign.center,
                      style: AppStyles.bodyMedium.copyWith(
                        color: AppColors.error,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () {
                        widget.controller.loadTableData();
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              );
            }

            if (widget.controller.tableData.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.table_rows_outlined,
                      color: Colors.grey,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'لا توجد بيانات',
                      style: AppStyles.titleMedium.copyWith(
                        color: Colors.grey,
                      ),
                    ),
                    if (widget.table.isCreatable) ...[
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: _showAddRowDialog,
                        icon: const Icon(Icons.add),
                        label: const Text('إضافة سجل جديد'),
                      ),
                    ],
                  ],
                ),
              );
            }

            return Column(
              children: [
                // جدول البيانات
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: SingleChildScrollView(
                      child: _buildDataTable(),
                    ),
                  ),
                ),

                // شريط التنقل بين الصفحات
                _buildPagination(),
              ],
            );
          }),
        ),
      ],
    );
  }

  Widget _buildToolbar(bool isLargeScreen) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // عنوان الجدول
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.table.displayName,
                      style: AppStyles.headingMedium,
                    ),
                    Text(
                      widget.table.description,
                      style: AppStyles.bodyMedium.copyWith(
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),

              // أزرار الإجراءات
              if (widget.table.isCreatable)
                ElevatedButton.icon(
                  onPressed: _showAddRowDialog,
                  icon: const Icon(Icons.add),
                  label: Text(isLargeScreen ? 'إضافة سجل جديد' : 'إضافة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                ),
              const SizedBox(width: 8),
              if (widget.table.isExportable)
                OutlinedButton.icon(
                  onPressed: _exportToCsv,
                  icon: const Icon(Icons.download),
                  label: Text(isLargeScreen ? 'تصدير البيانات' : 'تصدير'),
                ),
              const SizedBox(width: 8),
              if (widget.table.isImportable)
                OutlinedButton.icon(
                  onPressed: _importFromCsv,
                  icon: const Icon(Icons.upload),
                  label: Text(isLargeScreen ? 'استيراد البيانات' : 'استيراد'),
                ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              // حقل البحث
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'بحث...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              widget.controller.clearSearch();
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  onSubmitted: (value) {
                    if (value.isNotEmpty) {
                      widget.controller.applySearch(_searchColumn, value);
                    } else {
                      widget.controller.clearSearch();
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),

              // قائمة اختيار عمود البحث
              DropdownButton<String>(
                value: _searchColumn,
                items: widget.table.columns
                    .where((col) => col.isSearchable)
                    .map((col) {
                  // استخدام معرف العمود كقيمة
                  return DropdownMenuItem<String>(
                    value: col.id,
                    child: Text('${col.displayName} (${col.id})'),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _searchColumn = value;
                    });

                    if (_searchController.text.isNotEmpty) {
                      widget.controller.applySearch(value, _searchController.text);
                    }
                  }
                },
              ),
              const SizedBox(width: 8),

              // زر البحث
              ElevatedButton(
                onPressed: () {
                  if (_searchController.text.isNotEmpty) {
                    widget.controller.applySearch(_searchColumn, _searchController.text);
                  } else {
                    widget.controller.clearSearch();
                  }
                },
                child: const Text('بحث'),
              ),
              const SizedBox(width: 8),

              // زر إزالة التصفية
              if (widget.controller.filterClause.value.isNotEmpty ||
                  widget.controller.searchText.value.isNotEmpty)
                OutlinedButton.icon(
                  onPressed: () {
                    _searchController.clear();
                    widget.controller.clearFilter();
                    widget.controller.clearSearch();
                  },
                  icon: const Icon(Icons.filter_alt_off),
                  label: const Text('إزالة التصفية'),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDataTable() {
    // تحديد الأعمدة المرئية
    final visibleColumns = widget.table.columns
        .where((col) => col.isVisibleInList)
        .toList();

    return Obx(() {
      return DataTable(
        columns: [
          // عمود الإجراءات
          const DataColumn(
            label: Text('الإجراءات'),
          ),

          // أعمدة البيانات
          ...visibleColumns.map((col) {
            return DataColumn(
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(col.displayName),
                  if (col.isSortable) ...[
                    const SizedBox(width: 4),
                    InkWell(
                      onTap: () {
                        final isAscending = widget.controller.orderBy.value != col.id ||
                            widget.controller.orderDirection.value == 'DESC';
                        widget.controller.changeOrder(col.id, isAscending);
                      },
                      child: Icon(
                        widget.controller.orderBy.value == col.id
                            ? (widget.controller.orderDirection.value == 'ASC'
                                ? Icons.arrow_upward
                                : Icons.arrow_downward)
                            : Icons.unfold_more,
                        size: 16,
                      ),
                    ),
                  ],
                ],
              ),
            );
          }),
        ],
        rows: widget.controller.tableData.map((row) {
          return DataRow(
            cells: [
              // خلية الإجراءات
              DataCell(
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (widget.table.isEditable)
                      IconButton(
                        icon: const Icon(Icons.edit, size: 20),
                        tooltip: 'تعديل',
                        onPressed: () => _showEditRowDialog(row),
                      ),
                    if (widget.table.isDeletable)
                      IconButton(
                        icon: const Icon(Icons.delete, size: 20),
                        tooltip: 'حذف',
                        color: AppColors.error,
                        onPressed: () => _showDeleteConfirmation(row),
                      ),
                  ],
                ),
              ),

              // خلايا البيانات
              ...visibleColumns.map((col) {
                return DataCell(
                  _buildCellContent(col, row[col.id]),
                  onTap: widget.table.isEditable
                      ? () => _showEditRowDialog(row)
                      : null,
                );
              }),
            ],
          );
        }).toList(),
      );
    });
  }

  Widget _buildCellContent(DatabaseColumn column, dynamic value) {
    if (value == null) {
      return const Text('-');
    }

    switch (column.type) {
      case DatabaseColumnType.boolean:
        return Icon(
          value == 1 || value == true ? Icons.check_circle : Icons.cancel,
          color: value == 1 || value == true ? Colors.green : Colors.red,
          size: 20,
        );

      case DatabaseColumnType.date:
      case DatabaseColumnType.datetime:
        if (value is int) {
          final date = DateTime.fromMillisecondsSinceEpoch(value);
          return Text(
            column.type == DatabaseColumnType.date
                ? '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}'
                : '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}',
          );
        }
        return Text(value.toString());

      case DatabaseColumnType.foreignKey:
        // عرض قيمة المفتاح الخارجي
        return FutureBuilder<String>(
          future: widget.controller.getForeignKeyDisplayValue(
            column.foreignKeyTable!,
            column.foreignKeyColumn!,
            column.foreignKeyDisplayColumn!,
            value,
          ),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              );
            }

            if (snapshot.hasError) {
              return Text(
                value.toString(),
                style: const TextStyle(color: Colors.red),
              );
            }

            return Text(
              snapshot.data ?? value.toString(),
              overflow: TextOverflow.ellipsis,
            );
          },
        );

      case DatabaseColumnType.color:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: _parseColor(value.toString()),
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text(value.toString()),
          ],
        );

      case DatabaseColumnType.image:
        return value.toString().isNotEmpty
            ? const Icon(Icons.image, color: Colors.blue)
            : const Text('-');

      case DatabaseColumnType.file:
        return value.toString().isNotEmpty
            ? const Icon(Icons.insert_drive_file, color: Colors.blue)
            : const Text('-');

      case DatabaseColumnType.password:
        return const Text('••••••••');

      case DatabaseColumnType.enumType: // تم إضافة حالة enumType
        if (column.allowedValues != null && column.allowedValueNames != null) {
          try {
            final index = column.allowedValues!.indexOf(value);
            if (index >= 0 && index < column.allowedValueNames!.length) {
              return Text(
                column.allowedValueNames![index],
                overflow: TextOverflow.ellipsis,
              );
            }
          } catch (e) {
            debugPrint('خطأ في عرض قيمة القائمة المحددة: $e');
          }
        }
        return Text(
          value.toString(),
          overflow: TextOverflow.ellipsis,
        );

      default:
        return Text(
          value.toString(),
          overflow: TextOverflow.ellipsis,
        );
    }
  }

  Widget _buildPagination() {
    return Obx(() {
      final totalPages = (widget.controller.totalRowCount.value / widget.controller.pageSize.value).ceil();

      if (totalPages <= 1) {
        return const SizedBox.shrink();
      }

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // معلومات الصفحة
            Text(
              'الصفحة ${widget.controller.currentPage.value} من $totalPages (${widget.controller.totalRowCount.value} سجل)',
              style: AppStyles.bodyMedium,
            ),

            // أزرار التنقل
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.first_page),
                  tooltip: 'الصفحة الأولى',
                  onPressed: widget.controller.currentPage.value > 1
                      ? () => widget.controller.changePage(1)
                      : null,
                ),
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  tooltip: 'الصفحة السابقة',
                  onPressed: widget.controller.currentPage.value > 1
                      ? () => widget.controller.changePage(widget.controller.currentPage.value - 1)
                      : null,
                ),

                // أرقام الصفحات
                ...List.generate(
                  totalPages > 5 ? 5 : totalPages,
                  (index) {
                    int pageNumber;

                    if (totalPages <= 5) {
                      pageNumber = index + 1;
                    } else {
                      if (widget.controller.currentPage.value <= 3) {
                        pageNumber = index + 1;
                      } else if (widget.controller.currentPage.value >= totalPages - 2) {
                        pageNumber = totalPages - 4 + index;
                      } else {
                        pageNumber = widget.controller.currentPage.value - 2 + index;
                      }
                    }

                    return Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      child: ElevatedButton(
                        onPressed: pageNumber != widget.controller.currentPage.value
                            ? () => widget.controller.changePage(pageNumber)
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: pageNumber == widget.controller.currentPage.value
                              ? AppColors.primary
                              : Colors.grey.shade200,
                          foregroundColor: pageNumber == widget.controller.currentPage.value
                              ? Colors.white
                              : Colors.black,
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          minimumSize: const Size(40, 40),
                        ),
                        child: Text('$pageNumber'),
                      ),
                    );
                  },
                ),

                IconButton(
                  icon: const Icon(Icons.chevron_right),
                  tooltip: 'الصفحة التالية',
                  onPressed: widget.controller.currentPage.value < totalPages
                      ? () => widget.controller.changePage(widget.controller.currentPage.value + 1)
                      : null,
                ),
                IconButton(
                  icon: const Icon(Icons.last_page),
                  tooltip: 'الصفحة الأخيرة',
                  onPressed: widget.controller.currentPage.value < totalPages
                      ? () => widget.controller.changePage(totalPages)
                      : null,
                ),
              ],
            ),

            // اختيار حجم الصفحة
            Row(
              children: [
                const Text('عدد السجلات:'),
                const SizedBox(width: 8),
                DropdownButton<int>(
                  value: widget.controller.pageSize.value,
                  items: [10, 20, 50, 100].map((size) {
                    return DropdownMenuItem<int>(
                      value: size,
                      child: Text('$size'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      widget.controller.changePageSize(value);
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  void _showAddRowDialog() {
    // تنفيذ إضافة سجل جديد
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('إضافة سجل جديد'),
          content: SizedBox(
            width: 500,
            child: DatabaseRowEditor(
              table: widget.table,
              onSave: (row) {
                Navigator.of(context).pop();
                widget.controller.createRow(row);
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  void _showEditRowDialog(Map<String, dynamic> row) {
    // تنفيذ تعديل سجل
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تعديل سجل'),
          content: SizedBox(
            width: 500,
            child: DatabaseRowEditor(
              table: widget.table,
              initialValues: row,
              onSave: (updatedRow) {
                Navigator.of(context).pop();

                // البحث عن العمود المفتاح الأساسي
                final primaryKeyColumn = widget.table.columns.firstWhere(
                  (col) => col.isPrimaryKey,
                  orElse: () => widget.table.columns.first,
                );

                widget.controller.updateRow(updatedRow, primaryKeyColumn.id);
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmation(Map<String, dynamic> row) {
    // البحث عن العمود المفتاح الأساسي
    final primaryKeyColumn = widget.table.columns.firstWhere(
      (col) => col.isPrimaryKey,
      orElse: () => widget.table.columns.first,
    );

    final primaryKeyValue = row[primaryKeyColumn.id];

    // البحث عن عمود العرض
    final displayColumn = widget.table.columns.firstWhere(
      (col) => col.isVisibleInList && !col.isPrimaryKey,
      orElse: () => primaryKeyColumn,
    );

    final displayValue = row[displayColumn.id];

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من حذف السجل "${displayValue ?? primaryKeyValue}"؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                widget.controller.deleteRow(primaryKeyColumn.id, primaryKeyValue);
              },
              style: TextButton.styleFrom(
                foregroundColor: AppColors.error,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  void _exportToCsv() async {
    try {
      // عرض مؤشر التحميل
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // تنفيذ تصدير البيانات إلى CSV
      final csvData = await widget.controller.exportToCsv();

      // إغلاق مؤشر التحميل
      Get.back();

      if (csvData != null) {
        Get.snackbar(
          'تم التصدير بنجاح',
          'تم تصدير البيانات بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'خطأ في التصدير',
          'حدث خطأ أثناء تصدير البيانات: ${widget.controller.error.value}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'خطأ في التصدير',
        'حدث خطأ أثناء تصدير البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _importFromCsv() async {
    try {
      // عرض مربع حوار لتأكيد الاستيراد
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('تأكيد الاستيراد'),
          content: const Text(
            'سيؤدي استيراد البيانات إلى استبدال البيانات الحالية. هل أنت متأكد من المتابعة؟',
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('متابعة'),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      // عرض مؤشر التحميل
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // تنفيذ استيراد البيانات من CSV
      // هنا يجب تنفيذ عملية اختيار الملف واستيراد البيانات
      // هذا مجرد نموذج بسيط
      await Future.delayed(const Duration(seconds: 2));

      // إغلاق مؤشر التحميل
      Get.back();

      Get.snackbar(
        'استيراد البيانات',
        'لم يتم تنفيذ استيراد البيانات بعد. هذه الميزة قيد التطوير.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
    } catch (e) {
      // إغلاق مؤشر التحميل إذا كان مفتوحًا
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      Get.snackbar(
        'خطأ في الاستيراد',
        'حدث خطأ أثناء استيراد البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Color _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        String hexColor = colorString.replaceAll('#', '');
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }
        return Color(int.parse('0x$hexColor'));
      }
      return Colors.blue;
    } catch (e) {
      return Colors.grey;
    }
  }
}
