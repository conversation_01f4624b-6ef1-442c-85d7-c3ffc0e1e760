import 'dart:async';
import 'package:get/get.dart';
import '../models/message_model.dart';
import '../models/chat_group_model.dart';
import '../models/user_model.dart';
import '../models/group_member_model.dart';
import '../models/websocket_event.dart';
import '../services/unified_realtime_service.dart';
import '../services/message_service.dart';
// تم إزالة المستودعات - سيتم استخدام API
// import '../database/user_repository.dart';
// import '../database/chat_group_repository.dart';
// import '../database/message_repository.dart';
import '../utils/logger.dart';

/// وحدة تحكم موحدة للمحادثات
/// تجمع بين وظائف MessageController و RealtimeChatController
class UnifiedChatController extends GetxController {
  // الخدمات - تم إزالة المستودعات
  final UnifiedRealtimeService _realtimeService = UnifiedRealtimeService();
  final MessageService _messageService = MessageService();
  // تم إزالة المستودعات - سيتم استخدام API
  // final UserRepository _userRepository = UserRepository();
  // final ChatGroupRepository _chatGroupRepository = ChatGroupRepository();
  // final MessageRepository _messageRepository = MessageRepository();

  // المستخدم الحالي
  final Rx<User?> currentUser = Rx<User?>(null);

  // المجموعة المحددة
  final Rx<ChatGroup?> selectedGroup = Rx<ChatGroup?>(null);

  // قائمة الرسائل
  final RxList<Message> messages = <Message>[].obs;

  // قائمة المستخدمين
  final RxList<User> users = <User>[].obs;

  // قائمة المجموعات
  final RxList<ChatGroup> groups = <ChatGroup>[].obs;

  // قائمة أعضاء المجموعة المحددة
  final RxList<GroupMember> groupMembers = <GroupMember>[].obs;

  // حالة الكتابة
  final RxMap<String, bool> typingUsers = <String, bool>{}.obs;

  // حالة الاتصال
  final RxBool isConnected = false.obs;

  // حالة تحميل الرسائل
  final RxBool isLoadingMessages = false.obs;

  // حالة تحميل المجموعات
  final RxBool isLoadingGroups = false.obs;

  // متغير للإشارة إلى وصول رسالة جديدة
  final RxBool newMessageReceived = false.obs;

  // مؤقت حالة الكتابة
  Timer? _typingTimer;
  bool _isTyping = false;

  // متغيرات لتحميل المزيد من الرسائل
  int _currentOffset = 0;
  static const int _pageSize = 20;
  final RxBool canLoadMore = true.obs;

  // قائمة المشتركين في تحديثات الرسائل
  final List<Function()> _messagesUpdateSubscribers = [];

  @override
  void onInit() {
    super.onInit();

    // تهيئة خدمة الوقت الفعلي
    _realtimeService.initialize();

    // الاستماع لأحداث الوقت الحقيقي
    _realtimeService.eventStream.listen(_handleRealtimeEvent);

    // مراقبة حالة الاتصال
    ever(_realtimeService.isConnected, (connected) {
      isConnected.value = connected;
      AppLogger.info('حالة الاتصال: ${connected ? 'متصل' : 'غير متصل'}');
    });
  }

  /// تعيين المستخدم الحالي
  Future<void> setCurrentUser(User user) async {
    currentUser.value = user;
    AppLogger.info('تم تعيين المستخدم الحالي: ${user.name} (${user.id})');

    // الاتصال بخادم WebSocket
    await _realtimeService.connect(user.id);

    // تحميل البيانات الأولية
    await loadInitialData();
  }

  /// تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    if (currentUser.value == null) return;

    try {
      // تحميل المستخدمين
      await loadUsers();

      // تحميل المجموعات
      await loadGroups();

      AppLogger.info('تم تحميل البيانات الأولية بنجاح');
    } catch (e) {
      AppLogger.error('خطأ في تحميل البيانات الأولية', e);
    }
  }

  /// تحميل المستخدمين
  Future<void> loadUsers() async {
    try {
      AppLogger.info('جاري تحميل المستخدمين...');
      // TODO: تنفيذ تحميل المستخدمين عبر API
      // final usersList = await apiService.getAllUsers();
      // users.assignAll(usersList);
      users.clear();
      AppLogger.info('تم تحميل ${users.length} مستخدم');
    } catch (e) {
      AppLogger.error('خطأ في تحميل المستخدمين', e);
    }
  }

  /// تحميل المجموعات
  Future<void> loadGroups() async {
    if (currentUser.value == null) return;

    try {
      isLoadingGroups.value = true;
      AppLogger.info('جاري تحميل مجموعات المحادثة...');
      // TODO: تنفيذ تحميل مجموعات المحادثة عبر API
      // final groupsList = await apiService.getChatGroupsForUser(currentUser.value!.id);
      // groups.assignAll(groupsList);
      groups.clear();
      AppLogger.info('تم تحميل ${groups.length} مجموعة محادثة');
    } catch (e) {
      AppLogger.error('خطأ في تحميل مجموعات المحادثة', e);
    } finally {
      isLoadingGroups.value = false;
    }
  }

  /// اختيار مجموعة
  Future<void> selectGroup(ChatGroup group) async {
    AppLogger.info('تم اختيار المجموعة: ${group.name} (${group.id})');
    selectedGroup.value = group;

    // إعادة تعيين متغيرات تحميل الرسائل
    _currentOffset = 0;
    canLoadMore.value = true;
    messages.clear();

    // تحميل أعضاء المجموعة
    await loadGroupMembers(group.id);

    // تحميل رسائل المجموعة
    await loadMessages();

    // وضع علامة على الرسائل كمقروءة
    if (messages.isNotEmpty && currentUser.value != null) {
      final lastMessage = messages.last;
      await _messageService.markMessagesAsRead(
        group.id,
        currentUser.value!.id,
        lastMessage.id,
      );

      // إرسال إشعار بقراءة الرسالة
      _realtimeService.sendReadReceipt(lastMessage.id, group.id);
    }
  }

  /// تحميل أعضاء المجموعة
  Future<void> loadGroupMembers(String groupId) async {
    try {
      AppLogger.info('جاري تحميل أعضاء المجموعة...');
      // TODO: تنفيذ تحميل أعضاء المجموعة عبر API
      // final members = await apiService.getGroupMembers(groupId);
      // groupMembers.assignAll(members);
      groupMembers.clear();
      AppLogger.info('تم تحميل ${groupMembers.length} عضو في المجموعة');
    } catch (e) {
      AppLogger.error('خطأ في تحميل أعضاء المجموعة', e);
    }
  }

  /// تحميل رسائل المجموعة المحددة
  Future<void> loadMessages() async {
    if (selectedGroup.value == null || currentUser.value == null) return;

    try {
      isLoadingMessages.value = true;
      AppLogger.info('جاري تحميل الرسائل للمجموعة: ${selectedGroup.value!.id}');

      final messagesList = await _messageService.getMessagesForGroup(
        selectedGroup.value!.id,
        limit: _pageSize,
        offset: _currentOffset,
      );

      if (messagesList.isEmpty) {
        canLoadMore.value = false;
      } else {
        _currentOffset += messagesList.length;

        // إضافة الرسائل الجديدة إلى القائمة
        if (_currentOffset == messagesList.length) {
          // إذا كانت هذه هي الصفحة الأولى، استبدل القائمة بالكامل
          messages.assignAll(messagesList);
        } else {
          // وإلا، أضف الرسائل الجديدة إلى بداية القائمة
          messages.insertAll(0, messagesList);
        }

        AppLogger.info(
            'تم تحميل ${messagesList.length} رسالة، الإجمالي: ${messages.length}');
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل الرسائل', e);
    } finally {
      isLoadingMessages.value = false;
    }
  }

  /// تحميل المزيد من الرسائل
  Future<void> loadMoreMessages() async {
    if (selectedGroup.value == null ||
        isLoadingMessages.value ||
        !canLoadMore.value) {
      return;
    }

    await loadMessages();
  }

  /// إرسال رسالة
  Future<Message?> sendMessage(
    String groupId,
    String senderId,
    String content, {
    List<String> mentionedUserIds = const [],
    String? replyToMessageId,
  }) async {
    try {
      AppLogger.info('جاري إرسال رسالة إلى المجموعة: $groupId');

      // إنشاء الرسالة
      final message = await _messageService.sendMessage(
        groupId,
        senderId,
        content,
        mentionedUserIds: mentionedUserIds,
        replyToMessageId: replyToMessageId,
      );

      AppLogger.info('تم إنشاء الرسالة بنجاح: ${message.id}');

      // إضافة الرسالة إلى القائمة إذا كانت تنتمي إلى المجموعة المحددة
      if (selectedGroup.value != null && selectedGroup.value!.id == groupId) {
        messages.add(message);

        // إطلاق حدث الرسالة الجديدة
        newMessageReceived.toggle();

        // إخطار المشتركين بتحديث الرسائل
        _notifyMessagesUpdateSubscribers();
      }

      // إرسال الرسالة عبر WebSocket
      _realtimeService.sendMessage(message);

      // إيقاف حالة الكتابة
      _stopTyping();

      return message;
    } catch (e) {
      AppLogger.error('خطأ في إرسال الرسالة', e);
      return null;
    }
  }

  /// إرسال حالة الكتابة
  void sendTypingStatus(bool isTyping) {
    if (currentUser.value == null || selectedGroup.value == null) return;

    // تجنب إرسال نفس الحالة مرة أخرى
    if (_isTyping == isTyping) return;

    _isTyping = isTyping;

    // إرسال حالة الكتابة
    _realtimeService.sendTypingStatus(selectedGroup.value!.id, isTyping);

    // إذا كان المستخدم يكتب، ابدأ مؤقت لإيقاف حالة الكتابة بعد فترة
    if (isTyping) {
      _startTypingTimer();
    } else {
      _stopTypingTimer();
    }
  }

  /// بدء مؤقت حالة الكتابة
  void _startTypingTimer() {
    _stopTypingTimer();

    // إيقاف حالة الكتابة بعد 5 ثوانٍ من عدم الكتابة
    _typingTimer = Timer(const Duration(seconds: 5), _stopTyping);
  }

  /// إيقاف مؤقت حالة الكتابة
  void _stopTypingTimer() {
    _typingTimer?.cancel();
    _typingTimer = null;
  }

  /// إيقاف حالة الكتابة
  void _stopTyping() {
    if (_isTyping) {
      _isTyping = false;

      if (currentUser.value != null && selectedGroup.value != null) {
        _realtimeService.sendTypingStatus(selectedGroup.value!.id, false);
      }
    }
  }

  /// معالجة أحداث الوقت الحقيقي
  void _handleRealtimeEvent(WebSocketEvent event) {
    switch (event.type) {
      case WebSocketEventType.message:
        _handleNewMessage(event.data);
        break;
      case WebSocketEventType.typing:
        _handleTypingStatus(event.data);
        break;
      case WebSocketEventType.readReceipt:
        _handleReadReceipt(event.data);
        break;
      case WebSocketEventType.userStatus:
        _handleUserStatus(event.data);
        break;
      case WebSocketEventType.groupUpdate:
        _handleGroupUpdate(event.data);
        break;
      case WebSocketEventType.notification:
        // يتم معالجتها في وحدة تحكم الإشعارات
        break;
      case WebSocketEventType.error:
        AppLogger.error('تم استلام حدث خطأ من WebSocket', event.data);
        break;
      default:
        break;
    }
  }

  /// معالجة رسالة جديدة
  void _handleNewMessage(Map<String, dynamic> data) {
    try {
      AppLogger.debug('معالجة رسالة جديدة: $data');
      final message = Message.fromJson(data);

      // إذا كانت الرسالة تنتمي إلى المجموعة المحددة، أضفها إلى القائمة
      if (selectedGroup.value != null &&
          message.groupId == selectedGroup.value!.id) {
        // التحقق من عدم وجود الرسالة بالفعل في القائمة
        if (!messages.any((m) => m.id == message.id)) {
          // إضافة الرسالة إلى القائمة
          messages.add(message);

          // تحديث القائمة لضمان تحديث واجهة المستخدم
          messages.refresh();

          // إطلاق حدث الرسالة الجديدة
          newMessageReceived.toggle();

          // إخطار المشتركين بتحديث الرسائل
          _notifyMessagesUpdateSubscribers();

          // إرسال إشعار بقراءة الرسالة إذا كانت من مستخدم آخر
          if (currentUser.value != null &&
              message.senderId != currentUser.value!.id) {
            _realtimeService.sendReadReceipt(message.id, message.groupId);
          }

          AppLogger.info(
              'تمت إضافة رسالة جديدة إلى المجموعة: ${selectedGroup.value!.name}');
        }
      }

      // تحديث آخر رسالة في المجموعة
      final groupIndex = groups.indexWhere((g) => g.id == message.groupId);
      if (groupIndex != -1) {
        final group = groups[groupIndex];
        groups[groupIndex] = group.copyWith(
          lastMessageId: message.id,
          updatedAt: DateTime.now(),
        );

        // تحديث قائمة المجموعات لضمان تحديث واجهة المستخدم
        groups.refresh();
      }
    } catch (e) {
      AppLogger.error('خطأ في معالجة رسالة جديدة', e);
    }
  }

  /// معالجة حالة الكتابة
  void _handleTypingStatus(Map<String, dynamic> data) {
    try {
      final senderId = data['senderId'];
      final groupId = data['groupId'];
      final isTyping = data['isTyping'];

      // إذا كانت حالة الكتابة تنتمي إلى المجموعة المحددة، حدث حالة الكتابة
      if (selectedGroup.value != null && groupId == selectedGroup.value!.id) {
        if (isTyping) {
          typingUsers[senderId] = true;
        } else {
          typingUsers.remove(senderId);
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في معالجة حالة الكتابة', e);
    }
  }

  /// معالجة إشعار قراءة الرسالة
  void _handleReadReceipt(Map<String, dynamic> data) {
    try {
      final messageId = data['messageId'];
      final groupId = data['groupId'];

      // إذا كان إشعار القراءة ينتمي إلى المجموعة المحددة، حدث حالة القراءة
      if (selectedGroup.value != null && groupId == selectedGroup.value!.id) {
        final messageIndex = messages.indexWhere((m) => m.id == messageId);
        if (messageIndex != -1) {
          final message = messages[messageIndex];
          messages[messageIndex] = message.copyWith(isRead: true);
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في معالجة إشعار قراءة الرسالة', e);
    }
  }

  /// معالجة حالة المستخدم
  void _handleUserStatus(Map<String, dynamic> data) {
    try {
      final userId = data['userId'];
      final isOnline = data['isOnline'];

      // تحديث حالة المستخدم
      final userIndex = users.indexWhere((u) => u.id == userId);
      if (userIndex != -1) {
        final user = users[userIndex];
        users[userIndex] = user.copyWith(
          isOnline: isOnline,
          lastSeen: DateTime.now(),
        );
      }
    } catch (e) {
      AppLogger.error('خطأ في معالجة حالة المستخدم', e);
    }
  }

  /// معالجة تحديث المجموعة
  void _handleGroupUpdate(Map<String, dynamic> data) {
    try {
      final group = ChatGroup.fromJson(data);

      // تحديث المجموعة في القائمة
      final groupIndex = groups.indexWhere((g) => g.id == group.id);
      if (groupIndex != -1) {
        groups[groupIndex] = group;
      } else {
        // إضافة المجموعة إذا لم تكن موجودة
        groups.add(group);
      }

      // تحديث المجموعة المحددة إذا كانت هي المجموعة المحدثة
      if (selectedGroup.value != null && selectedGroup.value!.id == group.id) {
        selectedGroup.value = group;

        // تحديث أعضاء المجموعة
        loadGroupMembers(group.id);
      }
    } catch (e) {
      AppLogger.error('خطأ في معالجة تحديث المجموعة', e);
    }
  }

  /// الاشتراك في تحديثات الرسائل
  Worker subscribeToMessagesUpdates(Function() callback) {
    _messagesUpdateSubscribers.add(callback);

    // إرجاع Worker لإلغاء الاشتراك
    return ever(newMessageReceived, (_) {
      callback();
    });
  }

  /// إخطار المشتركين بتحديث الرسائل
  void _notifyMessagesUpdateSubscribers() {
    for (final callback in _messagesUpdateSubscribers) {
      callback();
    }
  }

  @override
  void onClose() {
    _stopTypingTimer();
    super.onClose();
  }
}
