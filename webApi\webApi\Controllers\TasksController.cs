using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة المهام في نظام إدارة المهام
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class TasksController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public TasksController(TasksDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// الحصول على جميع المهام مع البيانات المرتبطة
        /// </summary>
        /// <returns>قائمة بجميع المهام</returns>
        /// <response code="200">إرجاع قائمة المهام</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Models.Task>>> GetTasks()
        {
            return await _context.Tasks
                .Include(t => t.Creator)
                .Include(t => t.Assignee)
                .Include(t => t.Department)
                .Include(t => t.StatusNavigation)
                .Include(t => t.PriorityNavigation)
                .Include(t => t.TaskType)
                .Where(t => !t.IsDeleted)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على مهمة محددة بواسطة المعرف
        /// </summary>
        /// <param name="id">معرف المهمة</param>
        /// <returns>تفاصيل المهمة</returns>
        /// <response code="200">إرجاع المهمة</response>
        /// <response code="404">إذا لم يتم العثور على المهمة</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<Models.Task>> GetTask(int id)
        {
            var task = await _context.Tasks
                .Include(t => t.Creator)
                .Include(t => t.Assignee)
                .Include(t => t.Department)
                .Include(t => t.StatusNavigation)
                .Include(t => t.PriorityNavigation)
                .Include(t => t.TaskType)
                .Include(t => t.Subtasks)
                .Include(t => t.TaskComments)
                .Include(t => t.Attachments)
                .FirstOrDefaultAsync(t => t.Id == id && !t.IsDeleted);

            if (task == null)
            {
                return NotFound();
            }

            return task;
        }

        /// <summary>
        /// Update a task
        /// </summary>
        /// <param name="id">Task ID</param>
        /// <param name="task">Updated task data</param>
        /// <returns>No content</returns>
        /// <response code="204">Task updated successfully</response>
        /// <response code="400">Invalid request</response>
        /// <response code="404">Task not found</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutTask(int id, Models.Task task)
        {
            if (id != task.Id)
            {
                return BadRequest();
            }

            _context.Entry(task).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!TaskExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// Create a new task
        /// </summary>
        /// <param name="task">Task data</param>
        /// <returns>Created task</returns>
        /// <response code="201">Task created successfully</response>
        /// <response code="400">Invalid request</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<Models.Task>> PostTask(Models.Task task)
        {
            task.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            _context.Tasks.Add(task);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetTask", new { id = task.Id }, task);
        }

        /// <summary>
        /// Delete a task (soft delete)
        /// </summary>
        /// <param name="id">Task ID</param>
        /// <returns>No content</returns>
        /// <response code="204">Task deleted successfully</response>
        /// <response code="404">Task not found</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteTask(int id)
        {
            var task = await _context.Tasks.FindAsync(id);
            if (task == null)
            {
                return NotFound();
            }

            task.IsDeleted = true;
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// Get tasks by assignee
        /// </summary>
        /// <param name="assigneeId">Assignee user ID</param>
        /// <returns>List of tasks assigned to the user</returns>
        /// <response code="200">Returns the list of tasks</response>
        [HttpGet("assignee/{assigneeId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Models.Task>>> GetTasksByAssignee(int assigneeId)
        {
            return await _context.Tasks
                .Include(t => t.Creator)
                .Include(t => t.StatusNavigation)
                .Include(t => t.PriorityNavigation)
                .Where(t => t.AssigneeId == assigneeId && !t.IsDeleted)
                .ToListAsync();
        }

        /// <summary>
        /// Get tasks by status
        /// </summary>
        /// <param name="statusId">Status ID</param>
        /// <returns>List of tasks with the specified status</returns>
        /// <response code="200">Returns the list of tasks</response>
        [HttpGet("status/{statusId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Models.Task>>> GetTasksByStatus(int statusId)
        {
            return await _context.Tasks
                .Include(t => t.Creator)
                .Include(t => t.Assignee)
                .Include(t => t.PriorityNavigation)
                .Where(t => t.Status == statusId && !t.IsDeleted)
                .ToListAsync();
        }

        private bool TaskExists(int id)
        {
            return _context.Tasks.Any(e => e.Id == id && !e.IsDeleted);
        }
    }
}
