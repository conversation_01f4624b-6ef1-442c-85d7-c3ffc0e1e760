import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_application_2/controllers/archive_controller.dart';
import 'package:flutter_application_2/models/archive_document_model.dart';
import 'package:flutter_application_2/models/archive_category_model.dart';
import 'package:flutter_application_2/models/archive_tag_model.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/widgets/custom_app_bar.dart';
import 'package:flutter_application_2/widgets/loading_indicator.dart';
import 'package:flutter_application_2/widgets/custom_text_field.dart';
import 'package:flutter_application_2/widgets/custom_date_picker.dart';

/// شاشة تعديل وثيقة أرشيف
class EditDocumentScreen extends StatefulWidget {
  /// الوثيقة المراد تعديلها
  final ArchiveDocument document;

  const EditDocumentScreen({
    super.key,
    required this.document,
  });

  @override
  State<EditDocumentScreen> createState() => _EditDocumentScreenState();
}

class _EditDocumentScreenState extends State<EditDocumentScreen> {
  final ArchiveController _controller = Get.find<ArchiveController>();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _documentNumberController;
  late TextEditingController _issuerController;
  late TextEditingController _recipientController;
  late TextEditingController _changeNotesController;

  DateTime? _documentDate;
  String? _selectedCategoryId;
  List<String> _selectedTagIds = [];
  ArchiveDocumentConfidentiality _confidentiality = ArchiveDocumentConfidentiality.normal;
  ArchiveDocumentImportance _importance = ArchiveDocumentImportance.normal;
  bool _createVersion = true;

  List<ArchiveCategory> _categories = [];
  List<ArchiveTag> _tags = [];
  bool _isLoading = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadData();
  }

  /// تهيئة وحدات التحكم بالنصوص
  void _initializeControllers() {
    _titleController = TextEditingController(text: widget.document.title);
    _descriptionController = TextEditingController(text: widget.document.description);
    _documentNumberController = TextEditingController(text: widget.document.documentNumber);
    _issuerController = TextEditingController(text: widget.document.issuer);
    _recipientController = TextEditingController(text: widget.document.recipient);
    _changeNotesController = TextEditingController();

    _documentDate = widget.document.documentDate;
    _selectedCategoryId = widget.document.categoryId;
    _selectedTagIds = widget.document.tagIds ?? [];
    _confidentiality = widget.document.confidentiality;
    _importance = widget.document.importance;
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل التصنيفات
      _categories = await _controller.archiveService.getAllCategories();

      // تحميل الوسوم
      _tags = await _controller.archiveService.getAllTags();
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _documentNumberController.dispose();
    _issuerController.dispose();
    _recipientController.dispose();
    _changeNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'تعديل وثيقة: ${widget.document.title}',
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معلومات الوثيقة الأساسية
                    Card(
                      elevation: 2,
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'معلومات الوثيقة الأساسية',
                              style: AppStyles.headline6,
                            ),
                            const SizedBox(height: 16),
                            CustomTextField(
                              controller: _titleController,
                              label: 'عنوان الوثيقة',
                              hint: 'أدخل عنوان الوثيقة',
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال عنوان الوثيقة';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            CustomTextField(
                              controller: _descriptionController,
                              label: 'وصف الوثيقة',
                              hint: 'أدخل وصف الوثيقة',
                              maxLines: 3,
                            ),
                            const SizedBox(height: 16),
                            DropdownButtonFormField<String>(
                              decoration: InputDecoration(
                                labelText: 'تصنيف الوثيقة',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              value: _selectedCategoryId,
                              items: _categories.map((category) {
                                return DropdownMenuItem<String>(
                                  value: category.id,
                                  child: Text(category.name),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedCategoryId = value;
                                });
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى اختيار تصنيف الوثيقة';
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                    ),

                    // معلومات الوثيقة الإضافية
                    Card(
                      elevation: 2,
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'معلومات الوثيقة الإضافية',
                              style: AppStyles.headline6,
                            ),
                            const SizedBox(height: 16),
                            CustomTextField(
                              controller: _documentNumberController,
                              label: 'رقم الوثيقة',
                              hint: 'أدخل رقم الوثيقة',
                            ),
                            const SizedBox(height: 16),
                            CustomDatePicker(
                              label: 'تاريخ الوثيقة',
                              selectedDate: _documentDate,
                              onDateSelected: (date) {
                                setState(() {
                                  _documentDate = date;
                                });
                              },
                            ),
                            const SizedBox(height: 16),
                            CustomTextField(
                              controller: _issuerController,
                              label: 'الجهة المصدرة',
                              hint: 'أدخل اسم الجهة المصدرة للوثيقة',
                            ),
                            const SizedBox(height: 16),
                            CustomTextField(
                              controller: _recipientController,
                              label: 'الجهة المستلمة',
                              hint: 'أدخل اسم الجهة المستلمة للوثيقة',
                            ),
                          ],
                        ),
                      ),
                    ),

                    // الوسوم والتصنيفات
                    Card(
                      elevation: 2,
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'الوسوم',
                              style: AppStyles.headline6,
                            ),
                            const SizedBox(height: 16),
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: _tags.map((tag) {
                                final isSelected = _selectedTagIds.contains(tag.id);
                                return FilterChip(
                                  label: Text(tag.name),
                                  selected: isSelected,
                                  selectedColor: Color(int.parse(tag.color)),
                                  checkmarkColor: Colors.white,
                                  backgroundColor: Colors.grey[200],
                                  onSelected: (selected) {
                                    setState(() {
                                      if (selected) {
                                        _selectedTagIds.add(tag.id);
                                      } else {
                                        _selectedTagIds.remove(tag.id);
                                      }
                                    });
                                  },
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // السرية والأهمية
                    Card(
                      elevation: 2,
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'السرية والأهمية',
                              style: AppStyles.headline6,
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: DropdownButtonFormField<ArchiveDocumentConfidentiality>(
                                    decoration: InputDecoration(
                                      labelText: 'مستوى السرية',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    value: _confidentiality,
                                    items: ArchiveDocumentConfidentiality.values.map((confidentiality) {
                                      return DropdownMenuItem<ArchiveDocumentConfidentiality>(
                                        value: confidentiality,
                                        child: Text(_getConfidentialityText(confidentiality)),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      if (value != null) {
                                        setState(() {
                                          _confidentiality = value;
                                        });
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: DropdownButtonFormField<ArchiveDocumentImportance>(
                                    decoration: InputDecoration(
                                      labelText: 'مستوى الأهمية',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    value: _importance,
                                    items: ArchiveDocumentImportance.values.map((importance) {
                                      return DropdownMenuItem<ArchiveDocumentImportance>(
                                        value: importance,
                                        child: Text(_getImportanceText(importance)),
                                      );
                                    }).toList(),
                                    onChanged: (value) {
                                      if (value != null) {
                                        setState(() {
                                          _importance = value;
                                        });
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    // خيارات الإصدار
                    Card(
                      elevation: 2,
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'خيارات الإصدار',
                              style: AppStyles.headline6,
                            ),
                            const SizedBox(height: 16),
                            CheckboxListTile(
                              title: const Text('إنشاء إصدار جديد من الوثيقة قبل التعديل'),
                              value: _createVersion,
                              onChanged: (value) {
                                setState(() {
                                  _createVersion = value ?? true;
                                });
                              },
                              controlAffinity: ListTileControlAffinity.leading,
                              activeColor: AppColors.primary,
                            ),
                            if (_createVersion) ...[
                              const SizedBox(height: 16),
                              CustomTextField(
                                controller: _changeNotesController,
                                label: 'ملاحظات التغيير',
                                hint: 'أدخل ملاحظات حول التغييرات التي قمت بها',
                                maxLines: 3,
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),

                    // أزرار الإجراءات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Get.back(),
                          child: const Text('إلغاء'),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: _isSaving ? null : _saveDocument,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                          ),
                          child: _isSaving
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Text('حفظ التغييرات'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  /// حفظ الوثيقة
  Future<void> _saveDocument() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSaving = true;
      });

      try {
        // إنشاء نسخة محدثة من الوثيقة
        final updatedDocument = widget.document.copyWith(
          title: _titleController.text,
          description: _descriptionController.text,
          categoryId: _selectedCategoryId!,
          tagIds: _selectedTagIds,
          documentNumber: _documentNumberController.text,
          documentDate: _documentDate,
          issuer: _issuerController.text,
          recipient: _recipientController.text,
          confidentiality: _confidentiality,
          importance: _importance,
          updatedAt: DateTime.now(),
        );

        // حفظ الوثيقة
        final success = await _controller.updateDocument(
          updatedDocument,
          createVersion: _createVersion,
          changeNotes: _createVersion ? _changeNotesController.text : null,
        );

        if (success) {
          Get.back(result: true);
          Get.snackbar(
            'تم بنجاح',
            'تم تحديث الوثيقة بنجاح',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );
        } else {
          Get.snackbar(
            'خطأ',
            'فشل في تحديث الوثيقة',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      } catch (e) {
        debugPrint('خطأ في حفظ الوثيقة: $e');
        Get.snackbar(
          'خطأ',
          'حدث خطأ أثناء تحديث الوثيقة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      } finally {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  /// الحصول على نص مستوى السرية
  String _getConfidentialityText(ArchiveDocumentConfidentiality confidentiality) {
    switch (confidentiality) {
      case ArchiveDocumentConfidentiality.normal:
        return 'عادي';
      case ArchiveDocumentConfidentiality.confidential:
        return 'سري';
      case ArchiveDocumentConfidentiality.highlyConfidential:
        return 'سري للغاية';
      case ArchiveDocumentConfidentiality.topSecret:
        return 'سري جداً';
      default:
        return 'عادي';
    }
  }

  /// الحصول على نص مستوى الأهمية
  String _getImportanceText(ArchiveDocumentImportance importance) {
    switch (importance) {
      case ArchiveDocumentImportance.low:
        return 'منخفضة';
      case ArchiveDocumentImportance.normal:
        return 'عادية';
      case ArchiveDocumentImportance.high:
        return 'مرتفعة';
      case ArchiveDocumentImportance.urgent:
        return 'عاجلة';
    }
  }
}
