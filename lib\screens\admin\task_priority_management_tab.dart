import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/task_priority_controller.dart';
import '../../models/task_priority_model.dart';
import '../../constants/app_styles.dart';

/// شاشة إدارة أولويات المهام
///
/// توفر واجهة لإدارة أولويات المهام في النظام
class TaskPriorityManagementTab extends StatefulWidget {
  const TaskPriorityManagementTab({super.key});

  @override
  State<TaskPriorityManagementTab> createState() => _TaskPriorityManagementTabState();
}

class _TaskPriorityManagementTabState extends State<TaskPriorityManagementTab> {
  late TaskPriorityController _taskPriorityController;
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _colorController = TextEditingController();
  final _iconController = TextEditingController();
  final _levelController = TextEditingController();
  
  TaskPriority? _selectedTaskPriority;
  bool _showInactivePriorities = false;
  bool _isDefault = false;

  @override
  void initState() {
    super.initState();
    
    // التحقق من وجود المتحكم أو إنشاؤه
    if (!Get.isRegistered<TaskPriorityController>()) {
      Get.put(TaskPriorityController());
    }
    _taskPriorityController = Get.find<TaskPriorityController>();
    
    // تحميل البيانات
    _loadData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _colorController.dispose();
    _iconController.dispose();
    _levelController.dispose();
    super.dispose();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    await _taskPriorityController.loadTaskPriorities(includeInactive: _showInactivePriorities);
  }

  /// إعادة تعيين نموذج الإدخال
  void _resetForm() {
    _selectedTaskPriority = null;
    _nameController.clear();
    _descriptionController.clear();
    _colorController.clear();
    _iconController.clear();
    _levelController.text = '1';
    _isDefault = false;
    _formKey.currentState?.reset();
  }

  /// تحويل قيمة اللون من نص إلى كائن لون
  Color _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        return Color(int.parse('0xFF${colorString.substring(1)}'));
      } else {
        return Color(int.parse('0xFF$colorString'));
      }
    } catch (e) {
      return Colors.grey;
    }
  }

  /// عرض مربع حوار تأكيد الحذف
  void _showDeleteConfirmationDialog(TaskPriority taskPriority) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف أولوية المهمة "${taskPriority.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              
              final success = await _taskPriorityController.deleteTaskPriority(taskPriority.id);
              
              if (success) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف أولوية المهمة بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } else {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('فشل حذف أولوية المهمة: ${_taskPriorityController.errorMessage.value}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار إنشاء/تعديل أولوية مهمة
  void _showTaskPriorityDialog({TaskPriority? taskPriority}) {
    _selectedTaskPriority = taskPriority;
    
    if (taskPriority != null) {
      _nameController.text = taskPriority.name;
      _descriptionController.text = taskPriority.description;
      _colorController.text = taskPriority.color ?? '';
      _iconController.text = taskPriority.icon ?? '';
      _levelController.text = taskPriority.level.toString();
      _isDefault = taskPriority.isDefault;
    } else {
      _resetForm();
    }
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          taskPriority == null ? 'إنشاء أولوية مهمة جديدة' : 'تعديل أولوية المهمة',
          style: AppStyles.headingMedium,
        ),
        content: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // اسم أولوية المهمة
                TextFormField(
                  controller: _nameController,
                  decoration: AppStyles.inputDecoration(
                    labelText: 'اسم أولوية المهمة',
                    hintText: 'أدخل اسم أولوية المهمة',
                    prefixIcon: const Icon(Icons.priority_high),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال اسم أولوية المهمة';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                
                // وصف أولوية المهمة
                TextFormField(
                  controller: _descriptionController,
                  decoration: AppStyles.inputDecoration(
                    labelText: 'وصف أولوية المهمة',
                    hintText: 'أدخل وصف أولوية المهمة',
                    prefixIcon: const Icon(Icons.description),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال وصف أولوية المهمة';
                    }
                    return null;
                  },
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                
                // لون أولوية المهمة
                TextFormField(
                  controller: _colorController,
                  decoration: AppStyles.inputDecoration(
                    labelText: 'لون أولوية المهمة (اختياري)',
                    hintText: 'أدخل قيمة اللون (مثال: FF5733)',
                    prefixIcon: const Icon(Icons.color_lens),
                  ),
                ),
                const SizedBox(height: 16),
                
                // أيقونة أولوية المهمة
                TextFormField(
                  controller: _iconController,
                  decoration: AppStyles.inputDecoration(
                    labelText: 'أيقونة أولوية المهمة (اختياري)',
                    hintText: 'أدخل اسم الأيقونة',
                    prefixIcon: const Icon(Icons.emoji_objects),
                  ),
                ),
                const SizedBox(height: 16),
                
                // مستوى الأولوية
                TextFormField(
                  controller: _levelController,
                  decoration: AppStyles.inputDecoration(
                    labelText: 'مستوى الأولوية',
                    hintText: 'أدخل رقم مستوى الأولوية (1: منخفضة، 4: عاجلة)',
                    prefixIcon: const Icon(Icons.trending_up),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال مستوى الأولوية';
                    }
                    final level = int.tryParse(value);
                    if (level == null) {
                      return 'الرجاء إدخال رقم صحيح';
                    }
                    if (level < 1 || level > 10) {
                      return 'مستوى الأولوية يجب أن يكون بين 1 و 10';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                
                // أولوية افتراضية
                CheckboxListTile(
                  title: const Text('أولوية افتراضية للمهام الجديدة'),
                  value: _isDefault,
                  onChanged: (value) {
                    setState(() {
                      _isDefault = value ?? false;
                    });
                  },
                  controlAffinity: ListTileControlAffinity.leading,
                ),
              ],
            ),
          ),
        ),
        actions: [
          // زر الإلغاء
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetForm();
            },
            child: const Text('إلغاء'),
          ),
          
          // زر الحفظ
          ElevatedButton(
            onPressed: () async {
              if (_formKey.currentState!.validate()) {
                final name = _nameController.text;
                final description = _descriptionController.text;
                final color = _colorController.text.isEmpty ? null : _colorController.text;
                final icon = _iconController.text.isEmpty ? null : _iconController.text;
                final level = int.tryParse(_levelController.text) ?? 1;
                
                bool success;
                
                if (_selectedTaskPriority == null) {
                  // إنشاء أولوية مهمة جديدة
                  success = await _taskPriorityController.createTaskPriority(
                    name: name,
                    description: description,
                    level: level,
                    color: color,
                    icon: icon,
                    isDefault: _isDefault,
                  );
                } else {
                  // تحديث أولوية المهمة الحالية
                  final updatedTaskPriority = _selectedTaskPriority!.copyWith(
                    name: name,
                    description: description,
                    color: color,
                    icon: icon,
                    isDefault: _isDefault,
                    level: level,
                  );
                  
                  success = await _taskPriorityController.updateTaskPriority(updatedTaskPriority);
                }
                
                if (success) {
                  Navigator.of(context).pop();
                  _resetForm();
                  
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          _selectedTaskPriority == null
                              ? 'تم إنشاء أولوية المهمة بنجاح'
                              : 'تم تحديث أولوية المهمة بنجاح',
                        ),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } else {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'فشل العملية: ${_taskPriorityController.errorMessage.value}',
                        ),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (_taskPriorityController.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        
        if (_taskPriorityController.taskPriorities.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.priority_high_outlined,
                  color: Colors.grey,
                  size: 48,
                ),
                const SizedBox(height: 16),
                const Text(
                  'لا توجد أولويات مهام',
                  style: TextStyle(color: Colors.grey),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => _showTaskPriorityDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('إنشاء أولوية مهمة جديدة'),
                ),
              ],
            ),
          );
        }
        
        return Column(
          children: [
            // شريط الأدوات
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // زر إنشاء أولوية مهمة جديدة
                  ElevatedButton.icon(
                    onPressed: () => _showTaskPriorityDialog(),
                    icon: const Icon(Icons.add),
                    label: const Text('إنشاء أولوية مهمة جديدة'),
                  ),
                  const Spacer(),
                  
                  // خيار عرض الأولويات غير النشطة
                  Row(
                    children: [
                      const Text('عرض الأولويات غير النشطة'),
                      Checkbox(
                        value: _showInactivePriorities,
                        onChanged: (value) {
                          setState(() {
                            _showInactivePriorities = value ?? false;
                            _loadData();
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // قائمة أولويات المهام
            Expanded(
              child: ListView.builder(
                itemCount: _taskPriorityController.taskPriorities.length,
                itemBuilder: (context, index) {
                  final taskPriority = _taskPriorityController.taskPriorities[index];
                  
                  return Card(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: ListTile(
                      leading: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: taskPriority.color != null
                              ? _parseColor(taskPriority.color!)
                              : Colors.grey,
                          shape: BoxShape.circle,
                        ),
                      ),
                      title: Text(
                        taskPriority.name,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: taskPriority.isActive ? null : Colors.grey,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            taskPriority.description,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: taskPriority.isActive ? null : Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'مستوى الأولوية: ${taskPriority.level}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // علامة الافتراضي
                          if (taskPriority.isDefault)
                            const Chip(
                              label: Text('افتراضي'),
                              backgroundColor: Colors.green,
                              labelStyle: TextStyle(color: Colors.white),
                            ),
                          
                          // حالة النشاط
                          if (!taskPriority.isActive)
                            const Chip(
                              label: Text('غير نشط'),
                              backgroundColor: Colors.grey,
                              labelStyle: TextStyle(color: Colors.white),
                            ),
                          
                          // زر التعديل
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () => _showTaskPriorityDialog(taskPriority: taskPriority),
                            tooltip: 'تعديل',
                          ),
                          
                          // زر الحذف
                          IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () => _showDeleteConfirmationDialog(taskPriority),
                            tooltip: 'حذف',
                            color: Colors.red,
                          ),
                        ],
                      ),
                      onTap: () => _showTaskPriorityDialog(taskPriority: taskPriority),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      }),
    );
  }
}
