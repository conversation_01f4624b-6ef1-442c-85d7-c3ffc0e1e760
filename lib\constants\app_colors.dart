import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// ألوان التطبيق
///
/// تم تقسيم الألوان إلى قسمين:
/// 1. ألوان ثابتة: مثل الألوان الأساسية والألوان الثانوية وألوان الحالة
/// 2. ألوان ديناميكية: تتغير حسب وضع السمة (فاتح/داكن)
///
/// عند استخدام الألوان الديناميكية، يجب الانتباه إلى عدم استخدام const معها
/// لأنها تتغير حسب وضع السمة.
///
/// مثال صحيح:
/// Container(color: AppColors.background)
///
/// مثال خاطئ:
/// const Container(color: AppColors.background)
///
/// يمكن استخدام الدوال المساعدة للحصول على ألوان مناسبة للوضع الحالي:
/// - AppColors.getShadowColor(0.1)
/// - AppColors.getContainerColor()
/// - AppColors.getBorderColor()
class AppColors {
  // Primary colors
  static const Color primary = Color(0xFF2196F3);
  static const Color primaryLight = Color(0xFF64B5F6);
  static const Color primaryDark = Color(0xFF1976D2);

  // Accent colors
  static const Color accent = Color(0xFFFFA000);
  static const Color accentLight = Color(0xFFFFB74D);
  static const Color accentDark = Color(0xFFF57C00);

  // Light theme colors
  static const Color lightBackground = Color(0xFFF5F5F5);
  static const Color lightCard = Colors.white;
  static const Color lightDialog = Colors.white;
  static const Color lightTextPrimary = Color(0xFF000000); // تم تحسين لون النص الأساسي ليكون أكثر وضوحًا
  static const Color lightTextSecondary = Color(0xFF424242); // تم تحسين لون النص الثانوي ليكون أكثر وضوحًا
  static const Color lightTextHint = Color(0xFF757575); // تم تحسين لون النص التلميحي ليكون أكثر وضوحًا
  static const Color lightBorder = Color(0xFFE0E0E0);
  static const Color lightDivider = Color(0xFFEEEEEE);
  static const Color lightIcon = Color(0xFF424242); // تم تحسين لون الأيقونة ليكون أكثر وضوحًا

  // Dark theme colors
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkCard = Color(0xFF1E1E1E);
  static const Color darkDialog = Color(0xFF1E1E1E);
  static const Color darkTextPrimary = Color(0xFFFFFFFF); // تم تحسين لون النص الأساسي ليكون أكثر وضوحًا
  static const Color darkTextSecondary = Color(0xFFCCCCCC); // تم تحسين لون النص الثانوي ليكون أكثر وضوحًا
  static const Color darkTextHint = Color(0xFF999999); // تم تحسين لون النص التلميحي ليكون أكثر وضوحًا
  static const Color darkBorder = Color(0xFF333333);
  static const Color darkDivider = Color(0xFF222222);
  static const Color darkIcon = Color(0xFFCCCCCC); // تم تحسين لون الأيقونة ليكون أكثر وضوحًا

  // Dynamic colors that change based on theme
  static Color get background => Get.isDarkMode ? darkBackground : lightBackground;
  static Color get card => Get.isDarkMode ? darkCard : lightCard;
  static Color get dialog => Get.isDarkMode ? darkDialog : lightDialog;
  static Color get textPrimary => Get.isDarkMode ? darkTextPrimary : lightTextPrimary;
  // static Color get textPrimary => Get.isDarkMode ? const Color.fromARGB(255, 6, 6, 6) : const Color.fromARGB(255, 236, 234, 234);
  static Color get textSecondary => Get.isDarkMode ? darkTextSecondary : lightTextSecondary;
  static Color get textHint => Get.isDarkMode ? darkTextHint : lightTextHint;
  static Color get border => Get.isDarkMode ? darkBorder : lightBorder;
  static Color get divider => Get.isDarkMode ? darkDivider : lightDivider;
  static Color get icon => Get.isDarkMode ? darkIcon : lightIcon;

  // Status colors - these remain consistent across themes for clarity
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Task priority colors
  static const Color priorityLow = Color(0xFF8BC34A);
  static const Color priorityMedium = Color(0xFFFFC107);
  static const Color priorityHigh = Color(0xFFFF9800);
  static const Color priorityUrgent = Color(0xFFF44336);

  // Task status colors
  static const Color statusPending = Color(0xFFBDBDBD);
  static const Color statusInProgress = Color(0xFF2196F3);
  static const Color statusWaitingForInfo = Color(0xFFFF9800);
  static const Color statusCompleted = Color(0xFF4CAF50);
  static const Color statusCancelled = Color(0xFF9E9E9E);
  static const Color statusNews = Color(0xFF9C27B0); // لون للمهام الجديدة

  // Button colors
  static const Color buttonPrimary = Color(0xFF2196F3);
  static const Color buttonSecondary = Color(0xFF9E9E9E);
  static const Color buttonDisabled = Color(0xFFE0E0E0);

  // Icon active color
  static const Color iconActive = Color(0xFF2196F3);

  // Get color for task priority
  static Color getTaskPriorityColor(int priority) {
    switch (priority) {
      case 0: // Low
        return priorityLow;
      case 1: // Medium
        return priorityMedium;
      case 2: // High
        return priorityHigh;
      case 3: // Urgent
        return priorityUrgent;
      default:
        return priorityMedium;
    }
  }

  // Get color for task status
  static Color getTaskStatusColor(int status) {
    switch (status) {
      case 0: // Pending
        return statusPending;
      case 1: // In Progress
        return statusInProgress;
      case 2: // Waiting for Info
        return statusWaitingForInfo;
      case 3: // Completed
        return statusCompleted;
      case 4: // Cancelled
        return statusCancelled;
      case 5: // News
        return statusNews;
      default:
        return statusPending;
    }
  }

  // Get shadow color based on theme
  static Color getShadowColor(double opacity) {
    if (Get.isDarkMode) {
      final Color whiteWithOpacity = Color.fromRGBO(255, 255, 255, opacity * 0.3);
      return whiteWithOpacity;
    } else {
      final Color blackWithOpacity = Color.fromRGBO(0, 0, 0, opacity);
      return blackWithOpacity;
    }
  }

  // Get container color based on theme
  static Color getContainerColor({bool lighter = false}) {
    if (Get.isDarkMode) {
      return lighter ? const Color(0xFF2C2C2C) : darkCard;
    } else {
      return lighter ? const Color(0xFFF9F9F9) : lightCard;
    }
  }

  // Get border color based on theme
  static Color getBorderColor() {
    return Get.isDarkMode ? darkBorder : lightBorder;
  }
}
