import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/contribution_report_model.dart';
import '../services/contribution_report_service.dart';
import '../database/contribution_report_repository.dart';
import '../controllers/auth_controller.dart';
import '../controllers/user_controller.dart';

/// وحدة تحكم تقارير المساهمات
/// توفر واجهة للتعامل مع تقارير المساهمات في واجهة المستخدم
class ContributionReportController extends GetxController {
  final ContributionReportService _reportService = ContributionReportService();
  final ContributionReportRepository _reportRepository = ContributionReportRepository();
  final UserController _userController = Get.find<UserController>();

  // حالة التحميل والخطأ
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;

  // قائمة التقارير
  final RxList<ContributionReport> reports = <ContributionReport>[].obs;

  // التقرير الحالي
  final Rx<ContributionReport?> currentReport = Rx<ContributionReport?>(null);

  // بيانات التقرير الحالي
  final RxMap<String, dynamic> reportData = <String, dynamic>{}.obs;
  final RxMap<String, dynamic> reportSummary = <String, dynamic>{}.obs;
  final RxMap<String, dynamic> reportPeriod = <String, dynamic>{}.obs;

  // تفاصيل المساهمات
  final RxList<ContributionDetail> contributionDetails = <ContributionDetail>[].obs;

  // ملخص المساهمات
  final Rx<ContributionSummary?> contributionSummary = Rx<ContributionSummary?>(null);

  @override
  void onInit() {
    super.onInit();
    // إنشاء جدول تقارير المساهمات إذا لم يكن موجودًا
    _reportRepository.createContributionReportsTable();
  }

  /// تحميل جميع تقارير المساهمات
  Future<void> loadAllReports() async {
    isLoading.value = true;
    error.value = '';

    try {
      final allReports = await _reportRepository.getAllContributionReports();
      reports.assignAll(allReports);
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحميل التقارير: ${e.toString()}';
      debugPrint(error.value);
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل تقارير المساهمات للمستخدم الحالي
  Future<void> loadReportsForCurrentUser() async {
    isLoading.value = true;
    error.value = '';

    try {
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;
      if (currentUser == null) {
        error.value = 'المستخدم غير مسجل الدخول';
        return;
      }

      final userReports = await _reportRepository.getContributionReportsForUser(currentUser.id);
      final sharedReports = await _reportRepository.getContributionReportsSharedWithUser(currentUser.id);

      // دمج التقارير وإزالة التكرارات
      final Set<String> reportIds = {};
      final List<ContributionReport> uniqueReports = [];

      for (final report in [...userReports, ...sharedReports]) {
        if (!reportIds.contains(report.id)) {
          reportIds.add(report.id);
          uniqueReports.add(report);
        }
      }

      reports.assignAll(uniqueReports);
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحميل التقارير: ${e.toString()}';
      debugPrint(error.value);
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل تقارير المساهمات لمهمة معينة
  Future<void> loadReportsForTask(String taskId) async {
    isLoading.value = true;
    error.value = '';

    try {
      final taskReports = await _reportRepository.getContributionReportsForTask(taskId);
      reports.assignAll(taskReports);
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحميل تقارير المهمة: ${e.toString()}';
      debugPrint(error.value);
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل تقارير المساهمات لقسم معين
  Future<void> loadReportsForDepartment(String departmentId) async {
    isLoading.value = true;
    error.value = '';

    try {
      final departmentReports = await _reportRepository.getContributionReportsForDepartment(departmentId);
      reports.assignAll(departmentReports);
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحميل تقارير القسم: ${e.toString()}';
      debugPrint(error.value);
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل تقرير مساهمات محدد
  Future<void> loadReport(String reportId) async {
    isLoading.value = true;
    error.value = '';

    try {
      final report = await _reportRepository.getContributionReportById(reportId);
      if (report == null) {
        error.value = 'التقرير غير موجود';
        return;
      }

      currentReport.value = report;

      // تنفيذ التقرير
      final reportResult = await _reportService.executeContributionReport(reportId);
      if (reportResult['isSuccess'] != true) {
        error.value = 'فشل تنفيذ التقرير: ${reportResult['errorMessages']}';
        return;
      }

      // تحديث بيانات التقرير - التعامل مع البيانات بشكل صحيح حسب نوعها
      if (reportResult['data'] is Map<String, dynamic>) {
        reportData.assignAll(reportResult['data']);
      } else if (reportResult['data'] is List) {
        // إذا كانت البيانات قائمة، نقوم بتحويلها إلى خريطة مع مفاتيح مناسبة
        final Map<String, dynamic> dataMap = {'items': reportResult['data']};
        reportData.assignAll(dataMap);
      } else {
        reportData.clear();
      }

      if (reportResult['summary'] is Map<String, dynamic>) {
        reportSummary.assignAll(reportResult['summary']);
      } else {
        reportSummary.clear();
      }

      if (reportResult['period'] is Map<String, dynamic>) {
        reportPeriod.assignAll(reportResult['period']);
      } else {
        reportPeriod.clear();
      }

      // تحويل بيانات التقرير إلى نماذج
      await _parseReportData();
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحميل التقرير: ${e.toString()}';
      debugPrint(error.value);
    } finally {
      isLoading.value = false;
    }
  }

  /// تحليل بيانات التقرير
  Future<void> _parseReportData() async {
    try {
      // تحليل تفاصيل المساهمات
      final List<ContributionDetail> details = [];
      if (reportData.isNotEmpty) {
        // التعامل مع الهيكل الجديد للبيانات
        if (reportData.containsKey('items') && reportData['items'] is List) {
          // البيانات في شكل قائمة مباشرة
          final itemsList = reportData['items'] as List;
          for (final detail in itemsList) {
            if (detail is Map<String, dynamic>) {
              details.add(ContributionDetail(
                id: detail['id'] ?? '',
                taskId: detail['taskId'] ?? '',
                taskTitle: detail['taskTitle'] ?? 'مهمة غير معروفة',
                userId: detail['userId'] ?? '',
                userName: detail['userName'] ?? 'مستخدم غير معروف',
                contributionType: detail['contributionType'] ?? 'unknown',
                contributionPercentage: (detail['contributionPercentage'] is num)
                    ? (detail['contributionPercentage'] as num).toDouble()
                    : 0.0,
                contributionDate: DateTime.fromMillisecondsSinceEpoch(detail['contributionDate'] ?? 0),
                notes: detail['notes'],
                evidenceDescription: detail['evidenceDescription'],
              ));
            }
          }
        } else {
          // الهيكل القديم للبيانات (للتوافق الخلفي)
          for (final item in reportData.values) {
            if (item is List) {
              for (final detail in item) {
                if (detail is Map<String, dynamic>) {
                  details.add(ContributionDetail(
                    id: detail['id'] ?? '',
                    taskId: detail['taskId'] ?? '',
                    taskTitle: detail['taskTitle'] ?? 'مهمة غير معروفة',
                    userId: detail['userId'] ?? '',
                    userName: detail['userName'] ?? 'مستخدم غير معروف',
                    contributionType: detail['contributionType'] ?? 'unknown',
                    contributionPercentage: (detail['contributionPercentage'] is num)
                        ? (detail['contributionPercentage'] as num).toDouble()
                        : 0.0,
                    contributionDate: DateTime.fromMillisecondsSinceEpoch(detail['contributionDate'] ?? 0),
                    notes: detail['notes'],
                    evidenceDescription: detail['evidenceDescription'],
                  ));
                }
              }
            }
          }
        }
      }
      contributionDetails.assignAll(details);

      // تحليل ملخص المساهمات
      if (reportSummary.isNotEmpty) {
        final Map<String, int> contributionsByType = {};
        final Map<String, double> contributionsByUser = {};

        if (reportSummary['contributionsByType'] is Map) {
          final typeMap = reportSummary['contributionsByType'] as Map;
          for (final key in typeMap.keys) {
            if (typeMap[key] is int) {
              contributionsByType[key.toString()] = typeMap[key] as int;
            } else if (typeMap[key] is num) {
              contributionsByType[key.toString()] = (typeMap[key] as num).toInt();
            }
          }
        }

        if (reportSummary['contributionsByUser'] is Map) {
          final userMap = reportSummary['contributionsByUser'] as Map;
          for (final key in userMap.keys) {
            if (userMap[key] is double) {
              contributionsByUser[key.toString()] = userMap[key] as double;
            } else if (userMap[key] is num) {
              contributionsByUser[key.toString()] = (userMap[key] as num).toDouble();
            }
          }
        }

        final totalContributions = reportSummary['totalContributions'] is int
            ? reportSummary['totalContributions'] as int
            : reportSummary['totalContributions'] is num
                ? (reportSummary['totalContributions'] as num).toInt()
                : 0;

        final totalContributors = reportSummary['totalContributors'] is int
            ? reportSummary['totalContributors'] as int
            : reportSummary['totalContributors'] is num
                ? (reportSummary['totalContributors'] as num).toInt()
                : 0;

        final averageContribution = reportSummary['averageContributionPerUser'] is double
            ? reportSummary['averageContributionPerUser'] as double
            : reportSummary['averageContributionPerUser'] is num
                ? (reportSummary['averageContributionPerUser'] as num).toDouble()
                : 0.0;

        final highestContribution = reportSummary['highestContribution'] is double
            ? reportSummary['highestContribution'] as double
            : reportSummary['highestContribution'] is num
                ? (reportSummary['highestContribution'] as num).toDouble()
                : 0.0;

        contributionSummary.value = ContributionSummary(
          totalContributions: totalContributions,
          totalContributors: totalContributors,
          contributionsByType: contributionsByType,
          contributionsByUser: contributionsByUser,
          averageContributionPerUser: averageContribution,
          highestContribution: highestContribution,
          topContributorId: reportSummary['topContributorId']?.toString(),
        );
      }
    } catch (e) {
      debugPrint('خطأ في تحليل بيانات التقرير: $e');
    }
  }

  /// إنشاء تقرير مساهمات جديد
  Future<ContributionReport?> createReport({
    required String title,
    String? description,
    String? taskId,
    String? userId,
    String? departmentId,
    int? periodDays,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    isLoading.value = true;
    error.value = '';

    try {
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;
      if (currentUser == null) {
        error.value = 'المستخدم غير مسجل الدخول';
        return null;
      }

      final report = await _reportService.createContributionReport(
        title: title,
        description: description,
        createdById: currentUser.id,
        taskId: taskId,
        userId: userId,
        departmentId: departmentId,
        periodDays: periodDays,
        startDate: startDate,
        endDate: endDate,
      );

      // إضافة التقرير الجديد إلى القائمة
      reports.add(report);

      return report;
    } catch (e) {
      error.value = 'حدث خطأ أثناء إنشاء التقرير: ${e.toString()}';
      debugPrint(error.value);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// حذف تقرير مساهمات
  Future<bool> deleteReport(String reportId) async {
    isLoading.value = true;
    error.value = '';

    try {
      final result = await _reportRepository.deleteContributionReport(reportId);
      if (result > 0) {
        // إزالة التقرير من القائمة
        reports.removeWhere((report) => report.id == reportId);
        return true;
      } else {
        error.value = 'فشل حذف التقرير';
        return false;
      }
    } catch (e) {
      error.value = 'حدث خطأ أثناء حذف التقرير: ${e.toString()}';
      debugPrint(error.value);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// مشاركة تقرير مساهمات مع مستخدمين
  Future<bool> shareReportWithUsers(String reportId, List<String> userIds) async {
    isLoading.value = true;
    error.value = '';

    try {
      final result = await _reportRepository.shareContributionReportWithUsers(reportId, userIds);
      if (result > 0) {
        // تحديث التقرير في القائمة
        final updatedReport = await _reportRepository.getContributionReportById(reportId);
        if (updatedReport != null) {
          final index = reports.indexWhere((report) => report.id == reportId);
          if (index >= 0) {
            reports[index] = updatedReport;
          }
        }
        return true;
      } else {
        error.value = 'فشل مشاركة التقرير';
        return false;
      }
    } catch (e) {
      error.value = 'حدث خطأ أثناء مشاركة التقرير: ${e.toString()}';
      debugPrint(error.value);
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// تصدير تقرير المساهمات بتنسيق PDF
  Future<String?> exportReportToPdf(String reportId) async {
    isLoading.value = true;
    error.value = '';

    try {
      final filePath = await _reportService.exportContributionReportToPdf(reportId);
      if (filePath == null) {
        error.value = 'فشل تصدير التقرير بتنسيق PDF';
      }
      return filePath;
    } catch (e) {
      error.value = 'حدث خطأ أثناء تصدير التقرير: ${e.toString()}';
      debugPrint(error.value);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// تصدير تقرير المساهمات بتنسيق Excel
  Future<String?> exportReportToExcel(String reportId) async {
    isLoading.value = true;
    error.value = '';

    try {
      final filePath = await _reportService.exportContributionReportToExcel(reportId);
      if (filePath == null) {
        error.value = 'فشل تصدير التقرير بتنسيق Excel';
      }
      return filePath;
    } catch (e) {
      error.value = 'حدث خطأ أثناء تصدير التقرير: ${e.toString()}';
      debugPrint(error.value);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// الحصول على اسم المستخدم بواسطة المعرف
  Future<String> getUserName(String userId) async {
    try {
      return await _userController.getUserNameById(userId);
    } catch (e) {
      return 'مستخدم غير معروف';
    }
  }

  /// تحويل نوع المساهمة إلى نص مقروء
  String getContributionTypeText(String type) {
    switch (type) {
      case 'transfer':
        return 'تحويل مهمة';
      case 'file':
        return 'إرفاق ملف';
      case 'comment':
        return 'تعليق';
      case 'manual':
        return 'تحديث يدوي';
      case 'auto':
        return 'حساب تلقائي';
      case 'activity':
        return 'نشاط متعدد';
      default:
        return 'تحديث تقدم';
    }
  }
}
