﻿using System;
using System.Collections.Generic;

namespace webApi.Models;

public partial class ArchiveTag
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public string? Color { get; set; }

    public bool IsActive { get; set; } = true;

    public int CreatedBy { get; set; }

    public long CreatedAt { get; set; }

    public long? UpdatedAt { get; set; }

    public bool IsDeleted { get; set; }

    public virtual User CreatedByNavigation { get; set; } = null!;

    public virtual ICollection<ArchiveDocument> Documents { get; set; } = new List<ArchiveDocument>();

    public virtual ICollection<ArchiveDocumentTag> ArchiveDocumentTags { get; set; } = new List<ArchiveDocumentTag>();
}
