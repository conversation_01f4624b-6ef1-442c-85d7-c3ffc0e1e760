import 'package:flutter/material.dart';
import 'package:flutter_application_2/constants/app_styles.dart';

/// بطاقة إحصائيات الأرشيف
///
/// تعرض إحصائية واحدة مع عنوان وقيمة وأيقونة
class ArchiveStatsCard extends StatelessWidget {
  /// عنوان البطاقة
  final String title;

  /// قيمة الإحصائية
  final String value;

  /// أيقونة البطاقة
  final IconData icon;

  /// لون البطاقة
  final Color color;

  /// دالة يتم استدعاؤها عند النقر على البطاقة
  final VoidCallback? onTap;

  const ArchiveStatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: color.withAlpha(76),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    color: color,
                    size: 32,
                  ),
                  const Spacer(),
                  Text(
                    value,
                    style: AppStyles.headline4.copyWith(
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: AppStyles.subtitle1,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
