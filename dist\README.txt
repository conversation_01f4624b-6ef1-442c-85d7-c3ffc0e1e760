# تطبيق Flutter Web مع خادم مدمج

## كيفية تشغيل التطبيق

1. انقر نقرًا مزدوجًا على ملف `start_app.bat` لتشغيل التطبيق. سيتم فتح المتصفح تلقائيًا على العنوان المحلي.
2. إذا كنت ترغب في تشغيل التطبيق بدون ظهور نافذة الطرفية، انقر نقرًا مزدوجًا على ملف `start_app_hidden.bat`.
3. لإيقاف الخادم، أغلق نافذة الطرفية أو اضغط على Ctrl+C في نافذة الطرفية.

## ملاحظات

- يعمل التطبيق على المنفذ 8080 افتراضيًا. إذا كان هذا المنفذ مستخدمًا بالفعل، يمكنك تعديل ملف `start_app.bat` لاستخدام منفذ آخر.
- يجب أن يكون لديك متصفح ويب مثبت على جهازك لتشغيل التطبيق.
- لا تحذف أو تنقل أي ملفات داخل هذا المجلد، حيث قد يؤدي ذلك إلى توقف التطبيق عن العمل.
