import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../services/unified_search_service.dart';

/// وحدة تحكم البحث الموحد
/// تدير عمليات البحث وحالة البحث في التطبيق
class UnifiedSearchController extends GetxController {
  // خدمة البحث الموحد
  final UnifiedSearchService _searchService = UnifiedSearchService();

  // حالة البحث
  final RxBool isSearching = false.obs;
  final RxString searchQuery = ''.obs;
  final RxList<SearchResult> searchResults = <SearchResult>[].obs;
  final RxList<SearchResultType> selectedTypes = <SearchResultType>[].obs;
  final RxBool showFilters = false.obs;
  final RxInt resultsPerType = 5.obs;
  final RxBool hasMoreResults = false.obs;

  // سجل البحث
  final RxList<String> searchHistory = <String>[].obs;
  final int maxHistoryItems = 10;

  @override
  void onInit() {
    super.onInit();
    // تحميل سجل البحث من التخزين المحلي
    _loadSearchHistory();

    // تحديد جميع أنواع البحث افتراضيًا
    selectedTypes.assignAll(SearchResultType.values);
  }

  /// تنفيذ البحث
  Future<void> search(String query) async {
    if (query.trim().isEmpty) {
      clearSearch();
      return;
    }

    // تحديث حالة البحث
    searchQuery.value = query;
    isSearching.value = true;

    try {
      // تنفيذ البحث
      final results = await _searchService.search(
        query: query,
        types: selectedTypes,
        limit: resultsPerType.value,
      );

      // تحديث النتائج
      searchResults.assignAll(results);

      // تحديث سجل البحث
      _addToSearchHistory(query);
    } catch (e) {
      debugPrint('خطأ في البحث: $e');
    } finally {
      isSearching.value = false;
    }
  }

  /// مسح البحث
  void clearSearch() {
    searchQuery.value = '';
    searchResults.clear();
  }

  /// تحديد أنواع البحث
  void toggleSearchType(SearchResultType type) {
    if (selectedTypes.contains(type)) {
      selectedTypes.remove(type);
    } else {
      selectedTypes.add(type);
    }

    // إعادة البحث بالأنواع الجديدة
    if (searchQuery.value.isNotEmpty) {
      search(searchQuery.value);
    }
  }

  /// تحديد جميع أنواع البحث
  void selectAllTypes() {
    selectedTypes.assignAll(SearchResultType.values);

    // إعادة البحث بالأنواع الجديدة
    if (searchQuery.value.isNotEmpty) {
      search(searchQuery.value);
    }
  }

  /// إلغاء تحديد جميع أنواع البحث
  void clearAllTypes() {
    selectedTypes.clear();

    // إعادة البحث بالأنواع الجديدة
    if (searchQuery.value.isNotEmpty) {
      search(searchQuery.value);
    }
  }

  /// تغيير عدد النتائج لكل نوع
  void setResultsPerType(int count) {
    resultsPerType.value = count;

    // إعادة البحث بالعدد الجديد
    if (searchQuery.value.isNotEmpty) {
      search(searchQuery.value);
    }
  }

  /// الحصول على عدد النتائج لكل نوع
  Map<SearchResultType, int> getResultCountsByType() {
    final counts = <SearchResultType, int>{};

    for (final type in SearchResultType.values) {
      counts[type] =
          searchResults.where((result) => result.type == type).length;
    }

    return counts;
  }

  /// إضافة عبارة بحث إلى سجل البحث
  void _addToSearchHistory(String query) {
    // إزالة العبارة إذا كانت موجودة بالفعل
    searchHistory.remove(query);

    // إضافة العبارة في بداية القائمة
    searchHistory.insert(0, query);

    // التأكد من عدم تجاوز الحد الأقصى
    if (searchHistory.length > maxHistoryItems) {
      searchHistory.removeLast();
    }

    // حفظ سجل البحث
    saveSearchHistory();
  }

  /// حفظ سجل البحث في التخزين المحلي
  void saveSearchHistory() {
    // TODO: تنفيذ حفظ سجل البحث في التخزين المحلي
  }

  /// تحميل سجل البحث من التخزين المحلي
  void _loadSearchHistory() {
    // TODO: تنفيذ تحميل سجل البحث من التخزين المحلي
  }

  /// مسح سجل البحث
  void clearSearchHistory() {
    searchHistory.clear();
    saveSearchHistory();
  }

  /// البحث المتقدم
  Future<List<SearchResult>> advancedSearch({
    required String query,
    required List<SearchResultType> types,
    DateTime? startDate,
    DateTime? endDate,
    Map<String, dynamic>? additionalFilters,
    int limit = 20,
  }) async {
    // TODO: تنفيذ البحث المتقدم
    return [];
  }

  /// الحصول على اقتراحات البحث
  List<String> getSearchSuggestions(String query) {
    if (query.isEmpty) {
      return searchHistory.take(5).toList();
    }

    // تصفية سجل البحث بناءً على الاستعلام
    return searchHistory
        .where((item) => item.toLowerCase().contains(query.toLowerCase()))
        .take(5)
        .toList();
  }
}
