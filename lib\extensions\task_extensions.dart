import 'package:flutter/material.dart';
import '../models/task_model.dart';

/// امتدادات لتعداد أولوية المهمة
extension TaskPriorityExtension on TaskPriority {
  /// الاسم المعروض بالإنجليزية
  String get displayName {
    switch (this) {
      case TaskPriority.low:
        return 'Low';
      case TaskPriority.medium:
        return 'Medium';
      case TaskPriority.high:
        return 'High';
      case TaskPriority.urgent:
        return 'Urgent';
    }
  }

  /// الاسم المعروض بالعربية
  String get displayNameAr {
    switch (this) {
      case TaskPriority.low:
        return 'منخفضة';
      case TaskPriority.medium:
        return 'متوسطة';
      case TaskPriority.high:
        return 'عالية';
      case TaskPriority.urgent:
        return 'عاجلة';
    }
  }

  /// لون الأولوية
  Color get color {
    switch (this) {
      case TaskPriority.low:
        return Colors.green;
      case TaskPriority.medium:
        return Colors.blue;
      case TaskPriority.high:
        return Colors.orange;
      case TaskPriority.urgent:
        return Colors.red;
    }
  }

  /// رمز الأولوية
  IconData get icon {
    switch (this) {
      case TaskPriority.low:
        return Icons.arrow_downward;

      case TaskPriority.medium:
        return Icons.remove;
      case TaskPriority.high:
        return Icons.arrow_upward;
      case TaskPriority.urgent:
        return Icons.priority_high;
    }
  }
}

// تم نقل امتدادات TaskStatus إلى ملف task_status_enum.dart
// لتجنب التكرار والتعارض
