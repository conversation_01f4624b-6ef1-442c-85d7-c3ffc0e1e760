import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/task_model.dart';
import '../models/task_status_enum.dart'; // إضافة استيراد نموذج حالة المهمة
import '../models/task_history_model.dart';

class TaskProvider with ChangeNotifier {
  final TaskService _taskService = TaskService();
  final TaskRepository _taskRepository = TaskRepository();
  final CommentRepository _commentRepository = CommentRepository();
  final AttachmentRepository _attachmentRepository = AttachmentRepository();
  final TaskHistoryRepository _taskHistoryRepository = TaskHistoryRepository();

  List<Task> _tasks = [];
  Task? _currentTask;
  List<Comment> _comments = [];
  List<Attachment> _attachments = [];
  List<TaskHistory> _taskHistory = [];
  Map<String, double> _userContributions = {};
  bool _isLoading = false;
  String? _error;

  List<Task> get tasks => _tasks;
  Task? get currentTask => _currentTask;
  List<Comment> get comments => _comments;
  List<Attachment> get attachments => _attachments;
  List<TaskHistory> get taskHistory => _taskHistory;
  Map<String, double> get userContributions => _userContributions;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load all tasks
  Future<void> loadAllTasks() async {
    _isLoading = true;
    notifyListeners();

    try {
      _tasks = await _taskRepository.getAllTasks();
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load tasks by department
  Future<void> loadTasksByDepartment(String departmentId) async {
    _isLoading = true;
    notifyListeners();

    try {
      _tasks = await _taskRepository.getTasksByDepartment(departmentId);
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load tasks by assignee
  Future<void> loadTasksByAssignee(String assigneeId) async {
    _isLoading = true;
    notifyListeners();

    try {
      _tasks = await _taskRepository.getTasksByAssignee(assigneeId);
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load tasks by creator
  Future<void> loadTasksByCreator(String creatorId) async {
    _isLoading = true;
    notifyListeners();

    try {
      _tasks = await _taskRepository.getTasksByCreator(creatorId);
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load tasks accessible by user
  Future<void> loadTasksAccessibleByUser(String userId) async {
    _isLoading = true;
    notifyListeners();

    try {
      _tasks = await _taskRepository.getTasksAccessibleByUser(userId);
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load task details
  Future<void> loadTaskDetails(String taskId) async {
    _isLoading = true;
    notifyListeners();

    try {
      _currentTask = await _taskRepository.getTaskById(taskId);
      if (_currentTask != null) {
        await loadTaskComments(taskId);
        await loadTaskAttachments(taskId);
        await loadTaskHistory(taskId);
        await loadUserContributions(taskId);
      }
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load task comments
  Future<void> loadTaskComments(String taskId) async {
    try {
      _comments = await _commentRepository.getCommentsForTask(taskId);
    } catch (e) {
      print('Error loading comments: $e');
      _comments = [];
    }
  }

  // Load task attachments
  Future<void> loadTaskAttachments(String taskId) async {
    try {
      _attachments = await _attachmentRepository.getAttachmentsForTask(taskId);
    } catch (e) {
      print('Error loading attachments: $e');
      _attachments = [];
    }
  }

  // Load task history
  Future<void> loadTaskHistory(String taskId) async {
    try {
      _taskHistory = await _taskHistoryRepository.getHistoryForTask(taskId);
    } catch (e) {
      print('Error loading task history: $e');
      _taskHistory = [];
    }
  }

  // Load user contributions
  Future<void> loadUserContributions(String taskId) async {
    try {
      _userContributions =
          await _taskService.calculateUserContributions(taskId);
    } catch (e) {
      print('Error loading user contributions: $e');
      _userContributions = {};
    }
  }

  // Create a new task
  Future<bool> createTask(Task task) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final createdTask = await _taskService.createTask(task);
      _tasks.add(createdTask);
      return true;
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update task
  Future<bool> updateTask(Task task) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final result = await _taskRepository.updateTask(task);
      if (result > 0) {
        // Update task in list
        final index = _tasks.indexWhere((t) => t.id == task.id);
        if (index >= 0) {
          _tasks[index] = task;
        }

        // Update current task if it's the same
        if (_currentTask?.id == task.id) {
          _currentTask = task;
        }

        return true;
      } else {
        _error = 'Failed to update task';
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Transfer task
  Future<bool> transferTask(
    String taskId,
    String currentUserId,
    String newAssigneeId,
    String? transferNote,
  ) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final result = await _taskService.transferTask(
        taskId,
        currentUserId,
        newAssigneeId,
        transferNote,
      );

      if (result) {
        // Reload task details
        await loadTaskDetails(taskId);

        // Update task in list
        final index = _tasks.indexWhere((t) => t.id == taskId);
        if (index >= 0 && _currentTask != null) {
          _tasks[index] = _currentTask!;
        }

        return true;
      } else {
        _error = 'Failed to transfer task';
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update task status
  Future<bool> updateTaskStatus(
    String taskId,
    String currentUserId,
    TaskStatus newStatus,
    String? statusNote,
  ) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final result = await _taskService.updateTaskStatus(
        taskId,
        currentUserId,
        newStatus,
        statusNote,
      );

      if (result) {
        // Reload task details
        await loadTaskDetails(taskId);

        // Update task in list
        final index = _tasks.indexWhere((t) => t.id == taskId);
        if (index >= 0 && _currentTask != null) {
          _tasks[index] = _currentTask!;
        }

        return true;
      } else {
        _error = 'Failed to update task status';
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update task progress
  Future<bool> updateTaskProgress(
    String taskId,
    String currentUserId,
    double newProgress,
  ) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final result = await _taskService.updateTaskProgress(
        taskId,
        currentUserId,
        newProgress,
      );

      if (result) {
        // Reload task details
        await loadTaskDetails(taskId);

        // Update task in list
        final index = _tasks.indexWhere((t) => t.id == taskId);
        if (index >= 0 && _currentTask != null) {
          _tasks[index] = _currentTask!;
        }

        return true;
      } else {
        _error = 'Failed to update task progress';
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Request information
  Future<bool> requestInformation(
    String taskId,
    String requesterId,
    String targetUserId,
    String requestMessage,
  ) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final result = await _taskService.requestInformation(
        taskId,
        requesterId,
        targetUserId,
        requestMessage,
      );

      if (result) {
        // Reload task history
        await loadTaskHistory(taskId);
        return true;
      } else {
        _error = 'Failed to request information';
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Provide information
  Future<bool> provideInformation(
    String taskId,
    String providerId,
    String targetUserId,
    String infoMessage,
  ) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final result = await _taskService.provideInformation(
        taskId,
        providerId,
        targetUserId,
        infoMessage,
      );

      if (result) {
        // Reload task history
        await loadTaskHistory(taskId);
        return true;
      } else {
        _error = 'Failed to provide information';
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Add comment
  Future<bool> addComment(Comment comment) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final createdComment = await _taskService.addComment(comment);
      if (createdComment != null) {
        _comments.add(createdComment);
        return true;
      } else {
        _error = 'Failed to add comment';
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Add attachment
  Future<bool> addAttachment(
    String taskId,
    String uploaderId,
    File file,
    String fileName,
    String fileType,
  ) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final attachment = await _taskService.addAttachment(
        taskId,
        uploaderId,
        file,
        fileName,
        fileType,
      );

      if (attachment != null) {
        _attachments.add(attachment);
        return true;
      } else {
        _error = 'Failed to add attachment';
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Delete task
  Future<bool> deleteTask(String taskId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final result = await _taskRepository.deleteTask(taskId);
      if (result > 0) {
        _tasks.removeWhere((task) => task.id == taskId);
        if (_currentTask?.id == taskId) {
          _currentTask = null;
        }
        return true;
      } else {
        _error = 'Failed to delete task';
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear current task
  void clearCurrentTask() {
    _currentTask = null;
    _comments = [];
    _attachments = [];
    _taskHistory = [];
    _userContributions = {};
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
