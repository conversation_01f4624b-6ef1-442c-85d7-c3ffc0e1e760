import 'package:get/get.dart';
import 'package:flutter/foundation.dart' as foundation;
import '../models/user_model.dart';

import '../controllers/auth_controller.dart';

class UserController extends GetxController {
  final UserRepository _userRepository = UserRepository();

  final RxList<User> users = <User>[].obs;
  final RxMap<String, bool> userOnlineStatus = <String, bool>{}.obs;
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadUsersByPermissions();
  }

  /// تحميل المستخدمين حسب صلاحيات المستخدم الحالي
  Future<void> loadUsersByPermissions() async {
    try {
      final authController = Get.find<AuthController>();
      final currentUser = authController.currentUser.value;

      if (currentUser == null) {
        foundation.debugPrint('لا يوجد مستخدم مسجل دخول');
        return;
      }

      if (authController.isSuperAdmin) {
        // مدير النظام العام يرى جميع المستخدمين
        await loadAllUsers();
      } else if (authController.isAdmin && currentUser.departmentId != null) {
        // مدير الإدارة يرى مستخدمي إدارته فقط
        await loadUsersByDepartment(currentUser.departmentId!);
      } else if (authController.isDepartmentManager &&
          currentUser.departmentId != null) {
        // مدير القسم يرى مستخدمي قسمه فقط
        await loadUsersByDepartment(currentUser.departmentId!);
      } else {
        // الموظف العادي لا يرى قائمة المستخدمين
        users.value = [];
      }
    } catch (e) {
      foundation.debugPrint('خطأ في تحميل المستخدمين حسب الصلاحيات: $e');
      await loadAllUsers(); // fallback
    }
  }

  /// تحميل جميع المستخدمين
  ///
  /// يقوم بتحميل جميع المستخدمين من قاعدة البيانات ويحفظهم في قائمة المستخدمين
  /// كما يقوم بتحديث حالة الاتصال لكل مستخدم
  Future<void> loadAllUsers() async {
    // استخدام Future.microtask لتأجيل تحديث الحالة
    await Future.microtask(() => isLoading.value = true);

    try {
      // تحميل جميع المستخدمين من قاعدة البيانات
      users.value = await _userRepository.getAllUsers();

      // تحديث حالة الاتصال لكل مستخدم (في تطبيق حقيقي، هذا سيكون من خلال خدمة اتصال)
      // هنا نقوم بتعيين المستخدم الحالي كمتصل والباقي كغير متصلين
      try {
        final authController = Get.find<AuthController>();
        final currentUserId = authController.currentUser.value?.id;

        for (final user in users) {
          userOnlineStatus[user.id] = user.id == currentUserId;
        }
      } catch (e) {
        foundation.debugPrint('Error updating online status: $e');
      }

      error.value = '';
    } catch (e) {
      error.value = e.toString();
      foundation.debugPrint('Error loading users: $e');
    } finally {
      // استخدام Future.microtask لتأجيل تحديث الحالة
      await Future.microtask(() => isLoading.value = false);
    }
  }

  /// تحميل المستخدمين حسب القسم
  ///
  /// يقوم بتحميل المستخدمين المنتمين لقسم معين من قاعدة البيانات
  Future<void> loadUsersByDepartment(String departmentId) async {
    // استخدام Future.microtask لتأجيل تحديث الحالة
    await Future.microtask(() => isLoading.value = true);

    try {
      users.value = await _userRepository.getUsersByDepartment(departmentId);

      // تحديث حالة الاتصال لكل مستخدم
      try {
        final authController = Get.find<AuthController>();
        final currentUserId = authController.currentUser.value?.id;

        for (final user in users) {
          userOnlineStatus[user.id] = user.id == currentUserId;
        }
      } catch (e) {
        foundation.debugPrint('Error updating online status: $e');
      }

      error.value = '';
    } catch (e) {
      error.value = e.toString();
      foundation.debugPrint('Error loading users by department: $e');
    } finally {
      // استخدام Future.microtask لتأجيل تحديث الحالة
      await Future.microtask(() => isLoading.value = false);
    }
  }

  /// الحصول على مستخدم بواسطة المعرف
  ///
  /// يقوم بالبحث عن مستخدم بواسطة معرفه وإرجاعه
  Future<User?> getUserById(String userId) async {
    try {
      // البحث أولاً في القائمة المحملة مسبقاً لتحسين الأداء
      final cachedUser = users.firstWhereOrNull((user) => user.id == userId);
      if (cachedUser != null) {
        return cachedUser;
      }

      // إذا لم يتم العثور على المستخدم في الذاكرة المؤقتة، قم بتحميله من قاعدة البيانات
      return await _userRepository.getUserById(userId);
    } catch (e) {
      foundation.debugPrint('Error getting user by ID: $e');
      return null;
    }
  }

  /// الحصول على اسم المستخدم بواسطة المعرف
  ///
  /// يقوم بالبحث عن مستخدم بواسطة معرفه وإرجاع اسمه
  Future<String> getUserNameById(String userId) async {
    try {
      // البحث أولاً في القائمة المحملة مسبقاً لتحسين الأداء
      final cachedUser = users.firstWhereOrNull((user) => user.id == userId);
      if (cachedUser != null) {
        return cachedUser.name;
      }

      // إذا لم يتم العثور على المستخدم في الذاكرة المؤقتة، قم بتحميله من قاعدة البيانات
      final user = await _userRepository.getUserById(userId);
      return user?.name ?? 'مستخدم غير معروف';
    } catch (e) {
      foundation.debugPrint('Error getting user name by ID: $e');
      return 'مستخدم غير معروف';
    }
  }

  /// الحصول على حالة اتصال المستخدم
  ///
  /// يقوم بإرجاع حالة اتصال المستخدم (متصل أو غير متصل)
  bool isUserOnline(String userId) {
    return userOnlineStatus[userId] ?? false;
  }

  /// تحديث حالة اتصال المستخدم
  ///
  /// يقوم بتحديث حالة اتصال المستخدم (متصل أو غير متصل)
  void updateUserOnlineStatus(String userId, bool isOnline) {
    userOnlineStatus[userId] = isOnline;
  }

  /// مسح الخطأ
  void clearError() {
    error.value = '';
  }
}
