using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة علامات الأرشيف
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class ArchiveTagsController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public ArchiveTagsController(TasksDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// الحصول على جميع علامات الأرشيف
        /// </summary>
        /// <returns>قائمة بجميع علامات الأرشيف</returns>
        /// <response code="200">إرجاع قائمة علامات الأرشيف</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveTag>>> GetArchiveTags()
        {
            return await _context.ArchiveTags
                .Include(at => at.CreatedByNavigation)
                .Include(at => at.ArchiveDocumentTags.Where(adt => !adt.Document.IsDeleted))
                .Where(at => !at.IsDeleted)
                .OrderBy(at => at.Name)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على علامة أرشيف محددة
        /// </summary>
        /// <param name="id">معرف علامة الأرشيف</param>
        /// <returns>علامة الأرشيف المطلوبة</returns>
        /// <response code="200">إرجاع علامة الأرشيف</response>
        /// <response code="404">علامة الأرشيف غير موجودة</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<ArchiveTag>> GetArchiveTag(int id)
        {
            var archiveTag = await _context.ArchiveTags
                .Include(at => at.CreatedByNavigation)
                .Include(at => at.ArchiveDocumentTags.Where(adt => !adt.Document.IsDeleted))
                .ThenInclude(adt => adt.Document)
                .FirstOrDefaultAsync(at => at.Id == id && !at.IsDeleted);

            if (archiveTag == null)
            {
                return NotFound();
            }

            return archiveTag;
        }

        /// <summary>
        /// البحث في علامات الأرشيف
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <returns>قائمة علامات الأرشيف المطابقة</returns>
        /// <response code="200">إرجاع قائمة علامات الأرشيف المطابقة</response>
        [HttpGet("search")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveTag>>> SearchTags([FromQuery] string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await GetArchiveTags();
            }

            return await _context.ArchiveTags
                .Include(at => at.CreatedByNavigation)
                .Where(at => !at.IsDeleted &&
                           (at.Name.Contains(searchTerm) ||
                            (at.Description != null && at.Description.Contains(searchTerm))))
                .OrderBy(at => at.Name)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على العلامات الأكثر استخداماً
        /// </summary>
        /// <param name="limit">عدد العلامات المطلوب إرجاعها</param>
        /// <returns>قائمة العلامات الأكثر استخداماً</returns>
        /// <response code="200">إرجاع قائمة العلامات الأكثر استخداماً</response>
        [HttpGet("popular")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<object>>> GetPopularTags([FromQuery] int limit = 10)
        {
            var popularTags = await _context.ArchiveTags
                .Include(at => at.ArchiveDocumentTags)
                .Where(at => !at.IsDeleted)
                .Select(at => new
                {
                    at.Id,
                    at.Name,
                    at.Description,
                    at.Color,
                    DocumentCount = at.ArchiveDocumentTags.Count(adt => !adt.Document.IsDeleted)
                })
                .Where(x => x.DocumentCount > 0)
                .OrderByDescending(x => x.DocumentCount)
                .Take(limit)
                .ToListAsync();

            return Ok(popularTags);
        }

        /// <summary>
        /// عدد الوثائق المرتبطة بعلامة محددة
        /// </summary>
        /// <param name="id">معرف علامة الأرشيف</param>
        /// <returns>عدد الوثائق</returns>
        /// <response code="200">إرجاع عدد الوثائق</response>
        [HttpGet("{id}/documents/count")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<int>> GetDocumentsCount(int id)
        {
            var count = await _context.ArchiveDocumentTags
                .Include(adt => adt.Document)
                .CountAsync(adt => adt.TagId == id && !adt.Document.IsDeleted);

            return count;
        }

        /// <summary>
        /// الحصول على الوثائق المرتبطة بعلامة محددة
        /// </summary>
        /// <param name="id">معرف علامة الأرشيف</param>
        /// <returns>قائمة الوثائق المرتبطة بالعلامة</returns>
        /// <response code="200">إرجاع قائمة الوثائق</response>
        [HttpGet("{id}/documents")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<ArchiveDocument>>> GetTagDocuments(int id)
        {
            var documents = await _context.ArchiveDocumentTags
                .Include(adt => adt.Document)
                .ThenInclude(d => d.Category)
                .Include(adt => adt.Document)
                .ThenInclude(d => d.CreatedByNavigation)
                .Where(adt => adt.TagId == id && !adt.Document.IsDeleted)
                .Select(adt => adt.Document)
                .OrderByDescending(d => d.CreatedAt)
                .ToListAsync();

            return documents;
        }

        /// <summary>
        /// إنشاء علامة أرشيف جديدة
        /// </summary>
        /// <param name="archiveTag">بيانات علامة الأرشيف</param>
        /// <returns>علامة الأرشيف المُنشأة</returns>
        /// <response code="201">تم إنشاء علامة الأرشيف بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ArchiveTag>> PostArchiveTag(ArchiveTag archiveTag)
        {
            // التحقق من صحة البيانات
            if (string.IsNullOrWhiteSpace(archiveTag.Name))
            {
                return BadRequest("اسم العلامة مطلوب");
            }

            // التحقق من عدم وجود علامة بنفس الاسم
            var existingTag = await _context.ArchiveTags
                .FirstOrDefaultAsync(at => at.Name.ToLower() == archiveTag.Name.ToLower() && !at.IsDeleted);

            if (existingTag != null)
            {
                return BadRequest("يوجد علامة بهذا الاسم مسبقاً");
            }

            archiveTag.CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            archiveTag.IsDeleted = false;

            _context.ArchiveTags.Add(archiveTag);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetArchiveTag", new { id = archiveTag.Id }, archiveTag);
        }

        /// <summary>
        /// تحديث علامة أرشيف
        /// </summary>
        /// <param name="id">معرف علامة الأرشيف</param>
        /// <param name="archiveTag">بيانات علامة الأرشيف المحدثة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث علامة الأرشيف بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">علامة الأرشيف غير موجودة</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutArchiveTag(int id, ArchiveTag archiveTag)
        {
            if (id != archiveTag.Id)
            {
                return BadRequest();
            }

            // التحقق من صحة البيانات
            if (string.IsNullOrWhiteSpace(archiveTag.Name))
            {
                return BadRequest("اسم العلامة مطلوب");
            }

            // التحقق من عدم وجود علامة أخرى بنفس الاسم
            var existingTag = await _context.ArchiveTags
                .FirstOrDefaultAsync(at => at.Name.ToLower() == archiveTag.Name.ToLower() &&
                                          at.Id != id && !at.IsDeleted);

            if (existingTag != null)
            {
                return BadRequest("يوجد علامة أخرى بهذا الاسم مسبقاً");
            }

            _context.Entry(archiveTag).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!ArchiveTagExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// ربط علامة بوثيقة أرشيف
        /// </summary>
        /// <param name="tagId">معرف العلامة</param>
        /// <param name="documentId">معرف الوثيقة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم ربط العلامة بالوثيقة بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">العلامة أو الوثيقة غير موجودة</response>
        [HttpPost("{tagId}/documents/{documentId}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> TagDocument(int tagId, int documentId)
        {
            // التحقق من وجود العلامة
            var tagExists = await _context.ArchiveTags.AnyAsync(at => at.Id == tagId && !at.IsDeleted);
            if (!tagExists)
            {
                return NotFound("العلامة غير موجودة");
            }

            // التحقق من وجود الوثيقة
            var documentExists = await _context.ArchiveDocuments.AnyAsync(ad => ad.Id == documentId && !ad.IsDeleted);
            if (!documentExists)
            {
                return NotFound("الوثيقة غير موجودة");
            }

            // التحقق من عدم وجود الربط مسبقاً
            var existingLink = await _context.ArchiveDocumentTags
                .AnyAsync(adt => adt.TagId == tagId && adt.DocumentId == documentId);

            if (existingLink)
            {
                return BadRequest("الوثيقة مرتبطة بالعلامة مسبقاً");
            }

            var archiveDocumentTag = new ArchiveDocumentTag
            {
                TagId = tagId,
                DocumentId = documentId
            };

            _context.ArchiveDocumentTags.Add(archiveDocumentTag);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// إلغاء ربط علامة بوثيقة أرشيف
        /// </summary>
        /// <param name="tagId">معرف العلامة</param>
        /// <param name="documentId">معرف الوثيقة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم إلغاء ربط العلامة بالوثيقة بنجاح</response>
        /// <response code="404">الربط غير موجود</response>
        [HttpDelete("{tagId}/documents/{documentId}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UntagDocument(int tagId, int documentId)
        {
            var archiveDocumentTag = await _context.ArchiveDocumentTags
                .FirstOrDefaultAsync(adt => adt.TagId == tagId && adt.DocumentId == documentId);

            if (archiveDocumentTag == null)
            {
                return NotFound("الربط غير موجود");
            }

            _context.ArchiveDocumentTags.Remove(archiveDocumentTag);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// حذف علامة أرشيف (حذف منطقي)
        /// </summary>
        /// <param name="id">معرف علامة الأرشيف</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف علامة الأرشيف بنجاح</response>
        /// <response code="404">علامة الأرشيف غير موجودة</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteArchiveTag(int id)
        {
            var archiveTag = await _context.ArchiveTags.FindAsync(id);
            if (archiveTag == null || archiveTag.IsDeleted)
            {
                return NotFound();
            }

            // حذف منطقي بدلاً من الحذف الفعلي
            archiveTag.IsDeleted = true;
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// تفعيل/إلغاء تفعيل علامة أرشيف
        /// </summary>
        /// <param name="id">معرف علامة الأرشيف</param>
        /// <param name="active">حالة التفعيل</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث حالة العلامة بنجاح</response>
        /// <response code="404">علامة الأرشيف غير موجودة</response>
        [HttpPatch("{id}/toggle-active")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> ToggleActive(int id, [FromQuery] bool active)
        {
            var archiveTag = await _context.ArchiveTags.FindAsync(id);
            if (archiveTag == null || archiveTag.IsDeleted)
            {
                return NotFound();
            }

            archiveTag.IsActive = active;
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// الحصول على إحصائيات العلامات
        /// </summary>
        /// <returns>إحصائيات العلامات</returns>
        /// <response code="200">إرجاع إحصائيات العلامات</response>
        [HttpGet("statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetTagsStatistics()
        {
            var totalTags = await _context.ArchiveTags.CountAsync(at => !at.IsDeleted);
            var activeTags = await _context.ArchiveTags.CountAsync(at => !at.IsDeleted && at.IsActive);
            var tagsWithDocuments = await _context.ArchiveTags
                .Where(at => !at.IsDeleted && at.ArchiveDocumentTags.Any(adt => !adt.Document.IsDeleted))
                .CountAsync();
            var totalTaggedDocuments = await _context.ArchiveDocumentTags
                .Include(adt => adt.Document)
                .CountAsync(adt => !adt.Document.IsDeleted);

            return Ok(new
            {
                TotalTags = totalTags,
                ActiveTags = activeTags,
                InactiveTags = totalTags - activeTags,
                TagsWithDocuments = tagsWithDocuments,
                UnusedTags = totalTags - tagsWithDocuments,
                TotalTaggedDocuments = totalTaggedDocuments
            });
        }

        /// <summary>
        /// دمج علامتين في علامة واحدة
        /// </summary>
        /// <param name="sourceTagId">معرف العلامة المصدر</param>
        /// <param name="targetTagId">معرف العلامة الهدف</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم دمج العلامات بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">إحدى العلامات غير موجودة</response>
        [HttpPost("{sourceTagId}/merge/{targetTagId}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> MergeTags(int sourceTagId, int targetTagId)
        {
            if (sourceTagId == targetTagId)
            {
                return BadRequest("لا يمكن دمج العلامة مع نفسها");
            }

            var sourceTag = await _context.ArchiveTags.FindAsync(sourceTagId);
            var targetTag = await _context.ArchiveTags.FindAsync(targetTagId);

            if (sourceTag == null || sourceTag.IsDeleted || targetTag == null || targetTag.IsDeleted)
            {
                return NotFound("إحدى العلامات غير موجودة");
            }

            // نقل جميع الوثائق من العلامة المصدر إلى العلامة الهدف
            var sourceDocumentTags = await _context.ArchiveDocumentTags
                .Where(adt => adt.TagId == sourceTagId)
                .ToListAsync();

            foreach (var docTag in sourceDocumentTags)
            {
                // التحقق من عدم وجود ربط مسبق مع العلامة الهدف
                var existingLink = await _context.ArchiveDocumentTags
                    .AnyAsync(adt => adt.TagId == targetTagId && adt.DocumentId == docTag.DocumentId);

                if (!existingLink)
                {
                    docTag.TagId = targetTagId;
                }
                else
                {
                    // إزالة الربط المكرر
                    _context.ArchiveDocumentTags.Remove(docTag);
                }
            }

            // حذف العلامة المصدر
            sourceTag.IsDeleted = true;

            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool ArchiveTagExists(int id)
        {
            return _context.ArchiveTags.Any(e => e.Id == id && !e.IsDeleted);
        }
    }
}
