import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_application_2/controllers/archive_controller.dart';

import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/utils/responsive_helper.dart';
import 'package:flutter_application_2/widgets/custom_app_bar.dart';
import 'package:flutter_application_2/widgets/loading_indicator.dart';

import 'document_browser_screen.dart';
import 'document_upload_screen.dart';
import 'category_management_screen.dart';
import 'tag_management_screen.dart';
import 'widgets/archive_stats_card.dart';
import 'widgets/recent_documents_list.dart';
import 'widgets/category_tree_view.dart';

/// شاشة الصفحة الرئيسية للأرشيف الإلكتروني
class ArchiveHomeScreen extends StatefulWidget {
  const ArchiveHomeScreen({super.key});

  @override
  State<ArchiveHomeScreen> createState() => _ArchiveHomeScreenState();
}

class _ArchiveHomeScreenState extends State<ArchiveHomeScreen> {
  final ArchiveController _controller = Get.find<ArchiveController>();

  @override
  void initState() {
    super.initState();
    // تحميل البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _controller.loadDocuments();
      _controller.loadRecentDocuments();
      _controller.loadCategories();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isLargeScreen = ResponsiveHelper.isLargeScreen(context);
    final isMediumScreen = ResponsiveHelper.isMediumScreen(context);
    final isSmallScreen = ResponsiveHelper.isSmallScreen(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'نظام الأرشفة الإلكترونية',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: () {
              _controller.loadDocuments();
              _controller.loadRecentDocuments();
              _controller.loadCategories();
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            tooltip: 'الإعدادات',
            onPressed: () {
              _showSettingsMenu(context);
            },
          ),
        ],
      ),
      body: GetBuilder<ArchiveController>(
        builder: (controller) {
          if (controller.isLoading.value) {
            return const Center(child: LoadingIndicator());
          }
          return _buildBody(context, isLargeScreen, isMediumScreen, isSmallScreen);
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Get.to(() => const DocumentUploadScreen());
        },
        icon: const Icon(Icons.upload_file),
        label: const Text('رفع وثيقة'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  Widget _buildBody(BuildContext context, bool isLargeScreen, bool isMediumScreen, bool isSmallScreen) {
    if (isLargeScreen) {
      return _buildLargeScreenLayout(context);
    } else if (isMediumScreen) {
      return _buildMediumScreenLayout(context);
    } else {
      return _buildSmallScreenLayout(context);
    }
  }

  Widget _buildLargeScreenLayout(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // القائمة الجانبية
        SizedBox(
          width: 280,
          child: Card(
            margin: const EdgeInsets.all(8.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'التصنيفات',
                    style: AppStyles.subtitle1,
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: CategoryTreeView(
                      categories: _controller.categoryTree,
                      onCategorySelected: (category) {
                        _controller.setSelectedCategory(category);
                        Get.to(() => DocumentBrowserScreen());
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // المحتوى الرئيسي
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // بطاقات الإحصائيات
                _buildStatsCards(),

                const SizedBox(height: 24),

                // الوثائق الحديثة
                Text(
                  'الوثائق المضافة حديثًا',
                  style: AppStyles.headline6,
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: RecentDocumentsList(
                    documents: _controller.recentDocuments,
                    onDocumentTap: (document) {
                      _controller.currentDocument.value = document;
                      Get.to(() => DocumentBrowserScreen(
                        initialDocumentId: document.id,
                      ));
                    },
                  ),
                ),

                const SizedBox(height: 16),

                // أزرار الإجراءات السريعة
                _buildQuickActionButtons(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMediumScreenLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقات الإحصائيات
          _buildStatsCards(),

          const SizedBox(height: 24),

          // أزرار الإجراءات السريعة
          _buildQuickActionButtons(),

          const SizedBox(height: 24),

          // التصنيفات
          Text(
            'التصنيفات',
            style: AppStyles.headline6,
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 200,
            child: CategoryTreeView(
              categories: _controller.categoryTree,
              onCategorySelected: (category) {
                _controller.setSelectedCategory(category);
                Get.to(() => DocumentBrowserScreen());
              },
            ),
          ),

          const SizedBox(height: 24),

          // الوثائق الحديثة
          Text(
            'الوثائق المضافة حديثًا',
            style: AppStyles.headline6,
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 300,
            child: RecentDocumentsList(
              documents: _controller.recentDocuments,
              onDocumentTap: (document) {
                _controller.currentDocument.value = document;
                Get.to(() => DocumentBrowserScreen(
                  initialDocumentId: document.id,
                ));
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSmallScreenLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // أزرار الإجراءات السريعة
          _buildQuickActionButtons(),

          const SizedBox(height: 16),

          // بطاقات الإحصائيات
          _buildStatsCards(),

          const SizedBox(height: 24),

          // الوثائق الحديثة
          Text(
            'الوثائق المضافة حديثًا',
            style: AppStyles.headline6,
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 300,
            child: RecentDocumentsList(
              documents: _controller.recentDocuments,
              onDocumentTap: (document) {
                _controller.currentDocument.value = document;
                Get.to(() => DocumentBrowserScreen(
                  initialDocumentId: document.id,
                ));
              },
            ),
          ),

          const SizedBox(height: 24),

          // التصنيفات
          Text(
            'التصنيفات',
            style: AppStyles.headline6,
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 200,
            child: CategoryTreeView(
              categories: _controller.categoryTree,
              onCategorySelected: (category) {
                _controller.setSelectedCategory(category);
                Get.to(() => DocumentBrowserScreen());
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCards() {
    final totalDocuments = _controller.documents.length;
    final recentDocuments = _controller.recentDocuments.length;
    final totalCategories = _controller.categories.length;

    return GridView.count(
      crossAxisCount: ResponsiveHelper.isSmallScreen(context) ? 1 :
                     ResponsiveHelper.isMediumScreen(context) ? 2 : 3,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        ArchiveStatsCard(
          title: 'إجمالي الوثائق',
          value: totalDocuments.toString(),
          icon: Icons.description,
          color: AppColors.primary,
          onTap: () {
            _controller.resetFilters();
            Get.to(() => DocumentBrowserScreen());
          },
        ),
        ArchiveStatsCard(
          title: 'الوثائق الحديثة',
          value: recentDocuments.toString(),
          icon: Icons.new_releases,
          color: AppColors.success,
          onTap: () {
            _controller.resetFilters();
            _controller.setSorting('createdAt', false);
            Get.to(() => DocumentBrowserScreen());
          },
        ),
        ArchiveStatsCard(
          title: 'التصنيفات',
          value: totalCategories.toString(),
          icon: Icons.category,
          color: AppColors.warning,
          onTap: () {
            Get.to(() => CategoryManagementScreen());
          },
        ),
      ],
    );
  }

  Widget _buildQuickActionButtons() {
    return Wrap(
      spacing: 16,
      runSpacing: 16,
      children: [
        ElevatedButton.icon(
          onPressed: () {
            Get.to(() => DocumentBrowserScreen());
          },
          icon: const Icon(Icons.search),
          label: const Text('تصفح الوثائق'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
        ElevatedButton.icon(
          onPressed: () {
            Get.to(() => const DocumentUploadScreen());
          },
          icon: const Icon(Icons.upload_file),
          label: const Text('رفع وثيقة'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.success,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
        ElevatedButton.icon(
          onPressed: () {
            Get.to(() => CategoryManagementScreen());
          },
          icon: const Icon(Icons.category),
          label: const Text('إدارة التصنيفات'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.warning,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
        ElevatedButton.icon(
          onPressed: () {
            Get.to(() => TagManagementScreen());
          },
          icon: const Icon(Icons.local_offer),
          label: const Text('إدارة الوسوم'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.info,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  void _showSettingsMenu(BuildContext context) {
    showMenu(
      context: context,
      position: const RelativeRect.fromLTRB(100, 80, 0, 0),
      items: [
        PopupMenuItem(
          value: 'categories',
          child: const Row(
            children: [
              Icon(Icons.category),
              SizedBox(width: 8),
              Text('إدارة التصنيفات'),
            ],
          ),
          onTap: () {
            Future.delayed(const Duration(milliseconds: 100), () {
              Get.to(() => CategoryManagementScreen());
            });
          },
        ),
        PopupMenuItem(
          value: 'tags',
          child: const Row(
            children: [
              Icon(Icons.local_offer),
              SizedBox(width: 8),
              Text('إدارة الوسوم'),
            ],
          ),
          onTap: () {
            Future.delayed(const Duration(milliseconds: 100), () {
              Get.to(() => TagManagementScreen());
            });
          },
        ),
        PopupMenuItem(
          value: 'repair',
          child: const Row(
            children: [
              Icon(Icons.build, color: Colors.orange),
              SizedBox(width: 8),
              Text('إصلاح نظام الأرشفة'),
            ],
          ),
          onTap: () {
            Future.delayed(const Duration(milliseconds: 100), () {
              Get.toNamed('/settings/archive-system-repair');
            });
          },
        ),
      ],
    );
  }
}
