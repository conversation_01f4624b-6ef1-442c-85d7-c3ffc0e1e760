import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/auth_controller.dart';

import '../routes/app_routes.dart';

/// وسيط الصلاحيات الموحد
///
/// يستخدم للتحقق من صلاحيات المستخدم قبل الانتقال إلى الشاشات المختلفة
/// يعتمد على نظام الأدوار المخصصة والصلاحيات المرنة
class UnifiedPermissionMiddleware extends GetMiddleware {
  final AuthController _authController = Get.find<AuthController>();

  @override
  RouteSettings? redirect(String? route) {
    // إذا كان المسار هو صفحة تسجيل الدخول أو التسجيل أو الصفحة الرئيسية، نسمح بالوصول
    if (route == AppRoutes.login ||
        route == AppRoutes.register ||
        route == AppRoutes.home) {
      return null;
    }

    // التحقق من تسجيل الدخول
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) {
      // إذا لم يكن المستخدم مسجل الدخول، نوجهه إلى صفحة تسجيل الدخول
      return const RouteSettings(name: AppRoutes.login);
    }

    // التحقق من الصلاحيات باستخدام النظام المرن
    return _checkRoutePermission(route, currentUser);
  }

  /// التحقق من صلاحية الوصول للمسار باستخدام النظام المرن
  RouteSettings? _checkRoutePermission(String? route, dynamic currentUser) {
    // تحويل المسار إلى اسم واجهة
    final interfaceName = _getInterfaceNameFromRoute(route);
    if (interfaceName == null) {
      // إذا لم نتمكن من تحديد الواجهة، نسمح بالوصول مؤقتاً
      // سيتم التحقق من الصلاحية في الشاشة نفسها
      debugPrint(
          'لم نتمكن من تحديد الواجهة للمسار: $route، سيتم التحقق من الصلاحيات في الشاشة نفسها');
      return null;
    }

    // التحقق من الصلاحية بشكل غير متزامن
    // نظراً لأن redirect يجب أن يكون متزامناً، سنستخدم طريقة أخرى
    _checkInterfaceAccessAsync(interfaceName, route);

    // نسمح بالوصول مؤقتاً والتحقق سيتم في الشاشة نفسها
    debugPrint(
        'السماح بالوصول المؤقت إلى: $route (واجهة: $interfaceName)، سيتم التحقق من الصلاحية في الشاشة');
    return null;
  }

  /// التحقق من صلاحية الوصول للواجهة بشكل غير متزامن
  void _checkInterfaceAccessAsync(String interfaceName, String? route) async {
    try {
      final unifiedPermissionService = Get.find<UnifiedPermissionService>();
      final hasAccess =
          await unifiedPermissionService.checkInterfaceAccess(interfaceName);

      if (!hasAccess) {
        // تأخير التنفيذ للسماح للشاشة بالتحميل أولاً
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Get.snackbar(
            'خطأ في الصلاحيات',
            'ليس لديك صلاحية للوصول إلى هذه الصفحة',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
          Get.offNamed(AppRoutes.home);
        });
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحية الواجهة: $e');
    }
  }

  /// تحويل المسار إلى اسم الواجهة
  String? _getInterfaceNameFromRoute(String? route) {
    if (route == null) return null;

    // قائمة المسارات والواجهات المقابلة لها
    final Map<String, String> routeToInterface = {
      '/tasks': 'tasks',
      '/task/': 'tasks',
      '/admin': 'admin',
      '/admin/roles': 'roles',
      '/admin/permission': 'admin',
      '/department': 'departments',
      '/user': 'users',
      '/report': 'reports',
      '/setting': 'settings',
      '/message': 'messages',
      '/chat': 'messages',
      '/notification': 'notifications',
      '/home': 'dashboard',
      '/dashboard': 'dashboard',
      '/gantt': 'gantt_chart',
      '/power-bi': 'power_bi',
      '/calendar': 'calendar',
      '/database': 'database',
      '/roles': 'roles',
      '/archive': 'archive',
      '/analytics': 'analytics',
      '/backup': 'backups',
      '/sync': 'settings',
    };

    // البحث عن الواجهة المناسبة للمسار
    for (final entry in routeToInterface.entries) {
      if (route.startsWith(entry.key)) {
        return entry.value;
      }
    }

    return null;
  }
}

/// دالة مساعدة للتحقق من صلاحية الوصول إلى واجهة معينة
///
/// تستخدم في الشاشات التي لا يمكن التحقق من صلاحياتها عن طريق المسار
Future<bool> checkInterfaceAccess(String interfaceName) async {
  final permissionService = Get.find<UnifiedPermissionService>();
  final authController = Get.find<AuthController>();

  // إذا كان المستخدم مدير النظام، فلديه وصول إلى جميع الواجهات
  if (authController.isAdmin) {
    return true;
  }

  final currentUser = authController.currentUser.value;
  if (currentUser == null) {
    Get.offNamed('/login');
    return false;
  }

  // التحقق من صلاحية الوصول إلى الواجهة
  final hasAccess = await permissionService.checkInterfaceAccess(interfaceName);

  if (!hasAccess) {
    Get.snackbar(
      'خطأ في الصلاحيات',
      'ليس لديك صلاحية للوصول إلى هذه الصفحة',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
    Get.offNamed('/home');
    return false;
  }

  return true;
}

/// دالة مساعدة للتحقق من صلاحية معينة
///
/// تستخدم للتحقق من صلاحيات المهام والبيانات
Future<bool> checkPermission(PermissionType type, PermissionScope scope) async {
  final permissionService = Get.find<UnifiedPermissionService>();

  // تحديد الواجهة المناسبة للمجال
  String interfaceName;
  switch (scope) {
    case PermissionScope.tasks:
      interfaceName = 'tasks';
      break;
    case PermissionScope.users:
      interfaceName = 'users';
      break;
    case PermissionScope.departments:
      interfaceName = 'departments';
      break;
    case PermissionScope.messages:
      interfaceName = 'messages';
      break;
    case PermissionScope.reports:
      interfaceName = 'reports';
      break;
    default:
      interfaceName = '';
  }

  return await permissionService.checkPermission(
    interfaceName: interfaceName,
    type: type,
    scope: scope,
  );
}
