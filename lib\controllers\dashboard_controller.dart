import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import 'dart:convert';

import '../models/dashboard_model.dart';
// Import with alias to resolve ambiguity
import '../models/dashboard_widget_model.dart' as widget_model;
import '../services/dashboard_service.dart';
// تم إزالة database_service - سيتم استخدام API
import 'auth_controller.dart';

/// وحدة التحكم في لوحة المعلومات
class DashboardController extends GetxController {
  final DashboardService _dashboardService = Get.find<DashboardService>();
  // تم إزالة DatabaseService - سيتم استخدام API
  final AuthController _authController = Get.find<AuthController>();

  // قائمة لوحات المعلومات
  final RxList<Dashboard> _dashboards = <Dashboard>[].obs;

  // لوحة المعلومات الحالية
  final Rx<Dashboard?> _currentDashboard = Rx<Dashboard?>(null);

  // حالة التحميل
  final RxBool _isLoading = false.obs;

  // رسالة الخطأ
  final RxString _errorMessage = ''.obs;

  /// الحصول على قائمة لوحات المعلومات
  List<Dashboard> get dashboards => _dashboards;

  /// الحصول على لوحة المعلومات الحالية
  Dashboard? get currentDashboard => _currentDashboard.value;

  /// هل جاري التحميل
  bool get isLoading => _isLoading.value;

  /// رسالة الخطأ
  String get errorMessage => _errorMessage.value;

  @override
  void onInit() {
    super.onInit();

    // التأكد من وجود متحكم المصادقة
    try {
      // تحميل لوحات المعلومات عند بدء التطبيق
      if (_authController.isLoggedIn) {
        loadDashboards();
      } else {
        debugPrint('لم يتم تسجيل الدخول. لن يتم تحميل لوحات المعلومات.');
      }
    } catch (e) {
      debugPrint('خطأ في تهيئة متحكم لوحة المعلومات: $e');
      // إعادة المحاولة بعد فترة قصيرة
      Future.delayed(const Duration(milliseconds: 500), () {
        if (Get.isRegistered<AuthController>()) {
          if (_authController.isLoggedIn) {
            loadDashboards();
          }
        }
      });
    }
  }

  /// تحميل لوحات المعلومات
  Future<void> loadDashboards() async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final userId = _authController.currentUser.value!.id;
      final dashboards = await _dashboardService.loadDashboardsByUserId(userId);
      _dashboards.assignAll(dashboards);
      _isLoading.value = false;
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء تحميل لوحات المعلومات: $e';
      _isLoading.value = false;
    }
  }

  /// الحصول على لوحات المعلومات للمستخدم
  Future<List<Dashboard>> getDashboardsByUserId(String userId) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final dashboards = await _dashboardService.loadDashboardsByUserId(userId);
      _isLoading.value = false;
      return dashboards;
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء تحميل لوحات المعلومات: $e';
      _isLoading.value = false;
      return [];
    }
  }

  /// الحصول على لوحة المعلومات بواسطة المعرف
  Future<Dashboard?> getDashboardById(String dashboardId) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final dashboard = await _dashboardService.getDashboardById(dashboardId);
      _isLoading.value = false;
      return dashboard;
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء تحميل لوحة المعلومات: $e';
      _isLoading.value = false;
      return null;
    }
  }

  /// الحصول على لوحة المعلومات الافتراضية للمستخدم
  Future<Dashboard> getUserDefaultDashboard(String userId) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // البحث عن لوحة المعلومات الافتراضية
      final dashboards = await _dashboardService.getDashboardsByUserId(userId);

      // البحث عن لوحة المعلومات الافتراضية
      final defaultDashboard =
          dashboards.firstWhereOrNull((dashboard) => dashboard.isDefault);

      if (defaultDashboard != null) {
        _currentDashboard.value = defaultDashboard;
        _isLoading.value = false;
        return defaultDashboard;
      }

      // إذا لم يتم العثور على لوحة معلومات افتراضية، إنشاء واحدة جديدة
      final newDashboard = await createDashboard(
        name: 'لوحة المعلومات الرئيسية',
        userId: userId,
        isDefault: true,
      );

      _currentDashboard.value = newDashboard;
      _isLoading.value = false;
      return newDashboard;
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء تحميل لوحة المعلومات الافتراضية: $e';
      _isLoading.value = false;

      // إنشاء لوحة معلومات افتراضية في الذاكرة
      final defaultDashboard = Dashboard(
        id: const Uuid().v4(),
        title: 'لوحة المعلومات الرئيسية',
        ownerId: userId,
        widgets: [],
        isDefault: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _currentDashboard.value = defaultDashboard;
      return defaultDashboard;
    }
  }

  /// إنشاء لوحة معلومات جديدة
  Future<Dashboard> createDashboard({
    required String name,
    required String userId,
    String? description,
    Color? backgroundColor,
    bool isDefault = false,
  }) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final now = DateTime.now();

      final dashboard = Dashboard(
        id: const Uuid().v4(),
        title: name,
        description: description,
        ownerId: userId,
        widgets: [],
        color: backgroundColor
            ?.toString()
            .replaceAll('Color(0x', '')
            .replaceAll(')', ''),
        isDefault: isDefault,
        createdAt: now,
        updatedAt: now,
      );

      // حفظ لوحة المعلومات في قاعدة البيانات
      await saveDashboard(dashboard);

      // إذا كانت لوحة المعلومات افتراضية، تحديث جميع لوحات المعلومات الأخرى
      if (isDefault) {
        await _dashboardService.setDefaultDashboard(dashboard.id, userId);
      }

      // إضافة لوحة المعلومات إلى القائمة
      _dashboards.add(dashboard);

      _isLoading.value = false;
      return dashboard;
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء إنشاء لوحة المعلومات: $e';
      _isLoading.value = false;
      rethrow;
    }
  }

  /// تحديث لوحة المعلومات
  Future<void> updateDashboard(Dashboard dashboard) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // تحديث تاريخ التحديث
      final updatedDashboard = dashboard.copyWith(
        updatedAt: DateTime.now(),
      );

      // حفظ لوحة المعلومات في قاعدة البيانات
      await _dashboardService.saveDashboard(updatedDashboard);

      // إذا كانت لوحة المعلومات افتراضية، تحديث جميع لوحات المعلومات الأخرى
      if (updatedDashboard.isDefault) {
        await _dashboardService.setDefaultDashboard(
            updatedDashboard.id, updatedDashboard.ownerId);
      }

      // تحديث لوحة المعلومات في القائمة
      final index = _dashboards.indexWhere((d) => d.id == updatedDashboard.id);
      if (index != -1) {
        _dashboards[index] = updatedDashboard;
      }

      // تحديث لوحة المعلومات الحالية إذا كانت هي نفسها
      if (_currentDashboard.value?.id == updatedDashboard.id) {
        _currentDashboard.value = updatedDashboard;
      }

      _isLoading.value = false;
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء تحديث لوحة المعلومات: $e';
      _isLoading.value = false;
    }
  }

  /// حذف لوحة المعلومات
  Future<void> deleteDashboard(String dashboardId) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // حذف لوحة المعلومات من قاعدة البيانات
      await _dashboardService.deleteDashboard(dashboardId);

      // حذف لوحة المعلومات من القائمة
      _dashboards.removeWhere((dashboard) => dashboard.id == dashboardId);

      // إذا كانت لوحة المعلومات الحالية هي المحذوفة، تعيين لوحة المعلومات الحالية إلى null
      if (_currentDashboard.value?.id == dashboardId) {
        _currentDashboard.value = null;
      }

      _isLoading.value = false;
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء حذف لوحة المعلومات: $e';
      _isLoading.value = false;
    }
  }

  /// الحصول على بيانات العنصر
  Future<List<Map<String, dynamic>>> getWidgetData(
      widget_model.DashboardWidget widget) async {
    try {
      // TODO: تنفيذ تحميل البيانات عبر API
      // final result = await apiService.executeQuery(widget.query);

      // بيانات وهمية مؤقتة
      return [
        {'name': 'عنصر 1', 'value': 10},
        {'name': 'عنصر 2', 'value': 20},
      ];
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء تحميل بيانات العنصر: $e';
      rethrow;
    }
  }

  /// حفظ لوحة المعلومات
  Future<void> saveDashboard(Dashboard dashboard) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // حفظ لوحة المعلومات في قاعدة البيانات
      await _dashboardService.saveDashboard(dashboard);

      // تحديث لوحة المعلومات في القائمة
      final index = _dashboards.indexWhere((d) => d.id == dashboard.id);
      if (index != -1) {
        _dashboards[index] = dashboard;
      } else {
        _dashboards.add(dashboard);
      }

      _isLoading.value = false;
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء حفظ لوحة المعلومات: $e';
      _isLoading.value = false;
      rethrow;
    }
  }

  /// حفظ عناصر لوحة المعلومات
  Future<void> saveDashboardWidgets(
      String dashboardId, List<widget_model.DashboardWidget>? widgets) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      // الحصول على لوحة المعلومات
      final dashboard = await getDashboardById(dashboardId);

      if (dashboard != null && widgets != null) {
        // تحويل العناصر إلى النموذج المناسب
        final List<DashboardWidget> dashboardWidgets = [];

        // تحويل كل عنصر من نموذج widget_model.DashboardWidget إلى نموذج DashboardWidget
        for (final widget in widgets) {
          // استخراج البيانات من العنصر
          final id = widget.id;
          final title = widget.title;
          final type = _convertWidgetType(widget.type);
          final settings = _createSettingsFromWidget(widget);
          final rowIndex = (widget.position.dy / 100.0).round();
          final columnIndex = (widget.position.dx / 100.0).round();
          final width = (widget.size.width / 100.0).round();
          final height = (widget.size.height / 100.0).round();

          // إنشاء عنصر جديد
          final dashboardWidget = DashboardWidget(
            id: id,
            title: title,
            type: type,
            settings: settings,
            rowIndex: rowIndex,
            columnIndex: columnIndex,
            width: width,
            height: height,
            isExpandable: true,
            isExpanded: false,
            isRefreshable: true,
          );

          dashboardWidgets.add(dashboardWidget);
        }

        // تحديث العناصر
        final updatedDashboard = dashboard.copyWith(
          widgets: dashboardWidgets,
          updatedAt: DateTime.now(),
        );

        // حفظ لوحة المعلومات
        await saveDashboard(updatedDashboard);
      }

      _isLoading.value = false;
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء حفظ عناصر لوحة المعلومات: $e';
      _isLoading.value = false;
      rethrow;
    }
  }

  /// الحصول على مصادر البيانات المتاحة
  Future<List<String>> getAvailableDataSources() async {
    try {
      // في هذا المثال، نفترض أن مصدر البيانات الوحيد هو قاعدة البيانات المحلية
      return ['local_database'];
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء تحميل مصادر البيانات: $e';
      rethrow;
    }
  }

  /// الحصول على الاستعلامات المتاحة
  Future<List<String>> getAvailableQueries(String dataSource,
      {String? currentQuery}) async {
    try {
      // قائمة الاستعلامات الافتراضية
      final List<String> queries = [
        'SELECT * FROM tasks',
        'SELECT * FROM users',
        'SELECT * FROM departments',
        'SELECT * FROM task_comments',
        'SELECT * FROM task_attachments',
        'SELECT * FROM task_history',
        'SELECT status, COUNT(*) as count FROM tasks GROUP BY status',
        'SELECT date(created_at) as date, COUNT(*) as count FROM tasks GROUP BY date(created_at)',
        'SELECT u.name as userName, COUNT(t.id) as taskCount FROM users u LEFT JOIN tasks t ON u.id = t.assigned_to GROUP BY u.id',
        'SELECT d.name as departmentName, COUNT(t.id) as taskCount FROM departments d LEFT JOIN tasks t ON d.id = t.department_id GROUP BY d.id',
      ];

      // إضافة الاستعلام الحالي إذا كان موجودًا ولم يكن في القائمة
      if (currentQuery != null &&
          currentQuery.isNotEmpty &&
          !queries.contains(currentQuery)) {
        queries.add(currentQuery);
      }

      return queries;
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء تحميل الاستعلامات: $e';
      rethrow;
    }
  }

  /// الحصول على الحقول المتاحة
  Future<List<String>> getAvailableFields(
      String dataSource, String query) async {
    try {
      // TODO: تنفيذ الحصول على أسماء الأعمدة عبر API
      // final result = await apiService.getColumnNames(query);

      // أسماء أعمدة وهمية مؤقتة
      final result = [
        {'name': 'عنصر', 'value': 0}
      ];

      if (result.isEmpty) {
        // إذا كان الاستعلام لا يعيد نتائج، نحاول استخراج أسماء الأعمدة من الاستعلام نفسه
        if (query.toLowerCase().contains('select') &&
            query.toLowerCase().contains('from')) {
          // استخراج أسماء الأعمدة من الاستعلام
          final selectPart = query
              .substring(query.toLowerCase().indexOf('select') + 6,
                  query.toLowerCase().indexOf('from'))
              .trim();

          // تقسيم الأعمدة
          final columns = selectPart.split(',').map((col) {
            // إزالة المسافات الزائدة
            final trimmedCol = col.trim();

            // التعامل مع حالة COUNT(*) as count
            if (trimmedCol.toLowerCase().contains(' as ')) {
              return trimmedCol
                  .substring(trimmedCol.toLowerCase().indexOf(' as ') + 4)
                  .trim();
            }

            // التعامل مع أسماء الجداول المؤهلة مثل u.name
            if (trimmedCol.contains('.')) {
              return trimmedCol.substring(trimmedCol.indexOf('.') + 1).trim();
            }

            return trimmedCol;
          }).toList();

          return columns;
        }

        // إذا لم نتمكن من استخراج الأعمدة، نعيد قائمة افتراضية
        return ['id', 'name', 'value', 'count', 'date'];
      }

      // استخراج أسماء الأعمدة من النتيجة الأولى
      return result.first.keys.toList();
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء تحميل الحقول: $e';
      // إرجاع قائمة افتراضية في حالة حدوث خطأ
      return ['id', 'name', 'value', 'count', 'date'];
    }
  }

  /// تحويل نوع العنصر من نموذج widget_model.DashboardWidgetType إلى نموذج DashboardWidgetType
  DashboardWidgetType _convertWidgetType(
      widget_model.DashboardWidgetType type) {
    switch (type) {
      case widget_model.DashboardWidgetType.barChart:
        return DashboardWidgetType.userPerformanceChart;
      case widget_model.DashboardWidgetType.lineChart:
        return DashboardWidgetType.taskProgressChart;
      case widget_model.DashboardWidgetType.pieChart:
        return DashboardWidgetType.taskStatusChart;
      case widget_model.DashboardWidgetType.table:
        return DashboardWidgetType.taskList;
      case widget_model.DashboardWidgetType.kpi:
        return DashboardWidgetType.kpi;
      case widget_model.DashboardWidgetType.custom:
        return DashboardWidgetType.custom;
      default:
        return DashboardWidgetType.custom;
    }
  }

  /// إنشاء إعدادات العنصر من نموذج widget_model.DashboardWidget
  String _createSettingsFromWidget(widget_model.DashboardWidget widget) {
    // إنشاء خريطة الإعدادات
    final Map<String, dynamic> settings = {
      'showValues': widget.showValues ?? true,
      'showLabels': widget.showLabels ?? true,
      'showGrid': widget.showGrid ?? true,
      'showLegend': widget.showLegend ?? true,
    };

    // إضافة نوع المخطط حسب نوع العنصر
    switch (widget.type) {
      case widget_model.DashboardWidgetType.barChart:
        settings['chartType'] = 'bar';
        break;
      case widget_model.DashboardWidgetType.lineChart:
        settings['chartType'] = 'line';
        settings['showDots'] = widget.showDots ?? true;
        settings['showArea'] = widget.showArea ?? false;
        break;
      case widget_model.DashboardWidgetType.pieChart:
        settings['chartType'] = 'pie';
        settings['showPercentages'] = true;
        break;
      case widget_model.DashboardWidgetType.table:
        settings['chartType'] = 'table';
        settings['showRowNumbers'] = widget.showRowNumbers ?? true;
        settings['pageSize'] = widget.pageSize ?? 10;
        break;
      case widget_model.DashboardWidgetType.kpi:
        settings['chartType'] = 'kpi';
        settings['format'] = widget.format;
        break;
      default:
        settings['chartType'] = 'custom';
        break;
    }

    // تحويل الإعدادات إلى سلسلة نصية JSON
    return jsonEncode(settings);
  }
}
