@echo off
chcp 65001 >nul
title نظام إدارة المهام

echo ========================================
echo       نظام إدارة المهام
echo ========================================
echo.
echo جاري تشغيل التطبيق...
echo.

cd /d "%~dp0portable"

if exist "flutter_application_2.exe" (
    start "" "flutter_application_2.exe"
    echo تم تشغيل التطبيق بنجاح!
    echo يمكنك إغلاق هذه النافذة الآن.
) else (
    echo خطأ: لم يتم العثور على ملف التطبيق!
    echo تأكد من وجود مجلد portable وملف flutter_application_2.exe
    pause
)

timeout /t 3 >nul
