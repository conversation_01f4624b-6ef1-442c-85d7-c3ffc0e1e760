import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/message_controller.dart';
import '../../models/chat_group_model.dart';
import '../../models/group_member_model.dart';
import '../../utils/date_formatter.dart';
import 'add_members_screen.dart';

class ChatInfoScreen extends StatefulWidget {
  final ChatGroup chatGroup;

  const ChatInfoScreen({
    super.key,
    required this.chatGroup,
  });

  @override
  State<ChatInfoScreen> createState() => _ChatInfoScreenState();
}

class _ChatInfoScreenState extends State<ChatInfoScreen> {
  final _messageController = Get.find<MessageController>();
  final _authController = Get.find<AuthController>();

  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadGroupMembers();

    // Initialize text controllers
    _nameController.text = widget.chatGroup.name;
    _descriptionController.text = widget.chatGroup.description ?? '';
  }

  Future<void> _loadGroupMembers() async {
    await _messageController.loadGroupMembers(widget.chatGroup.id);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) {
      return const Scaffold(
        body: Center(child: Text('User not logged in')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Group Info'),
        actions: [
          if (!widget.chatGroup.isDirectMessage)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                _showEditGroupDialog(context);
              },
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Group avatar and info
            Center(
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: widget.chatGroup.isDirectMessage ? AppColors.accent : AppColors.primary,
                    child: widget.chatGroup.avatarUrl != null
                        ? Image.network(widget.chatGroup.avatarUrl!)
                        : Text(
                            widget.chatGroup.name.substring(0, 1).toUpperCase(),
                            style: const TextStyle(color: Colors.white, fontSize: 36),
                          ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    widget.chatGroup.name,
                    style: AppStyles.titleLarge,
                    textAlign: TextAlign.center,
                  ),
                  if (widget.chatGroup.description != null && widget.chatGroup.description!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        widget.chatGroup.description!,
                        style: AppStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  const SizedBox(height: 8),
                  Text(
                    'Created ${DateFormatter.formatDateTime(widget.chatGroup.createdAt)}',
                    style: AppStyles.labelSmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // Group actions
            if (!widget.chatGroup.isDirectMessage)
              Card(
                child: Column(
                  children: [
                    ListTile(
                      leading: const Icon(Icons.person_add),
                      title: const Text('Add members'),
                      onTap: () {
                        Get.to(() => AddMembersScreen(chatGroup: widget.chatGroup));
                      },
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: const Icon(Icons.notifications_off_outlined),
                      title: const Text('Mute notifications'),
                      onTap: () {
                        // TODO: Implement mute notifications
                      },
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: const Icon(Icons.exit_to_app, color: Colors.red),
                      title: const Text('Leave group', style: TextStyle(color: Colors.red)),
                      onTap: () {
                        _confirmLeaveGroup();
                      },
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 24),

            // Members section
            Text(
              'Members',
              style: AppStyles.titleMedium,
            ),
            const SizedBox(height: 8),

            Obx(() {
              if (_messageController.groupMembers.isEmpty) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              return Card(
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _messageController.groupMembers.length,
                  itemBuilder: (context, index) {
                    final member = _messageController.groupMembers[index];
                    return _buildMemberItem(member);
                  },
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildMemberItem(GroupMember member) {
    final user = _messageController.users[member.userId];
    final isCurrentUser = member.userId == _authController.currentUser.value?.id;

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: AppColors.accent,
        backgroundImage: user?.profileImage != null
            ? NetworkImage(user!.profileImage!)
            : null,
        child: user?.profileImage == null
            ? Text(
                (user?.name ?? 'User').substring(0, 1).toUpperCase(),
                style: const TextStyle(color: Colors.white),
              )
            : null,
      ),
      title: Row(
        children: [
          Text(
            user?.name ?? 'Unknown User',
            style: AppStyles.titleSmall,
          ),
          if (isCurrentUser)
            Padding(
              padding: const EdgeInsets.only(left: 8),
              child: Text(
                '(You)',
                style: AppStyles.labelSmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
        ],
      ),
      subtitle: Text(
        _getRoleText(member.role),
        style: AppStyles.bodySmall.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
      trailing: member.role != GroupMemberRole.owner && !isCurrentUser
          ? IconButton(
              icon: const Icon(Icons.more_vert),
              onPressed: () {
                _showMemberOptions(member);
              },
            )
          : null,
    );
  }

  String _getRoleText(GroupMemberRole role) {
    switch (role) {
      case GroupMemberRole.owner:
        return 'Owner';
      case GroupMemberRole.admin:
        return 'Admin';
      case GroupMemberRole.member:
        return 'Member';
      default:
        return 'Member';
    }
  }

  void _showMemberOptions(GroupMember member) {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return;

    // Check if current user is admin or owner
    final currentMember = _messageController.groupMembers.firstWhere(
      (m) => m.userId == currentUser.id,
      orElse: () => GroupMember(
        id: '',
        groupId: '',
        userId: '',
        role: GroupMemberRole.member,
        joinedAt: DateTime.now(),
      ),
    );

    final isAdminOrOwner = currentMember.role == GroupMemberRole.admin ||
                          currentMember.role == GroupMemberRole.owner;

    if (!isAdminOrOwner) return;

    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.remove_circle_outline, color: Colors.red),
                title: const Text('Remove from group', style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.pop(context);
                  _confirmRemoveMember(member);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _confirmRemoveMember(GroupMember member) {
    final user = _messageController.users[member.userId];

    Get.defaultDialog(
      title: 'Remove Member',
      middleText: 'Are you sure you want to remove ${user?.name ?? 'this member'} from the group?',
      textConfirm: 'Remove',
      textCancel: 'Cancel',
      confirmTextColor: Colors.white,
      onConfirm: () {
        _messageController.leaveGroup(widget.chatGroup.id, member.userId).then((success) {
          if (success) {
            Get.back();
            _loadGroupMembers();
            Get.snackbar(
              'Success',
              '${user?.name ?? 'Member'} has been removed from the group',
              snackPosition: SnackPosition.BOTTOM,
            );
          } else {
            Get.back();
            Get.snackbar(
              'Error',
              'Failed to remove member: ${_messageController.error.value}',
              snackPosition: SnackPosition.BOTTOM,
            );
          }
        });
      },
    );
  }

  void _confirmLeaveGroup() {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return;

    Get.defaultDialog(
      title: 'Leave Group',
      middleText: 'Are you sure you want to leave this group? You will no longer receive messages from this group.',
      textConfirm: 'Leave',
      textCancel: 'Cancel',
      confirmTextColor: Colors.white,
      onConfirm: () {
        _messageController.leaveGroup(widget.chatGroup.id, currentUser.id).then((success) {
          if (success) {
            Get.back();
            Get.back();
            Get.back();
            Get.snackbar(
              'Success',
              'You have left the group',
              snackPosition: SnackPosition.BOTTOM,
            );
          } else {
            Get.back();
            Get.snackbar(
              'Error',
              'Failed to leave the group: ${_messageController.error.value}',
              snackPosition: SnackPosition.BOTTOM,
            );
          }
        });
      },
    );
  }

  void _showEditGroupDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Edit Group'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Group Name',
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                ),
                maxLines: 3,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                final name = _nameController.text.trim();
                final description = _descriptionController.text.trim();

                if (name.isEmpty) {
                  Get.snackbar(
                    'Error',
                    'Group name cannot be empty',
                    snackPosition: SnackPosition.BOTTOM,
                  );
                  return;
                }

                // Close dialog first
                Navigator.pop(context);

                // Then update group info
                _messageController.updateGroupInfo(
                  widget.chatGroup.id,
                  name,
                  description.isEmpty ? null : description,
                ).then((success) {
                  if (success) {
                    Get.snackbar(
                      'Success',
                      'Group information updated',
                      snackPosition: SnackPosition.BOTTOM,
                    );
                  } else {
                    Get.snackbar(
                      'Error',
                      'Failed to update group: ${_messageController.error.value}',
                      snackPosition: SnackPosition.BOTTOM,
                    );
                  }
                });
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }
}
