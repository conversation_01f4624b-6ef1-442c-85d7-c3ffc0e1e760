import 'dart:async';
import 'package:flutter/foundation.dart';

/// فئة لإدارة الوصول المتزامن إلى قاعدة البيانات
///
/// تستخدم لضمان عدم حدوث تضارب في العمليات المتزامنة على قاعدة البيانات
class DatabaseMutex {
  static final DatabaseMutex _instance = DatabaseMutex._internal();
  final Map<String, Completer<void>> _locks = {};

  factory DatabaseMutex() => _instance;

  DatabaseMutex._internal();

  /// الحصول على قفل لمورد معين
  ///
  /// [resourceName] اسم المورد المراد قفله
  ///
  /// يعيد Future يتم حله عندما يصبح المورد متاحًا
  Future<void> acquire(String resourceName) async {
    debugPrint('طلب قفل: $resourceName');
    
    // انتظار حتى يصبح المورد متاحًا
    while (_locks.containsKey(resourceName)) {
      await _locks[resourceName]!.future;
    }
    
    // إنشاء قفل جديد
    _locks[resourceName] = Completer<void>();
    debugPrint('تم الحصول على قفل: $resourceName');
  }

  /// تحرير قفل لمورد معين
  ///
  /// [resourceName] اسم المورد المراد تحرير قفله
  void release(String resourceName) {
    debugPrint('تحرير قفل: $resourceName');
    
    if (_locks.containsKey(resourceName)) {
      // إكمال المستقبل وإزالة القفل
      _locks[resourceName]!.complete();
      _locks.remove(resourceName);
    }
  }

  /// تنفيذ عملية مع قفل
  ///
  /// [resourceName] اسم المورد المراد قفله
  /// [operation] العملية المراد تنفيذها
  ///
  /// يعيد نتيجة العملية
  Future<T> withLock<T>(String resourceName, Future<T> Function() operation) async {
    try {
      // الحصول على القفل
      await acquire(resourceName);
      
      // تنفيذ العملية
      return await operation();
    } finally {
      // تحرير القفل بغض النظر عن نتيجة العملية
      release(resourceName);
    }
  }
}
