import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../controllers/calendar_controller.dart' as app_calendar;
import '../../controllers/task_controller.dart';
import '../../models/calendar_event_model.dart';
import '../../models/task_model.dart';
import '../../models/task_status_enum.dart';
import '../../constants/app_colors.dart';
import 'calendar_event_form.dart';

/// شاشة تفاصيل حدث التقويم
class CalendarEventDetails extends StatefulWidget {
  /// الحدث المراد عرض تفاصيله
  final CalendarEvent event;

  /// دالة يتم استدعاؤها عند تحديث الحدث
  final VoidCallback? onEventUpdated;

  /// دالة يتم استدعاؤها عند حذف الحدث
  final VoidCallback? onEventDeleted;

  const CalendarEventDetails({
    super.key,
    required this.event,
    this.onEventUpdated,
    this.onEventDeleted,
  });

  @override
  State<CalendarEventDetails> createState() => _CalendarEventDetailsState();
}

class _CalendarEventDetailsState extends State<CalendarEventDetails> {
  final app_calendar.CalendarController _calendarController = Get.find<app_calendar.CalendarController>();
  final TaskController _taskController = Get.find<TaskController>();

  Task? _relatedTask;
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadRelatedTask();
  }

  // تحميل المهمة المرتبطة بالحدث
  Future<void> _loadRelatedTask() async {
    if (widget.event.taskId != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        // تحميل تفاصيل المهمة
        await _taskController.loadTaskDetails(widget.event.taskId!);

        // الحصول على المهمة الحالية من وحدة التحكم
        final task = _taskController.currentTask.value;

        setState(() {
          _relatedTask = task;
          _isLoading = false;
        });
      } catch (e) {
        setState(() {
          _errorMessage = 'حدث خطأ أثناء تحميل المهمة المرتبطة: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الحدث'),
        actions: [
          // زر التعديل
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: 'تعديل الحدث',
            onPressed: _editEvent,
          ),
          // زر الحذف
          IconButton(
            icon: const Icon(Icons.delete),
            tooltip: 'حذف الحدث',
            onPressed: _confirmDeleteEvent,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildEventDetails(),
    );
  }

  // بناء تفاصيل الحدث
  Widget _buildEventDetails() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الحدث
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        backgroundColor: widget.event.color ?? _getEventTypeColor(widget.event.eventType),
                        child: Icon(
                          _getEventTypeIcon(widget.event.eventType),
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.event.title,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _calendarController.getEventTypeName(widget.event.eventType),
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  if (widget.event.status != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Chip(
                        label: Text(
                          _calendarController.getEventStatusName(widget.event.status),
                          style: const TextStyle(color: Colors.white),
                        ),
                        backgroundColor: _getStatusColor(widget.event.status),
                      ),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // وقت الحدث
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'الوقت',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ListTile(
                    leading: const Icon(Icons.access_time),
                    title: const Text('وقت البداية'),
                    subtitle: Text(
                      DateFormat('yyyy-MM-dd HH:mm').format(widget.event.startTime),
                    ),
                  ),
                  ListTile(
                    leading: const Icon(Icons.access_time),
                    title: const Text('وقت النهاية'),
                    subtitle: Text(
                      DateFormat('yyyy-MM-dd HH:mm').format(widget.event.endTime),
                    ),
                  ),
                  ListTile(
                    leading: const Icon(Icons.timelapse),
                    title: const Text('المدة'),
                    subtitle: Text(
                      _formatDuration(widget.event.endTime.difference(widget.event.startTime)),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // وصف الحدث
          if (widget.event.description != null && widget.event.description!.isNotEmpty)
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الوصف',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.event.description!,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
          if (widget.event.description != null && widget.event.description!.isNotEmpty)
            const SizedBox(height: 16),

          // معلومات التكرار (إذا كان الحدث متكررًا)
          if (widget.event.recurrencePattern != EventRecurrencePattern.none)
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معلومات التكرار',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ListTile(
                      leading: const Icon(Icons.repeat),
                      title: const Text('نمط التكرار'),
                      subtitle: Text(_getRecurrencePatternName(widget.event.recurrencePattern)),
                    ),
                    if (widget.event.recurrenceCount > 0)
                      ListTile(
                        leading: const Icon(Icons.repeat_one),
                        title: const Text('عدد مرات التكرار'),
                        subtitle: Text(
                          widget.event.recurrenceCount == -1
                              ? 'غير محدود'
                              : '${widget.event.recurrenceCount} مرات',
                        ),
                      ),
                    if (widget.event.recurrenceEndDate != null)
                      ListTile(
                        leading: const Icon(Icons.event_busy),
                        title: const Text('تاريخ انتهاء التكرار'),
                        subtitle: Text(
                          DateFormat('yyyy-MM-dd').format(widget.event.recurrenceEndDate!),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          if (widget.event.recurrencePattern != EventRecurrencePattern.none)
            const SizedBox(height: 16),

          // معلومات التنبيه (إذا كان التنبيه مفعلاً)
          if (widget.event.isReminderEnabled)
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معلومات التنبيه',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ListTile(
                      leading: const Icon(Icons.alarm),
                      title: const Text('وقت التنبيه'),
                      subtitle: Text(_getReminderTimeName(widget.event.reminderTime)),
                    ),
                  ],
                ),
              ),
            ),
          if (widget.event.isReminderEnabled)
            const SizedBox(height: 16),

          // المهمة المرتبطة
          if (widget.event.taskId != null)
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'المهمة المرتبطة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    if (_relatedTask != null)
                      ListTile(
                        leading: Icon(
                          Icons.task,
                          color: _getTaskStatusColor(_relatedTask!.status),
                        ),
                        title: Text(_relatedTask!.title),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 4),
                            Text('الحالة: ${_getTaskStatusName(_relatedTask!.status)}'),
                            if (_relatedTask!.dueDate != null)
                              Text('تاريخ الاستحقاق: ${DateFormat('yyyy-MM-dd').format(_relatedTask!.dueDate!)}'),
                          ],
                        ),
                        onTap: () => _openTaskDetails(_relatedTask!.id),
                      )
                    else if (_isLoading)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(8.0),
                          child: CircularProgressIndicator(),
                        ),
                      )
                    else
                      const ListTile(
                        leading: Icon(Icons.error_outline, color: Colors.red),
                        title: Text('لم يتم العثور على المهمة'),
                        subtitle: Text('قد تكون المهمة محذوفة أو غير متاحة'),
                      ),
                  ],
                ),
              ),
            ),

          // رسالة الخطأ
          if (_errorMessage.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.all(8),
              color: Colors.red.shade100,
              width: double.infinity,
              child: Text(
                _errorMessage,
                style: TextStyle(color: Colors.red.shade900),
              ),
            ),
        ],
      ),
    );
  }

  // تعديل الحدث
  void _editEvent() {
    Get.to(() => CalendarEventForm(
      event: widget.event,
      onEventUpdated: (updatedEvent) {
        if (widget.onEventUpdated != null) {
          widget.onEventUpdated!();
        }
      },
    ));
  }

  // تأكيد حذف الحدث
  void _confirmDeleteEvent() {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا الحدث؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: _deleteEvent,
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  // حذف الحدث
  Future<void> _deleteEvent() async {
    Get.back(); // إغلاق مربع الحوار

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _calendarController.deleteEvent(widget.event.id);

      if (success) {
        Get.back(); // العودة إلى الشاشة السابقة

        Get.snackbar(
          'تم الحذف',
          'تم حذف الحدث بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        if (widget.onEventDeleted != null) {
          widget.onEventDeleted!();
        }
      } else {
        setState(() {
          _errorMessage = 'حدث خطأ أثناء حذف الحدث';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ: $e';
        _isLoading = false;
      });
    }
  }

  // فتح تفاصيل المهمة
  void _openTaskDetails(String taskId) {
    // هنا يمكن إضافة كود لفتح تفاصيل المهمة
    Get.snackbar(
      'فتح المهمة',
      'سيتم فتح تفاصيل المهمة قريبًا',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // تنسيق المدة الزمنية
  String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays} يوم ${duration.inHours % 24} ساعة';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} ساعة ${duration.inMinutes % 60} دقيقة';
    } else {
      return '${duration.inMinutes} دقيقة';
    }
  }

  // الحصول على أيقونة نوع الحدث
  IconData _getEventTypeIcon(CalendarEventType type) {
    switch (type) {
      case CalendarEventType.task:
        return Icons.task;
      case CalendarEventType.meeting:
        return Icons.people;
      case CalendarEventType.reminder:
        return Icons.alarm;
      case CalendarEventType.vacation:
        return Icons.beach_access;
      case CalendarEventType.other:
        return Icons.event;
      default:
        return Icons.event;
    }
  }

  // الحصول على لون نوع الحدث
  Color _getEventTypeColor(CalendarEventType type) {
    switch (type) {
      case CalendarEventType.task:
        return AppColors.primary;
      case CalendarEventType.meeting:
        return Colors.orange;
      case CalendarEventType.reminder:
        return Colors.purple;
      case CalendarEventType.vacation:
        return Colors.green;
      case CalendarEventType.other:
        return Colors.grey;
      default:
        return AppColors.primary;
    }
  }

  // الحصول على لون حالة الحدث
  Color _getStatusColor(CalendarEventStatus? status) {
    if (status == null) return Colors.grey;

    switch (status) {
      case CalendarEventStatus.pending:
        return Colors.orange;
      case CalendarEventStatus.inProgress:
        return Colors.blue;
      case CalendarEventStatus.completed:
        return Colors.green;
      case CalendarEventStatus.cancelled:
        return Colors.red;
      case CalendarEventStatus.waiting:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  // الحصول على لون حالة المهمة
  Color _getTaskStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return Colors.orange;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.waitingForInfo:
        return Colors.purple;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.cancelled:
        return Colors.red;
      case TaskStatus.news:
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  // الحصول على اسم حالة المهمة
  String _getTaskStatusName(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return 'قيد الانتظار';
      case TaskStatus.inProgress:
        return 'قيد التنفيذ';
      case TaskStatus.waitingForInfo:
        return 'في انتظار معلومات';
      case TaskStatus.completed:
        return 'مكتملة';
      case TaskStatus.cancelled:
        return 'ملغاة';
      case TaskStatus.news:
        return 'جديدة';
      default:
        return '';
    }
  }

  // الحصول على اسم نمط التكرار
  String _getRecurrencePatternName(EventRecurrencePattern pattern) {
    switch (pattern) {
      case EventRecurrencePattern.none:
        return 'لا تكرار';
      case EventRecurrencePattern.daily:
        return 'يومي';
      case EventRecurrencePattern.weekly:
        return 'أسبوعي';
      case EventRecurrencePattern.monthly:
        return 'شهري';
      case EventRecurrencePattern.yearly:
        return 'سنوي';
      default:
        return 'لا تكرار';
    }
  }

  // الحصول على اسم وقت التنبيه
  String _getReminderTimeName(EventReminderTime time) {
    switch (time) {
      case EventReminderTime.none:
        return 'لا تنبيه';
      case EventReminderTime.fiveMinutes:
        return 'قبل 5 دقائق';
      case EventReminderTime.fifteenMinutes:
        return 'قبل 15 دقيقة';
      case EventReminderTime.thirtyMinutes:
        return 'قبل 30 دقيقة';
      case EventReminderTime.oneHour:
        return 'قبل ساعة';
      case EventReminderTime.oneDay:
        return 'قبل يوم';
      default:
        return 'لا تنبيه';
    }
  }
}
