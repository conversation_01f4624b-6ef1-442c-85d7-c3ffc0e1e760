using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة تتبع تقدم المهام
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class TaskProgressTrackersController : ControllerBase
    {
        private readonly TasksDbContext _context;

        public TaskProgressTrackersController(TasksDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// الحصول على جميع متتبعات تقدم المهام
        /// </summary>
        /// <returns>قائمة بجميع متتبعات تقدم المهام</returns>
        /// <response code="200">إرجاع قائمة متتبعات التقدم</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TaskProgressTracker>>> GetTaskProgressTrackers()
        {
            return await _context.TaskProgressTrackers
                .Include(tpt => tpt.Task)
                .Include(tpt => tpt.UpdatedByNavigation)
                .OrderByDescending(tpt => tpt.UpdatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على متتبع تقدم محدد
        /// </summary>
        /// <param name="id">معرف متتبع التقدم</param>
        /// <returns>متتبع التقدم المطلوب</returns>
        /// <response code="200">إرجاع متتبع التقدم</response>
        /// <response code="404">متتبع التقدم غير موجود</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<TaskProgressTracker>> GetTaskProgressTracker(int id)
        {
            var taskProgressTracker = await _context.TaskProgressTrackers
                .Include(tpt => tpt.Task)
                .Include(tpt => tpt.UpdatedByNavigation)
                .FirstOrDefaultAsync(tpt => tpt.Id == id);

            if (taskProgressTracker == null)
            {
                return NotFound();
            }

            return taskProgressTracker;
        }

        /// <summary>
        /// الحصول على متتبعات تقدم مهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <returns>قائمة متتبعات تقدم المهمة</returns>
        /// <response code="200">إرجاع قائمة متتبعات التقدم</response>
        [HttpGet("task/{taskId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TaskProgressTracker>>> GetTaskProgressTrackersByTask(int taskId)
        {
            return await _context.TaskProgressTrackers
                .Include(tpt => tpt.UpdatedByNavigation)
                .Where(tpt => tpt.TaskId == taskId)
                .OrderByDescending(tpt => tpt.UpdatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على أحدث تقدم لمهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <returns>أحدث تقدم للمهمة</returns>
        /// <response code="200">إرجاع أحدث تقدم</response>
        /// <response code="404">لا يوجد تقدم مسجل للمهمة</response>
        [HttpGet("task/{taskId}/latest")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<TaskProgressTracker>> GetLatestTaskProgress(int taskId)
        {
            var latestProgress = await _context.TaskProgressTrackers
                .Include(tpt => tpt.UpdatedByNavigation)
                .Where(tpt => tpt.TaskId == taskId)
                .OrderByDescending(tpt => tpt.UpdatedAt)
                .FirstOrDefaultAsync();

            if (latestProgress == null)
            {
                return NotFound("لا يوجد تقدم مسجل لهذه المهمة");
            }

            return latestProgress;
        }

        /// <summary>
        /// الحصول على متتبعات التقدم لمستخدم محدد
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة متتبعات التقدم للمستخدم</returns>
        /// <response code="200">إرجاع قائمة متتبعات التقدم</response>
        [HttpGet("user/{userId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TaskProgressTracker>>> GetTaskProgressTrackersByUser(int userId)
        {
            return await _context.TaskProgressTrackers
                .Include(tpt => tpt.Task)
                .Where(tpt => tpt.UpdatedBy == userId)
                .OrderByDescending(tpt => tpt.UpdatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على متتبعات التقدم في فترة زمنية محددة
        /// </summary>
        /// <param name="startDate">تاريخ البداية (Unix timestamp)</param>
        /// <param name="endDate">تاريخ النهاية (Unix timestamp)</param>
        /// <returns>قائمة متتبعات التقدم في الفترة المحددة</returns>
        /// <response code="200">إرجاع قائمة متتبعات التقدم</response>
        [HttpGet("date-range")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<TaskProgressTracker>>> GetTaskProgressTrackersByDateRange(
            [FromQuery] long startDate, 
            [FromQuery] long endDate)
        {
            return await _context.TaskProgressTrackers
                .Include(tpt => tpt.Task)
                .Include(tpt => tpt.UpdatedByNavigation)
                .Where(tpt => tpt.UpdatedAt >= startDate && tpt.UpdatedAt <= endDate)
                .OrderByDescending(tpt => tpt.UpdatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// إحصائيات تقدم المهام
        /// </summary>
        /// <returns>إحصائيات التقدم</returns>
        /// <response code="200">إرجاع إحصائيات التقدم</response>
        [HttpGet("statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<object>> GetProgressStatistics()
        {
            var totalTrackers = await _context.TaskProgressTrackers.CountAsync();
            
            var averageProgress = await _context.TaskProgressTrackers
                .AverageAsync(tpt => (double?)tpt.ProgressPercentage) ?? 0;

            var progressDistribution = await _context.TaskProgressTrackers
                .GroupBy(tpt => tpt.ProgressPercentage / 10 * 10) // تجميع بفئات 10%
                .Select(g => new { Range = $"{g.Key}-{g.Key + 9}%", Count = g.Count() })
                .OrderBy(x => x.Range)
                .ToListAsync();

            var today = DateTimeOffset.UtcNow.Date;
            var todayStart = new DateTimeOffset(today).ToUnixTimeSeconds();
            var todayEnd = new DateTimeOffset(today.AddDays(1)).ToUnixTimeSeconds() - 1;

            var todayUpdates = await _context.TaskProgressTrackers
                .CountAsync(tpt => tpt.UpdatedAt >= todayStart && tpt.UpdatedAt <= todayEnd);

            var completedTasks = await _context.TaskProgressTrackers
                .CountAsync(tpt => tpt.ProgressPercentage == 100);

            return Ok(new
            {
                TotalTrackers = totalTrackers,
                AverageProgress = Math.Round(averageProgress, 2),
                TodayUpdates = todayUpdates,
                CompletedTasks = completedTasks,
                ProgressDistribution = progressDistribution
            });
        }

        /// <summary>
        /// إنشاء متتبع تقدم جديد
        /// </summary>
        /// <param name="taskProgressTracker">بيانات متتبع التقدم</param>
        /// <returns>متتبع التقدم المُنشأ</returns>
        /// <response code="201">تم إنشاء متتبع التقدم بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<TaskProgressTracker>> PostTaskProgressTracker(TaskProgressTracker taskProgressTracker)
        {
            // التحقق من وجود المهمة
            var taskExists = await _context.Tasks.AnyAsync(t => t.Id == taskProgressTracker.TaskId && !t.IsDeleted);
            if (!taskExists)
            {
                return BadRequest("المهمة غير موجودة");
            }

            // التحقق من صحة نسبة التقدم
            if (taskProgressTracker.ProgressPercentage < 0 || taskProgressTracker.ProgressPercentage > 100)
            {
                return BadRequest("نسبة التقدم يجب أن تكون بين 0 و 100");
            }

            taskProgressTracker.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

            _context.TaskProgressTrackers.Add(taskProgressTracker);
            await _context.SaveChangesAsync();

            // تحديث نسبة التقدم في المهمة نفسها
            var task = await _context.Tasks.FindAsync(taskProgressTracker.TaskId);
            if (task != null)
            {
                task.CompletionPercentage = (int)taskProgressTracker.ProgressPercentage;
                await _context.SaveChangesAsync();
            }

            return CreatedAtAction("GetTaskProgressTracker", new { id = taskProgressTracker.Id }, taskProgressTracker);
        }

        /// <summary>
        /// تحديث تقدم مهمة سريع
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <param name="progressPercentage">نسبة التقدم</param>
        /// <param name="updatedBy">معرف المستخدم الذي حدث التقدم</param>
        /// <param name="notes">ملاحظات (اختياري)</param>
        /// <returns>متتبع التقدم المُنشأ</returns>
        /// <response code="201">تم تحديث التقدم بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost("update-progress")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<TaskProgressTracker>> UpdateTaskProgress(
            [FromQuery] int taskId,
            [FromQuery] int progressPercentage,
            [FromQuery] int updatedBy,
            [FromQuery] string? notes = null)
        {
            // التحقق من وجود المهمة
            var taskExists = await _context.Tasks.AnyAsync(t => t.Id == taskId && !t.IsDeleted);
            if (!taskExists)
            {
                return BadRequest("المهمة غير موجودة");
            }

            // التحقق من صحة نسبة التقدم
            if (progressPercentage < 0 || progressPercentage > 100)
            {
                return BadRequest("نسبة التقدم يجب أن تكون بين 0 و 100");
            }

            var taskProgressTracker = new TaskProgressTracker
            {
                TaskId = taskId,
                ProgressPercentage = progressPercentage,
                UpdatedBy = updatedBy,
                Notes = notes,
                UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };

            _context.TaskProgressTrackers.Add(taskProgressTracker);
            await _context.SaveChangesAsync();

            // تحديث نسبة التقدم في المهمة نفسها
            var task = await _context.Tasks.FindAsync(taskId);
            if (task != null)
            {
                task.CompletionPercentage = (int)progressPercentage;
                
                // إذا وصلت النسبة إلى 100%، تحديث تاريخ الإكمال
                if (progressPercentage == 100 && !task.CompletedAt.HasValue)
                {
                    task.CompletedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                }
                
                await _context.SaveChangesAsync();
            }

            return CreatedAtAction("GetTaskProgressTracker", new { id = taskProgressTracker.Id }, taskProgressTracker);
        }

        /// <summary>
        /// تحديث متتبع تقدم
        /// </summary>
        /// <param name="id">معرف متتبع التقدم</param>
        /// <param name="taskProgressTracker">بيانات متتبع التقدم المحدثة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث متتبع التقدم بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">متتبع التقدم غير موجود</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutTaskProgressTracker(int id, TaskProgressTracker taskProgressTracker)
        {
            if (id != taskProgressTracker.Id)
            {
                return BadRequest();
            }

            // التحقق من صحة نسبة التقدم
            if (taskProgressTracker.ProgressPercentage < 0 || taskProgressTracker.ProgressPercentage > 100)
            {
                return BadRequest("نسبة التقدم يجب أن تكون بين 0 و 100");
            }

            _context.Entry(taskProgressTracker).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!TaskProgressTrackerExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        /// <summary>
        /// حذف متتبع تقدم
        /// </summary>
        /// <param name="id">معرف متتبع التقدم</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف متتبع التقدم بنجاح</response>
        /// <response code="404">متتبع التقدم غير موجود</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteTaskProgressTracker(int id)
        {
            var taskProgressTracker = await _context.TaskProgressTrackers.FindAsync(id);
            if (taskProgressTracker == null)
            {
                return NotFound();
            }

            _context.TaskProgressTrackers.Remove(taskProgressTracker);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        /// <summary>
        /// حذف جميع متتبعات تقدم مهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        /// <returns>عدد المتتبعات المحذوفة</returns>
        /// <response code="200">تم حذف متتبعات التقدم بنجاح</response>
        [HttpDelete("task/{taskId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<int>> DeleteTaskProgressTrackersByTask(int taskId)
        {
            var trackers = await _context.TaskProgressTrackers
                .Where(tpt => tpt.TaskId == taskId)
                .ToListAsync();

            var deletedCount = trackers.Count;
            _context.TaskProgressTrackers.RemoveRange(trackers);
            await _context.SaveChangesAsync();

            return deletedCount;
        }

        private bool TaskProgressTrackerExists(int id)
        {
            return _context.TaskProgressTrackers.Any(e => e.Id == id);
        }
    }
}
