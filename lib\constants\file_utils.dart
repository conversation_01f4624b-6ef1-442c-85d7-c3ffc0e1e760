import 'package:flutter/foundation.dart';

/// أدوات التعامل مع الملفات
///
/// توفر هذه الفئة مجموعة من الدوال المساعدة للتعامل مع الملفات
class FileUtils {
  /// الحصول على نوع الملف من امتداده
  ///
  /// [extension] امتداد الملف
  ///
  /// يعيد نوع الملف (صورة، فيديو، صوت، مستند، إلخ)
  static String getFileTypeFromExtension(String extension) {
    final String ext = extension.toLowerCase();

    // صور
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg'].contains(ext)) {
      return 'image';
    }
    
    // فيديو
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', '3gp'].contains(ext)) {
      return 'video';
    }
    
    // صوت
    if (['mp3', 'wav', 'ogg', 'aac', 'wma', 'm4a', 'flac'].contains(ext)) {
      return 'audio';
    }
    
    // مستندات
    if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'odt', 'ods', 'odp'].contains(ext)) {
      return 'document';
    }
    
    // ملفات مضغوطة
    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].contains(ext)) {
      return 'archive';
    }
    
    // ملفات أخرى
    return 'other';
  }

  /// الحصول على أيقونة الملف بناءً على نوعه
  ///
  /// [fileType] نوع الملف
  ///
  /// يعيد اسم الأيقونة المناسبة لنوع الملف
  static String getFileIcon(String fileType) {
    switch (fileType) {
      case 'image':
        return 'assets/icons/image_file.png';
      case 'video':
        return 'assets/icons/video_file.png';
      case 'audio':
        return 'assets/icons/audio_file.png';
      case 'document':
        return 'assets/icons/document_file.png';
      case 'archive':
        return 'assets/icons/archive_file.png';
      default:
        return 'assets/icons/unknown_file.png';
    }
  }

  /// تحويل حجم الملف إلى صيغة مقروءة
  ///
  /// [bytes] حجم الملف بالبايت
  ///
  /// يعيد حجم الملف بصيغة مقروءة (مثل 1.5 MB)
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }

  /// التحقق من صلاحية امتداد الملف
  ///
  /// [extension] امتداد الملف
  /// [allowedExtensions] قائمة الامتدادات المسموح بها
  ///
  /// يعيد true إذا كان الامتداد مسموحًا به، وfalse خلاف ذلك
  static bool isValidExtension(String extension, List<String> allowedExtensions) {
    if (allowedExtensions.isEmpty) {
      return true;
    }
    
    return allowedExtensions.contains(extension.toLowerCase());
  }

  /// الحصول على اسم الملف بدون امتداد
  ///
  /// [fileName] اسم الملف
  ///
  /// يعيد اسم الملف بدون امتداد
  static String getFileNameWithoutExtension(String fileName) {
    if (fileName.contains('.')) {
      return fileName.substring(0, fileName.lastIndexOf('.'));
    }
    
    return fileName;
  }

  /// الحصول على امتداد الملف من اسمه
  ///
  /// [fileName] اسم الملف
  ///
  /// يعيد امتداد الملف
  static String getExtensionFromFileName(String fileName) {
    if (fileName.contains('.')) {
      return fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
    }
    
    return '';
  }

  /// طباعة معلومات الملف للتصحيح
  ///
  /// [filePath] مسار الملف
  /// [fileSize] حجم الملف
  static void debugPrintFileInfo(String filePath, int fileSize) {
    if (kDebugMode) {
      final fileName = filePath.split('/').last;
      final extension = getExtensionFromFileName(fileName);
      final fileType = getFileTypeFromExtension(extension);
      final formattedSize = formatFileSize(fileSize);
      
      print('File Info:');
      print('- Path: $filePath');
      print('- Name: $fileName');
      print('- Extension: $extension');
      print('- Type: $fileType');
      print('- Size: $formattedSize');
    }
  }
}
