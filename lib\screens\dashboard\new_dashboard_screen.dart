import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

import '../../models/dashboard_model.dart';
import '../../controllers/dashboard_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/department_controller.dart';
import '../../widgets/app_drawer.dart';
import '../../widgets/common/loading_indicator.dart';
import '../../widgets/common/empty_state_widget.dart';
import '../../constants/app_styles.dart';
import '../../widgets/dashboard/modern_dashboard_widget.dart';
import '../../widgets/dashboard/edit_widget_dialog.dart';

/// شاشة لوحة المعلومات الجديدة
///
/// تعرض لوحة معلومات مخصصة بتصميم حديث وبسيط
class NewDashboardScreen extends StatefulWidget {
  const NewDashboardScreen({super.key});

  @override
  State<NewDashboardScreen> createState() => _NewDashboardScreenState();
}

class _NewDashboardScreenState extends State<NewDashboardScreen> {
  final DashboardController _dashboardController = Get.find<DashboardController>();
  final AuthController _authController = Get.find<AuthController>();
  final TaskController _taskController = Get.find<TaskController>();
  final UserController _userController = Get.find<UserController>();
  final DepartmentController _departmentController = Get.find<DepartmentController>();

  bool _isLoading = true;
  String? _errorMessage;
  Dashboard? _currentDashboard;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _loadDashboards();
  }

  /// تحميل لوحات المعلومات
  Future<void> _loadDashboards() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // تحميل البيانات الأساسية
      await _taskController.loadAllTasks();
      await _userController.loadAllUsers();
      await _departmentController.loadDepartments();

      // تحميل لوحات المعلومات
      final userId = _authController.currentUser.value?.id;
      if (userId == null) {
        throw Exception('لم يتم تسجيل الدخول');
      }

      final dashboards = await _dashboardController.getDashboardsByUserId(userId);

      // البحث عن لوحة المعلومات الافتراضية
      Dashboard? defaultDashboard = dashboards.firstWhereOrNull((d) => d.isDefault);

      // إذا لم يتم العثور على لوحة معلومات افتراضية، استخدم الأولى أو أنشئ واحدة جديدة
      if (defaultDashboard == null) {
        if (dashboards.isNotEmpty) {
          defaultDashboard = dashboards.first;
        } else {
          // إنشاء لوحة معلومات افتراضية
          defaultDashboard = await _createDefaultDashboard(userId);
          dashboards.add(defaultDashboard);
        }
      }

      setState(() {
        _currentDashboard = defaultDashboard;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل لوحات المعلومات: $e';
        _isLoading = false;
      });
    }
  }

  /// إنشاء لوحة معلومات افتراضية
  Future<Dashboard> _createDefaultDashboard(String userId) async {
    final dashboard = Dashboard(
      id: const Uuid().v4(),
      title: 'لوحة المعلومات الرئيسية',
      description: 'لوحة المعلومات الافتراضية',
      ownerId: userId,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      widgets: _getDefaultWidgets(),
      isDefault: true,
      gridRows: 12,
      gridColumns: 12,
    );

    await _dashboardController.saveDashboard(dashboard);
    return dashboard;
  }

  /// الحصول على العناصر الافتراضية للوحة المعلومات
  List<DashboardWidget> _getDefaultWidgets() {
    return [
      DashboardWidget(
        id: const Uuid().v4(),
        title: 'توزيع المهام حسب الحالة',
        type: DashboardWidgetType.taskStatusChart,
        settings: '{"chartType":"pie","showLegend":true,"showValues":true,"showPercentages":true}',
        rowIndex: 0,
        columnIndex: 0,
        width: 6,
        height: 4,
        isExpandable: true,
      ),
      DashboardWidget(
        id: const Uuid().v4(),
        title: 'أداء المستخدمين',
        type: DashboardWidgetType.userPerformanceChart,
        settings: '{"chartType":"bar","showGrid":true,"showValues":true,"maxUsers":5,"sortBy":"completed"}',
        rowIndex: 0,
        columnIndex: 6,
        width: 6,
        height: 4,
        isExpandable: true,
      ),
      DashboardWidget(
        id: const Uuid().v4(),
        title: 'تقدم المهام على مدار الوقت',
        type: DashboardWidgetType.taskProgressChart,
        settings: '{"chartType":"line","showGrid":true,"showDots":true,"showBelowArea":true,"timeRange":"month"}',
        rowIndex: 4,
        columnIndex: 0,
        width: 12,
        height: 4,
        isExpandable: true,
      ),
      DashboardWidget(
        id: const Uuid().v4(),
        title: 'أداء الأقسام',
        type: DashboardWidgetType.departmentPerformanceChart,
        settings: '{"chartType":"bar","showGrid":true,"showValues":true,"sortBy":"completed"}',
        rowIndex: 8,
        columnIndex: 0,
        width: 12,
        height: 4,
        isExpandable: true,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة المعلومات الجديدة'),
        actions: [
          // زر التحديث
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
            onPressed: _loadDashboards,
          ),
          // زر التعديل
          IconButton(
            icon: Icon(_isEditing ? Icons.check : Icons.edit),
            tooltip: _isEditing ? 'حفظ التغييرات' : 'تعديل لوحة المعلومات',
            onPressed: () {
              setState(() {
                _isEditing = !_isEditing;
              });
            },
          ),
        ],
      ),
      drawer: const AppDrawer(),
      floatingActionButton: _isEditing ? FloatingActionButton(
        onPressed: _showAddWidgetDialog,
        tooltip: 'إضافة عنصر جديد',
        child: const Icon(Icons.add),
      ) : null,
      body: _buildBody(),
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: LoadingIndicator(message: 'جاري تحميل لوحة المعلومات...'),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: EmptyStateWidget(
          icon: Icons.error_outline,
          title: 'حدث خطأ',
          message: _errorMessage!,
          buttonText: 'إعادة المحاولة',
          onButtonPressed: _loadDashboards,
        ),
      );
    }

    if (_currentDashboard == null) {
      return Center(
        child: EmptyStateWidget(
          icon: Icons.dashboard_customize,
          title: 'لا توجد لوحة معلومات',
          message: 'لم يتم العثور على لوحة معلومات. يمكنك إنشاء لوحة معلومات جديدة.',
          buttonText: 'إنشاء لوحة معلومات',
          onButtonPressed: () => _createDefaultDashboard(_authController.currentUser.value!.id),
        ),
      );
    }

    return _buildDashboardContent();
  }

  /// بناء محتوى لوحة المعلومات
  Widget _buildDashboardContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان لوحة المعلومات
          Text(
            _currentDashboard!.title,
            style: AppStyles.headingLarge,
          ),
          if (_currentDashboard!.description != null) ...[
            const SizedBox(height: 8),
            Text(
              _currentDashboard!.description!,
              style: AppStyles.bodyMedium,
            ),
          ],
          const SizedBox(height: 24),

          // عناصر لوحة المعلومات
          _buildDashboardWidgets(),
        ],
      ),
    );
  }

  /// بناء عناصر لوحة المعلومات
  Widget _buildDashboardWidgets() {
    if (_currentDashboard!.widgets.isEmpty) {
      return Center(
        child: EmptyStateWidget(
          icon: Icons.dashboard_customize,
          title: 'لا توجد عناصر',
          message: 'لوحة المعلومات فارغة. يمكنك إضافة عناصر جديدة.',
          buttonText: 'إضافة عنصر',
          onButtonPressed: _showAddWidgetDialog,
        ),
      );
    }

    // إنشاء شبكة من العناصر
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: _currentDashboard!.widgets.length,
      itemBuilder: (context, index) {
        final widget = _currentDashboard!.widgets[index];
        return ModernDashboardWidget(
          widget: widget,
          isEditing: _isEditing,
          onDelete: () => _deleteWidget(widget),
          onEdit: () => _editWidget(widget),
          onMove: (newPosition) => _moveWidget(widget, newPosition),
          onResize: (newSize) => _resizeWidget(widget, newSize),
        );
      },
    );
  }

  /// حذف عنصر
  void _deleteWidget(DashboardWidget widget) {
    if (_currentDashboard == null) return;

    // عرض مربع حوار للتأكيد
    Get.dialog(
      AlertDialog(
        title: const Text('حذف العنصر'),
        content: const Text('هل أنت متأكد من حذف هذا العنصر؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();

              // حذف العنصر من لوحة المعلومات
              final updatedWidgets = List<DashboardWidget>.from(_currentDashboard!.widgets)
                ..removeWhere((w) => w.id == widget.id);

              final updatedDashboard = _currentDashboard!.copyWith(
                widgets: updatedWidgets,
                updatedAt: DateTime.now(),
              );

              // حفظ التغييرات
              _dashboardController.saveDashboard(updatedDashboard);

              // تحديث واجهة المستخدم
              setState(() {
                _currentDashboard = updatedDashboard;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// تعديل عنصر
  void _editWidget(DashboardWidget widget) {
    if (_currentDashboard == null) return;

    // عرض حوار تعديل العنصر
    Get.dialog(
      EditWidgetDialog(widget: widget),
    ).then((updatedWidget) {
      if (updatedWidget != null) {
        // تحديث العنصر في لوحة المعلومات
        final updatedWidgets = List<DashboardWidget>.from(_currentDashboard!.widgets);
        final index = updatedWidgets.indexWhere((w) => w.id == widget.id);

        if (index != -1) {
          updatedWidgets[index] = updatedWidget;

          final updatedDashboard = _currentDashboard!.copyWith(
            widgets: updatedWidgets,
            updatedAt: DateTime.now(),
          );

          // حفظ التغييرات
          _dashboardController.saveDashboard(updatedDashboard);

          // تحديث واجهة المستخدم
          setState(() {
            _currentDashboard = updatedDashboard;
          });
        }
      }
    });
  }

  /// نقل عنصر
  void _moveWidget(DashboardWidget widget, Offset newPosition) {
    // سيتم تنفيذ هذه الميزة لاحقاً
  }

  /// تغيير حجم عنصر
  void _resizeWidget(DashboardWidget widget, Size newSize) {
    // سيتم تنفيذ هذه الميزة لاحقاً
  }

  /// عرض مربع حوار إضافة عنصر جديد
  void _showAddWidgetDialog() {
    if (_currentDashboard == null) return;

    Get.dialog(
      AlertDialog(
        title: const Text('إضافة عنصر جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('اختر نوع العنصر:'),
            const SizedBox(height: 16),
            _buildWidgetTypeButton(
              title: 'مخطط حالة المهام',
              icon: Icons.pie_chart,
              color: Colors.blue,
              onTap: () => _createWidget(DashboardWidgetType.taskStatusChart),
            ),
            const SizedBox(height: 8),
            _buildWidgetTypeButton(
              title: 'مخطط تقدم المهام',
              icon: Icons.show_chart,
              color: Colors.green,
              onTap: () => _createWidget(DashboardWidgetType.taskProgressChart),
            ),
            const SizedBox(height: 8),
            _buildWidgetTypeButton(
              title: 'مخطط أداء المستخدمين',
              icon: Icons.people,
              color: Colors.orange,
              onTap: () => _createWidget(DashboardWidgetType.userPerformanceChart),
            ),
            const SizedBox(height: 8),
            _buildWidgetTypeButton(
              title: 'مخطط أداء الأقسام',
              icon: Icons.business,
              color: Colors.purple,
              onTap: () => _createWidget(DashboardWidgetType.departmentPerformanceChart),
            ),
            const SizedBox(height: 8),
            _buildWidgetTypeButton(
              title: 'عدادات المهام',
              icon: Icons.numbers,
              color: Colors.teal,
              onTap: () => _createWidget(DashboardWidgetType.taskCounters),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// بناء زر نوع العنصر
  Widget _buildWidgetTypeButton({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color: color.withAlpha(128),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[600],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// إنشاء عنصر جديد
  void _createWidget(DashboardWidgetType type) {
    if (_currentDashboard == null) return;

    // إغلاق مربع الحوار
    Get.back();

    // تحديد عنوان العنصر وإعداداته
    String title;
    String settings;

    switch (type) {
      case DashboardWidgetType.taskStatusChart:
        title = 'توزيع المهام حسب الحالة';
        settings = '{"chartType":"pie","showLegend":true,"showValues":true,"showPercentages":true}';
        break;
      case DashboardWidgetType.taskProgressChart:
        title = 'تقدم المهام على مدار الوقت';
        settings = '{"chartType":"line","showGrid":true,"showDots":true,"showBelowArea":true,"timeRange":"month"}';
        break;
      case DashboardWidgetType.userPerformanceChart:
        title = 'أداء المستخدمين';
        settings = '{"chartType":"bar","showGrid":true,"showValues":true,"maxUsers":5,"sortBy":"completed"}';
        break;
      case DashboardWidgetType.departmentPerformanceChart:
        title = 'أداء الأقسام';
        settings = '{"chartType":"bar","showGrid":true,"showValues":true,"sortBy":"completed"}';
        break;
      case DashboardWidgetType.taskCounters:
        title = 'عدادات المهام';
        settings = '{"showTotal":true,"showCompleted":true,"showInProgress":true,"showDelayed":true}';
        break;
      default:
        title = 'عنصر جديد';
        settings = '{}';
        break;
    }

    // إنشاء العنصر الجديد
    final widget = DashboardWidget(
      id: const Uuid().v4(),
      title: title,
      type: type,
      settings: settings,
      rowIndex: 0,
      columnIndex: 0,
      width: 6,
      height: 4,
      isExpandable: true,
    );

    // إضافة العنصر إلى لوحة المعلومات
    final updatedWidgets = List<DashboardWidget>.from(_currentDashboard!.widgets)..add(widget);
    final updatedDashboard = _currentDashboard!.copyWith(
      widgets: updatedWidgets,
      updatedAt: DateTime.now(),
    );

    // حفظ التغييرات
    _dashboardController.saveDashboard(updatedDashboard);

    // تحديث واجهة المستخدم
    setState(() {
      _currentDashboard = updatedDashboard;
    });
  }
}
