import '../models/user_model.dart';
import '../controllers/auth_controller.dart';

/// امتدادات وحدة تحكم المستخدمين
/// تضيف دوال مفقودة مطلوبة لمكتبات Syncfusion
extension AuthControllerExtensions on AuthController {
  /// الحصول على جميع المستخدمين
  Future<List<User>> getAllUsers() async {
    try {
      // إنشاء مستودع المستخدمين
      final userRepository = UserRepository();

      // الحصول على جميع المستخدمين
      return await userRepository.getAllUsers();
    } catch (e) {
      // إرجاع قائمة فارغة في حالة حدوث خطأ
      return [];
    }
  }

  /// الحصول على مستخدم بواسطة المعرف
  Future<User?> getUserById(String userId) async {
    try {
      // التحقق مما إذا كان المستخدم الحالي هو المطلوب
      if (currentUser.value?.id == userId) {
        return currentUser.value;
      }

      // إنشاء مستودع المستخدمين
      final userRepository = UserRepository();

      // الحصول على المستخدم بواسطة المعرف
      return await userRepository.getUserById(userId);
    } catch (e) {
      // إرجاع null في حالة حدوث خطأ
      return null;
    }
  }
}
