import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/message_model.dart';
import '../models/chat_group_model.dart';
import '../models/user_model.dart';
import '../models/websocket_event.dart';
import '../services/unified_realtime_service.dart';
import '../services/message_service.dart';
import '../database/user_repository.dart';
import '../database/chat_group_repository.dart';

/// وحدة تحكم الدردشة في الوقت الحقيقي
/// @deprecated استخدم UnifiedChatController بدلاً من ذلك
/// تم استبداله بوحدة التحكم الموحدة للمحادثات
/// انظر: lib/controllers/unified_chat_controller.dart
class RealtimeChatController extends GetxController {
  final UnifiedRealtimeService _realtimeService = UnifiedRealtimeService();
  final MessageService _messageService = MessageService();
  final UserRepository _userRepository = UserRepository();
  final ChatGroupRepository _chatGroupRepository = ChatGroupRepository();

  // المستخدم الحالي
  final Rx<User?> currentUser = Rx<User?>(null);

  // المجموعة المحددة
  final Rx<ChatGroup?> selectedGroup = Rx<ChatGroup?>(null);

  // قائمة الرسائل
  final RxList<Message> messages = <Message>[].obs;

  // قائمة المستخدمين
  final RxList<User> users = <User>[].obs;

  // قائمة المجموعات
  final RxList<ChatGroup> groups = <ChatGroup>[].obs;

  // حالة الكتابة
  final RxMap<String, bool> typingUsers = <String, bool>{}.obs;

  // حالة الاتصال
  final RxBool isConnected = false.obs;

  // متغير للإشارة إلى وصول رسالة جديدة
  final RxBool newMessageReceived = false.obs;

  // مؤقت حالة الكتابة
  Timer? _typingTimer;
  bool _isTyping = false;

  @override
  void onInit() {
    super.onInit();
    // الاستماع لأحداث الوقت الحقيقي
    _realtimeService.eventStream.listen(_handleRealtimeEvent);
    // مراقبة حالة الاتصال
    ever(_realtimeService.isConnected, (connected) {
      isConnected.value = connected;
    });
  }

  /// تعيين المستخدم الحالي
  Future<void> setCurrentUser(User user) async {
    currentUser.value = user;

    // الاتصال بخادم WebSocket
    await _realtimeService.connect(user.id);

    // تحميل البيانات الأولية
    await loadInitialData();
  }

  /// تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    if (currentUser.value == null) return;

    // تحميل المستخدمين
    await loadUsers();

    // تحميل المجموعات
    await loadGroups();
  }

  /// تحميل المستخدمين
  Future<void> loadUsers() async {
    try {
      final usersList = await _userRepository.getAllUsers();
      users.assignAll(usersList);
    } catch (e) {
      debugPrint('Error loading users: $e');
    }
  }

  /// تحميل المجموعات
  Future<void> loadGroups() async {
    if (currentUser.value == null) return;

    try {
      final groupsList = await _chatGroupRepository
          .getChatGroupsForUser(currentUser.value!.id);
      groups.assignAll(groupsList);
    } catch (e) {
      debugPrint('Error loading groups: $e');
    }
  }

  /// اختيار مجموعة
  Future<void> selectGroup(ChatGroup group) async {
    selectedGroup.value = group;

    // تحميل رسائل المجموعة
    await loadMessages();

    // وضع علامة على الرسائل كمقروءة
    if (messages.isNotEmpty && currentUser.value != null) {
      final lastMessage = messages.last;
      await _messageService.markMessagesAsRead(
        group.id,
        currentUser.value!.id,
        lastMessage.id,
      );

      // إرسال إشعار بقراءة الرسالة
      _realtimeService.sendReadReceipt(lastMessage.id, group.id);
    }
  }

  /// تحميل رسائل المجموعة المحددة
  Future<void> loadMessages() async {
    if (selectedGroup.value == null) return;

    try {
      final messagesList = await _messageService.getMessagesForGroup(
        selectedGroup.value!.id,
        limit: 50,
        offset: 0,
      );

      messages.assignAll(messagesList);
    } catch (e) {
      debugPrint('Error loading messages: $e');
    }
  }

  /// إرسال رسالة
  Future<void> sendMessage(String content) async {
    debugPrint('🔍 تم استدعاء sendMessage مع المحتوى: $content');
    if (currentUser.value == null || selectedGroup.value == null) {
      debugPrint('🔍 لا يمكن إرسال الرسالة: المستخدم أو المجموعة غير محددة');
      return;
    }

    try {
      debugPrint('🔍 إرسال الرسالة إلى المجموعة: ${selectedGroup.value!.id}');
      // إرسال الرسالة
      final message = await _messageService.sendMessage(
        selectedGroup.value!.id,
        currentUser.value!.id,
        content,
      );
      debugPrint('🔍 تم إنشاء الرسالة بنجاح: ${message.id}');

      // إضافة الرسالة إلى القائمة
      messages.add(message);
      debugPrint(
          '🔍 تمت إضافة الرسالة إلى القائمة، عدد الرسائل الآن: ${messages.length}');

      // تغيير قيمة متغير الرسالة الجديدة لإطلاق حدث التمرير التلقائي
      final oldValue = newMessageReceived.value;
      newMessageReceived.toggle();
      debugPrint(
          '🔍 تم تغيير قيمة متغير الرسالة الجديدة من $oldValue إلى ${newMessageReceived.value}');

      // إرسال الرسالة عبر WebSocket
      _realtimeService.sendMessage(message);
      debugPrint('🔍 تم إرسال الرسالة عبر WebSocket');

      // إيقاف حالة الكتابة
      _stopTyping();
      debugPrint('🔍 تم إيقاف حالة الكتابة');
    } catch (e) {
      debugPrint('🔍 حدث خطأ أثناء إرسال الرسالة: $e');
    }
  }

  /// إرسال حالة الكتابة
  void sendTypingStatus(bool isTyping) {
    if (currentUser.value == null || selectedGroup.value == null) return;

    // تجنب إرسال نفس الحالة مرة أخرى
    if (_isTyping == isTyping) return;

    _isTyping = isTyping;

    // إرسال حالة الكتابة
    _realtimeService.sendTypingStatus(selectedGroup.value!.id, isTyping);

    // إذا كان المستخدم يكتب، ابدأ مؤقت لإيقاف حالة الكتابة بعد فترة
    if (isTyping) {
      _startTypingTimer();
    } else {
      _stopTypingTimer();
    }
  }

  /// بدء مؤقت حالة الكتابة
  void _startTypingTimer() {
    _stopTypingTimer();

    // إيقاف حالة الكتابة بعد 5 ثوانٍ من عدم الكتابة
    _typingTimer = Timer(const Duration(seconds: 5), _stopTyping);
  }

  /// إيقاف مؤقت حالة الكتابة
  void _stopTypingTimer() {
    _typingTimer?.cancel();
    _typingTimer = null;
  }

  /// إيقاف حالة الكتابة
  void _stopTyping() {
    if (_isTyping) {
      _isTyping = false;

      if (currentUser.value != null && selectedGroup.value != null) {
        _realtimeService.sendTypingStatus(selectedGroup.value!.id, false);
      }
    }
  }

  /// معالجة أحداث الوقت الحقيقي
  void _handleRealtimeEvent(WebSocketEvent event) {
    switch (event.type) {
      case WebSocketEventType.connect:
        // تم الاتصال بالخادم
        break;
      case WebSocketEventType.disconnect:
        // تم قطع الاتصال بالخادم
        break;
      case WebSocketEventType.message:
        _handleNewMessage(event.data);
        break;
      case WebSocketEventType.typing:
        _handleTypingStatus(event.data);
        break;
      case WebSocketEventType.readReceipt:
        _handleReadReceipt(event.data);
        break;
      case WebSocketEventType.userStatus:
        _handleUserStatus(event.data);
        break;
      case WebSocketEventType.groupUpdate:
        _handleGroupUpdate(event.data);
        break;
      case WebSocketEventType.notification:
        // يتم معالجتها في وحدة تحكم الإشعارات
        break;
      case WebSocketEventType.error:
        debugPrint('Received error event: ${event.data}');
        break;
    }
  }

  /// معالجة رسالة جديدة
  void _handleNewMessage(Map<String, dynamic> data) {
    try {
      debugPrint('🔍 تم استدعاء _handleNewMessage مع البيانات: $data');
      final message = Message.fromJson(data);
      debugPrint('🔍 تم تحويل البيانات إلى كائن Message: ${message.id}');

      // إذا كانت الرسالة تنتمي إلى المجموعة المحددة، أضفها إلى القائمة
      if (selectedGroup.value != null &&
          message.groupId == selectedGroup.value!.id) {
        debugPrint(
            '🔍 الرسالة تنتمي إلى المجموعة المحددة: ${selectedGroup.value!.id}');

        // إضافة الرسالة إلى القائمة
        messages.add(message);
        debugPrint(
            '🔍 تمت إضافة الرسالة إلى القائمة، عدد الرسائل الآن: ${messages.length}');

        // تغيير قيمة متغير الرسالة الجديدة لإطلاق حدث التمرير التلقائي
        final oldValue = newMessageReceived.value;
        newMessageReceived.toggle();
        debugPrint(
            '🔍 تم تغيير قيمة متغير الرسالة الجديدة من $oldValue إلى ${newMessageReceived.value}');

        // إطلاق حدث لتحديث الواجهة
        messages.refresh();
        debugPrint('🔍 تم استدعاء messages.refresh()');

        // إرسال إشعار بقراءة الرسالة إذا كانت من مستخدم آخر
        if (currentUser.value != null &&
            message.senderId != currentUser.value!.id) {
          debugPrint('🔍 إرسال إشعار بقراءة الرسالة: ${message.id}');
          _realtimeService.sendReadReceipt(message.id, message.groupId);

          // عرض رسالة "تم الاستلام بنجاح"
          Get.snackbar(
            '',
            '',
            titleText: const Text(
              'تم الاستلام بنجاح',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            messageText: const SizedBox.shrink(),
            backgroundColor: Colors.blue.shade600,
            colorText: Colors.white,
            snackPosition: SnackPosition.BOTTOM,
            margin: const EdgeInsets.all(8),
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            duration: const Duration(seconds: 1),
            borderRadius: 8,
            isDismissible: true,
          );
        }
      } else {
        debugPrint('🔍 الرسالة لا تنتمي إلى المجموعة المحددة');
        if (selectedGroup.value == null) {
          debugPrint('🔍 لا توجد مجموعة محددة');
        } else {
          debugPrint(
              '🔍 المجموعة المحددة: ${selectedGroup.value!.id}، مجموعة الرسالة: ${message.groupId}');
        }
      }

      // تحديث آخر رسالة في المجموعة
      final groupIndex = groups.indexWhere((g) => g.id == message.groupId);
      if (groupIndex != -1) {
        final group = groups[groupIndex];
        groups[groupIndex] = group.copyWith(
          lastMessageId: message.id,
          updatedAt: DateTime.now(),
        );
      }
    } catch (e) {
      debugPrint('Error handling new message: $e');
    }
  }

  /// معالجة حالة الكتابة
  void _handleTypingStatus(Map<String, dynamic> data) {
    try {
      final senderId = data['senderId'];
      final groupId = data['groupId'];
      final isTyping = data['isTyping'];

      // إذا كانت حالة الكتابة تنتمي إلى المجموعة المحددة، حدث حالة الكتابة
      if (selectedGroup.value != null && groupId == selectedGroup.value!.id) {
        if (isTyping) {
          typingUsers[senderId] = true;
        } else {
          typingUsers.remove(senderId);
        }
      }
    } catch (e) {
      debugPrint('Error handling typing status: $e');
    }
  }

  /// معالجة إشعار قراءة الرسالة
  void _handleReadReceipt(Map<String, dynamic> data) {
    try {
      final messageId = data['messageId'];
      final groupId = data['groupId'];

      // إذا كان إشعار القراءة ينتمي إلى المجموعة المحددة، حدث حالة القراءة
      if (selectedGroup.value != null && groupId == selectedGroup.value!.id) {
        final messageIndex = messages.indexWhere((m) => m.id == messageId);
        if (messageIndex != -1) {
          final message = messages[messageIndex];
          messages[messageIndex] = message.copyWith(isRead: true);
        }
      }
    } catch (e) {
      debugPrint('Error handling read receipt: $e');
    }
  }

  /// معالجة حالة المستخدم
  void _handleUserStatus(Map<String, dynamic> data) {
    try {
      final userId = data['userId'];
      final isOnline = data['isOnline'];

      // تحديث حالة المستخدم
      final userIndex = users.indexWhere((u) => u.id == userId);
      if (userIndex != -1) {
        final user = users[userIndex];
        users[userIndex] = user.copyWith(
          isOnline: isOnline,
          lastSeen: DateTime.now(),
        );
      }
    } catch (e) {
      debugPrint('Error handling user status: $e');
    }
  }

  /// معالجة تحديث المجموعة
  void _handleGroupUpdate(Map<String, dynamic> data) {
    try {
      final group = ChatGroup.fromJson(data);

      // تحديث المجموعة في القائمة
      final groupIndex = groups.indexWhere((g) => g.id == group.id);
      if (groupIndex != -1) {
        groups[groupIndex] = group;
      } else {
        // إضافة المجموعة إذا لم تكن موجودة
        groups.add(group);
      }

      // تحديث المجموعة المحددة إذا كانت هي المجموعة المحدثة
      if (selectedGroup.value != null && selectedGroup.value!.id == group.id) {
        selectedGroup.value = group;
      }
    } catch (e) {
      debugPrint('Error handling group update: $e');
    }
  }

  @override
  void onClose() {
    _stopTypingTimer();
    super.onClose();
  }
}
