import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/admin_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../utils/responsive_helper.dart';

/// تبويب النسخ الاحتياطية والاستعادة
///
/// يوفر واجهة لإدارة النسخ الاحتياطية واستعادة البيانات
class BackupRestoreTab extends StatefulWidget {
  const BackupRestoreTab({super.key});

  @override
  State<BackupRestoreTab> createState() => _BackupRestoreTabState();
}

class _BackupRestoreTabState extends State<BackupRestoreTab> {
  final AdminController _adminController = Get.find<AdminController>();
  final AuthController _authController = Get.find<AuthController>();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          Expanded(
            child: _buildBackupsList(),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الصفحة
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'النسخ الاحتياطية والاستعادة',
          style: AppStyles.titleLarge,
        ),
        Row(
          children: [
            ElevatedButton.icon(
              onPressed: _showCreateBackupDialog,
              icon: const Icon(Icons.backup),
              label: const Text('إنشاء نسخة احتياطية'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: _showExportDialog,
              icon: const Icon(Icons.file_download),
              label: const Text('تصدير البيانات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.accent,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء قائمة النسخ الاحتياطية
  Widget _buildBackupsList() {
    return Obx(() {
      if (_adminController.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      if (_adminController.backups.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.backup_outlined,
                size: 64,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد نسخ احتياطية',
                style: AppStyles.titleMedium,
              ),
              const SizedBox(height: 8),
              const Text(
                'قم بإنشاء نسخة احتياطية للبدء',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: _showCreateBackupDialog,
                icon: const Icon(Icons.backup),
                label: const Text('إنشاء نسخة احتياطية'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        );
      }

      return ResponsiveHelper.isDesktop(context) || ResponsiveHelper.isTablet(context)
          ? _buildBackupsDataTable()
          : _buildBackupsMobileList();
    });
  }

  /// بناء جدول النسخ الاحتياطية للشاشات الكبيرة
  Widget _buildBackupsDataTable() {
    return SingleChildScrollView(
      child: DataTable(
        columns: const [
          DataColumn(label: Text('اسم الملف')),
          DataColumn(label: Text('الحجم')),
          DataColumn(label: Text('تاريخ الإنشاء')),
          DataColumn(label: Text('النوع')),
          DataColumn(label: Text('الوصف')),
          DataColumn(label: Text('الإجراءات')),
        ],
        rows: _adminController.backups.map((backup) {
          return DataRow(
            cells: [
              DataCell(Text(backup.fileName)),
              DataCell(Text(_formatFileSize(backup.fileSize))),
              DataCell(Text(_formatDateTime(backup.createdAt))),
              DataCell(
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      backup.isAutomatic ? Icons.schedule : Icons.person,
                      color: backup.isAutomatic ? Colors.blue : Colors.green,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(backup.isAutomatic ? 'تلقائي' : 'يدوي'),
                  ],
                ),
              ),
              DataCell(Text(backup.description ?? '-')),
              DataCell(
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.restore, size: 20),
                      tooltip: 'استعادة',
                      onPressed: () => _showRestoreConfirmDialog(backup.id),
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                      tooltip: 'حذف',
                      onPressed: () => _showDeleteConfirmDialog(backup.id),
                    ),
                  ],
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// بناء قائمة النسخ الاحتياطية للشاشات الصغيرة
  Widget _buildBackupsMobileList() {
    return ListView.builder(
      itemCount: _adminController.backups.length,
      itemBuilder: (context, index) {
        final backup = _adminController.backups[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            title: Text(backup.fileName),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('الحجم: ${_formatFileSize(backup.fileSize)}'),
                Text('التاريخ: ${_formatDateTime(backup.createdAt)}'),
                Row(
                  children: [
                    Icon(
                      backup.isAutomatic ? Icons.schedule : Icons.person,
                      color: backup.isAutomatic ? Colors.blue : Colors.green,
                      size: 12,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      backup.isAutomatic ? 'تلقائي' : 'يدوي',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
                if (backup.description != null && backup.description!.isNotEmpty)
                  Text('الوصف: ${backup.description}'),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'restore':
                    _showRestoreConfirmDialog(backup.id);
                    break;
                  case 'delete':
                    _showDeleteConfirmDialog(backup.id);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem<String>(
                  value: 'restore',
                  child: Row(
                    children: [
                      Icon(Icons.restore),
                      SizedBox(width: 8),
                      Text('استعادة'),
                    ],
                  ),
                ),
                const PopupMenuItem<String>(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8),
                      Text('حذف'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// عرض حوار إنشاء نسخة احتياطية
  void _showCreateBackupDialog() {
    final descriptionController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('إنشاء نسخة احتياطية جديدة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'سيتم إنشاء نسخة احتياطية كاملة من قاعدة البيانات. يمكنك إضافة وصف اختياري للنسخة الاحتياطية.',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'الوصف (اختياري)',
                hintText: 'أدخل وصفًا للنسخة الاحتياطية',
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();

              final currentUser = _authController.currentUser.value;
              if (currentUser == null) return;

              final description = descriptionController.text.trim().isNotEmpty
                  ? descriptionController.text.trim()
                  : null;

              final result = await _adminController.createBackup(
                currentUser.id,
                description: description,
              );

              if (result != null) {
                Get.snackbar(
                  'تم بنجاح',
                  'تم إنشاء النسخة الاحتياطية بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  'خطأ',
                  'فشل إنشاء النسخة الاحتياطية: ${_adminController.error.value}',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار تأكيد الاستعادة
  void _showRestoreConfirmDialog(String backupId) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الاستعادة'),
        content: const Text(
          'هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () async {
              Get.back();

              final currentUser = _authController.currentUser.value;
              if (currentUser == null) return;

              final result = await _adminController.restoreBackup(backupId, currentUser.id);

              if (result) {
                Get.snackbar(
                  'تم بنجاح',
                  'تم استعادة النسخة الاحتياطية بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  'خطأ',
                  'فشل استعادة النسخة الاحتياطية: ${_adminController.error.value}',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('استعادة'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار تأكيد الحذف
  void _showDeleteConfirmDialog(String backupId) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text(
          'هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ لا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            onPressed: () async {
              Get.back();

              final result = await _adminController.deleteBackup(backupId);

              if (result) {
                Get.snackbar(
                  'تم بنجاح',
                  'تم حذف النسخة الاحتياطية بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  'خطأ',
                  'فشل حذف النسخة الاحتياطية: ${_adminController.error.value}',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار تصدير البيانات
  void _showExportDialog() {
    final tables = [
      'users',
      'departments',
      'tasks',
      'task_history',
      'comments',
      'notifications',
    ];

    String selectedTable = tables.first;
    final descriptionController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('تصدير البيانات'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'يمكنك تصدير بيانات جدول محدد إلى ملف CSV.',
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: selectedTable,
                  decoration: const InputDecoration(
                    labelText: 'الجدول',
                    hintText: 'اختر الجدول',
                  ),
                  items: tables.map((table) {
                    return DropdownMenuItem<String>(
                      value: table,
                      child: Text(table),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedTable = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'الوصف (اختياري)',
                    hintText: 'أدخل وصفًا للتصدير',
                  ),
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();

              final currentUser = _authController.currentUser.value;
              if (currentUser == null) return;

              final description = descriptionController.text.trim().isNotEmpty
                  ? descriptionController.text.trim()
                  : null;

              final result = await _adminController.exportTableToCsv(
                currentUser.id,
                selectedTable,
                description: description,
              );

              if (result != null) {
                Get.snackbar(
                  'تم بنجاح',
                  'تم تصدير البيانات بنجاح إلى: $result',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              } else {
                Get.snackbar(
                  'خطأ',
                  'فشل تصدير البيانات: ${_adminController.error.value}',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: Colors.red,
                  colorText: Colors.white,
                );
              }
            },
            child: const Text('تصدير'),
          ),
        ],
      ),
    );
  }

  /// تنسيق حجم الملف
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(2)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
    }
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }
}
