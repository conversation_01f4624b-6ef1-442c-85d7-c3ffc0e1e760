import 'package:get/get.dart';
import '../models/department_model.dart';
import '../models/user_model.dart';

/// وحدة تحكم الأقسام
/// تدير عمليات الأقسام مثل الإضافة والتعديل والحذف والاستعلام
class DepartmentController extends GetxController {
  final DatabaseHelper _db = Get.find<DatabaseHelper>();

  // قائمة الأقسام
  final RxList<Department> departments = <Department>[].obs;

  // حالة التحميل
  final RxBool isLoading = false.obs;

  // رسالة الخطأ
  final RxString errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadDepartments();
  }

  /// تحميل جميع الأقسام
  Future<void> loadDepartments() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final List<Department> loadedDepartments = await getAllDepartments();
      departments.value = loadedDepartments;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل الأقسام: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// الحصول على جميع الأقسام
  Future<List<Department>> getAllDepartments() async {
    try {
      final List<Map<String, dynamic>> maps = await _db.query('departments');
      return List.generate(maps.length, (i) {
        return Department.fromMap(maps[i]);
      });
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء استعلام الأقسام: $e';
      return [];
    }
  }

  /// الحصول على قسم بواسطة المعرف
  Future<Department?> getDepartmentById(String id) async {
    try {
      final List<Map<String, dynamic>> maps = await _db.query(
        'departments',
        whereClause: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return Department.fromMap(maps.first);
      }

      return null;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء استعلام القسم: $e';
      return null;
    }
  }

  /// إنشاء قسم جديد
  Future<Department?> createDepartment(Department department) async {
    try {
      final int id = await _db.insert('departments', department.toMap());

      if (id != -1) {
        final newDepartment = department.copyWith(id: department.id);
        departments.add(newDepartment);
        return newDepartment;
      }

      return null;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إنشاء القسم: $e';
      return null;
    }
  }

  /// تحديث قسم
  Future<bool> updateDepartment(Department department) async {
    try {
      final int rowsAffected = await _db.update(
        'departments',
        department.toMap(),
        'id = ?',
        [department.id],
      );

      if (rowsAffected > 0) {
        final index = departments.indexWhere((d) => d.id == department.id);
        if (index != -1) {
          departments[index] = department;
        }
        return true;
      }

      return false;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحديث القسم: $e';
      return false;
    }
  }

  /// حذف قسم
  Future<bool> deleteDepartment(String id) async {
    try {
      final int rowsAffected = await _db.update(
        'departments',
        {'isDeleted': 1},
        'id = ?',
        [id],
      );

      if (rowsAffected > 0) {
        departments.removeWhere((d) => d.id == id);
        return true;
      }

      return false;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء حذف القسم: $e';
      return false;
    }
  }

  /// الحصول على الموظفين في قسم
  Future<List<User>> getDepartmentEmployees(String departmentId) async {
    try {
      final List<Map<String, dynamic>> maps = await _db.query(
        'users',
        whereClause: 'departmentId = ? AND isDeleted = 0',
        whereArgs: [departmentId],
      );

      return List.generate(maps.length, (i) {
        return User.fromMap(maps[i]);
      });
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء استعلام موظفي القسم: $e';
      return [];
    }
  }

  /// إضافة موظف إلى قسم
  Future<bool> addEmployeeToDepartment(
      String userId, String departmentId) async {
    try {
      final int rowsAffected = await _db.update(
        'users',
        {'departmentId': departmentId},
        'id = ?',
        [userId],
      );

      return rowsAffected > 0;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إضافة الموظف إلى القسم: $e';
      return false;
    }
  }

  /// إزالة موظف من قسم
  Future<bool> removeEmployeeFromDepartment(String userId) async {
    try {
      final int rowsAffected = await _db.update(
        'users',
        {'departmentId': null},
        'id = ?',
        [userId],
      );

      return rowsAffected > 0;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إزالة الموظف من القسم: $e';
      return false;
    }
  }

  /// الحصول على اسم القسم
  String getDepartmentName(String? departmentId) {
    if (departmentId == null) return 'بدون قسم';

    final department =
        departments.firstWhereOrNull((d) => d.id == departmentId);
    return department?.name ?? 'قسم غير معروف';
  }
}
