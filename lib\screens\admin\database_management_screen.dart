import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/database_management_controller.dart';
import '../../database/database_helper.dart';
import '../../models/database_table_model.dart';
import '../../services/database_management_service.dart';
import '../../utils/responsive_helper.dart';
import 'database_table_view.dart';

/// شاشة إدارة جداول البيانات
///
/// توفر واجهة للتعامل مع جداول قاعدة البيانات
class DatabaseManagementScreen extends StatefulWidget {
  const DatabaseManagementScreen({super.key});

  @override
  State<DatabaseManagementScreen> createState() => _DatabaseManagementScreenState();
}

class _DatabaseManagementScreenState extends State<DatabaseManagementScreen> {
  late final DatabaseManagementController _controller;

  @override
  void initState() {
    super.initState();

    // إنشاء المتحكم إذا لم يكن موجودًا
    if (!Get.isRegistered<DatabaseManagementController>()) {
      final databaseHelper = Get.find<DatabaseHelper>();
      final databaseManagementService = DatabaseManagementService(databaseHelper);
      Get.put(DatabaseManagementController(databaseManagementService));
    }

    _controller = Get.find<DatabaseManagementController>();
  }

  @override
  Widget build(BuildContext context) {
    final isLargeScreen = ResponsiveHelper.isTablet(context) || ResponsiveHelper.isDesktop(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة قاعدة البيانات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: () {
              _controller.loadDatabaseTables();
              if (_controller.currentTable.value != null) {
                _controller.loadTableData();
              }
            },
          ),
        ],
      ),
      body: GetBuilder<DatabaseManagementController>(
        builder: (controller) {
          // استخدام متغيرات محلية بدلاً من الوصول المباشر للقيم المراقبة
          final databaseTables = controller.databaseTables;
          final currentTable = controller.currentTable.value;

          if (databaseTables.isEmpty) {
            return const Center(
              child: Text('لا توجد جداول متاحة'),
            );
          }

          return Row(
            children: [
              // قائمة الجداول
              SizedBox(
                width: isLargeScreen ? 250 : 200,
                child: Card(
                  margin: EdgeInsets.zero,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        color: AppColors.primary.withAlpha(26), // 0.1 * 255 = 26
                        child: Row(
                          children: [
                            const Icon(Icons.storage),
                            const SizedBox(width: 8),
                            Text(
                              'الجداول',
                              style: AppStyles.titleMedium,
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: ListView.builder(
                          itemCount: databaseTables.length,
                          itemBuilder: (context, index) {
                            final table = databaseTables[index];
                            return _buildTableListItem(table);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // محتوى الجدول
              Expanded(
                child: currentTable != null
                    ? DatabaseTableView(
                        controller: controller,
                        table: currentTable,
                      )
                    : const Center(
                        child: Text('اختر جدولًا من القائمة'),
                      ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTableListItem(DatabaseTable table) {
    // استخدام GetBuilder بدلاً من Obx
    return GetBuilder<DatabaseManagementController>(
      builder: (controller) {
        final isSelected = controller.currentTable.value?.id == table.id;

        return ListTile(
          leading: Icon(
            table.icon,
            color: isSelected ? AppColors.primary : null,
          ),
          title: Text(
            table.displayName,
            style: TextStyle(
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: isSelected ? AppColors.primary : null,
            ),
          ),
          subtitle: Text(
            table.description,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 12,
              // استخدام withAlpha بدلاً من withOpacity
              color: isSelected ? AppColors.primary.withAlpha(179) : null, // 0.7 * 255 = 179
            ),
          ),
          selected: isSelected,
          // استخدام withAlpha بدلاً من withOpacity
          selectedTileColor: AppColors.primary.withAlpha(26), // 0.1 * 255 = 26
          onTap: () {
            controller.selectTable(table.id);
          },
        );
      },
    );
  }
}
