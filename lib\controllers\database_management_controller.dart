import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../models/database_table_model.dart';
import '../services/database_management_service.dart';

/// متحكم إدارة جداول البيانات
///
/// يوفر واجهة للتعامل مع جداول قاعدة البيانات من خلال واجهة المستخدم
class DatabaseManagementController extends GetxController {
  final DatabaseManagementService _databaseManagementService;

  // حالة التحميل
  final isLoading = false.obs;

  // رسالة الخطأ
  final error = ''.obs;

  // الجدول الحالي
  final currentTable = Rxn<DatabaseTable>();

  // بيانات الجدول الحالي
  final tableData = <Map<String, dynamic>>[].obs;

  // إجمالي عدد السجلات
  final totalRowCount = 0.obs;

  // الصفحة الحالية
  final currentPage = 1.obs;

  // عدد السجلات في الصفحة
  final pageSize = 20.obs;

  // ترتيب البيانات
  final orderBy = ''.obs;

  // اتجاه الترتيب
  final orderDirection = 'ASC'.obs;

  // شرط التصفية
  final filterClause = ''.obs;

  // قيم شرط التصفية
  final filterArgs = <dynamic>[].obs;

  // نص البحث
  final searchText = ''.obs;

  // عمود البحث
  final searchColumn = ''.obs;

  // قائمة جداول قاعدة البيانات
  final databaseTables = <DatabaseTable>[].obs;

  DatabaseManagementController(this._databaseManagementService);

  @override
  void onInit() {
    super.onInit();
    loadDatabaseTables();
  }

  /// تحميل قائمة جداول قاعدة البيانات
  void loadDatabaseTables() {
    try {
      databaseTables.value = _databaseManagementService.getDatabaseTables();
      // استدعاء update() لإعلام GetBuilder بالتغييرات
      update();
    } catch (e) {
      error.value = e.toString();
      debugPrint('Error loading database tables: $e');
      update();
    }
  }

  /// اختيار جدول
  Future<void> selectTable(String tableId) async {
    isLoading.value = true;
    error.value = '';
    update();

    try {
      final table = _databaseManagementService.getDatabaseTableById(tableId);
      if (table != null) {
        currentTable.value = table;
        currentPage.value = 1;
        update();
        await loadTableData();
      } else {
        error.value = 'جدول غير موجود: $tableId';
        update();
      }
    } catch (e) {
      error.value = e.toString();
      debugPrint('Error selecting table: $e');
      update();
    } finally {
      isLoading.value = false;
      update();
    }
  }

  /// تحميل بيانات الجدول
  Future<void> loadTableData() async {
    if (currentTable.value == null) return;

    isLoading.value = true;
    error.value = '';
    update();

    try {
      // بناء شرط التصفية
      String? whereClause;
      List<dynamic>? whereArgs;

      if (filterClause.value.isNotEmpty) {
        whereClause = filterClause.value;
        whereArgs = filterArgs;
      }

      // إضافة شرط البحث
      if (searchText.value.isNotEmpty && searchColumn.value.isNotEmpty) {
        final column = currentTable.value!.columns.firstWhere(
          (col) => col.id == searchColumn.value,
          orElse: () => currentTable.value!.columns.first,
        );

        if (whereClause != null) {
          whereClause = '$whereClause AND ${column.id} LIKE ?';
          whereArgs = [...whereArgs!, '%${searchText.value}%'];
        } else {
          whereClause = '${column.id} LIKE ?';
          whereArgs = ['%${searchText.value}%'];
        }
      }

      // حساب إجمالي عدد السجلات
      totalRowCount.value = await _databaseManagementService.getTableRowCount(
        currentTable.value!.id,
        whereClause: whereClause,
        whereArgs: whereArgs,
      );

      // تحميل البيانات
      final data = await _databaseManagementService.getTableData(
        currentTable.value!.id,
        whereClause: whereClause,
        whereArgs: whereArgs,
        orderBy: orderBy.value.isNotEmpty ? '${orderBy.value} ${orderDirection.value}' : null,
        limit: pageSize.value,
        offset: (currentPage.value - 1) * pageSize.value,
      );

      tableData.value = data;
      update();
    } catch (e) {
      error.value = e.toString();
      debugPrint('Error loading table data: $e');
      update();
    } finally {
      isLoading.value = false;
      update();
    }
  }

  /// تغيير الصفحة
  Future<void> changePage(int page) async {
    if (page < 1 || page > (totalRowCount.value / pageSize.value).ceil()) return;

    currentPage.value = page;
    await loadTableData();
  }

  /// تغيير حجم الصفحة
  Future<void> changePageSize(int size) async {
    pageSize.value = size;
    currentPage.value = 1;
    await loadTableData();
  }

  /// تغيير ترتيب البيانات
  Future<void> changeOrder(String column, [bool ascending = true]) async {
    orderBy.value = column;
    orderDirection.value = ascending ? 'ASC' : 'DESC';
    await loadTableData();
  }

  /// تطبيق تصفية
  Future<void> applyFilter(String clause, List<dynamic> args) async {
    filterClause.value = clause;
    filterArgs.value = args;
    currentPage.value = 1;
    await loadTableData();
  }

  /// إزالة التصفية
  Future<void> clearFilter() async {
    filterClause.value = '';
    filterArgs.value = [];
    currentPage.value = 1;
    await loadTableData();
  }

  /// تطبيق بحث
  Future<void> applySearch(String column, String text) async {
    searchColumn.value = column;
    searchText.value = text;
    currentPage.value = 1;
    await loadTableData();
  }

  /// إزالة البحث
  Future<void> clearSearch() async {
    searchColumn.value = '';
    searchText.value = '';
    currentPage.value = 1;
    await loadTableData();
  }

  /// إنشاء سجل جديد
  Future<bool> createRow(Map<String, dynamic> row) async {
    if (currentTable.value == null) return false;

    isLoading.value = true;
    error.value = '';

    try {
      await _databaseManagementService.insertTableRow(currentTable.value!.id, row);
      await loadTableData();
      return true;
    } catch (e) {
      error.value = e.toString();
      debugPrint('Error creating row: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديث سجل
  Future<bool> updateRow(Map<String, dynamic> row, String primaryKeyColumn) async {
    if (currentTable.value == null) return false;

    isLoading.value = true;
    error.value = '';

    try {
      await _databaseManagementService.updateTableRow(
        currentTable.value!.id,
        row,
        primaryKeyColumn,
      );
      await loadTableData();
      return true;
    } catch (e) {
      error.value = e.toString();
      debugPrint('Error updating row: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// حذف سجل
  Future<bool> deleteRow(String primaryKeyColumn, dynamic primaryKeyValue) async {
    if (currentTable.value == null) return false;

    isLoading.value = true;
    error.value = '';

    try {
      await _databaseManagementService.deleteTableRow(
        currentTable.value!.id,
        primaryKeyColumn,
        primaryKeyValue,
      );
      await loadTableData();
      return true;
    } catch (e) {
      error.value = e.toString();
      debugPrint('Error deleting row: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// تنفيذ استعلام مخصص
  Future<List<Map<String, dynamic>>> executeQuery(String query, [List<dynamic>? arguments]) async {
    isLoading.value = true;
    error.value = '';

    try {
      final result = await _databaseManagementService.executeRawQuery(query, arguments);
      return result;
    } catch (e) {
      error.value = e.toString();
      debugPrint('Error executing query: $e');
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  /// تصدير بيانات الجدول إلى CSV
  Future<String?> exportToCsv() async {
    if (currentTable.value == null) return null;

    isLoading.value = true;
    error.value = '';

    try {
      // بناء شرط التصفية
      String? whereClause;
      List<dynamic>? whereArgs;

      if (filterClause.value.isNotEmpty) {
        whereClause = filterClause.value;
        whereArgs = filterArgs;
      }

      // إضافة شرط البحث
      if (searchText.value.isNotEmpty && searchColumn.value.isNotEmpty) {
        final column = currentTable.value!.columns.firstWhere(
          (col) => col.id == searchColumn.value,
          orElse: () => currentTable.value!.columns.first,
        );

        if (whereClause != null) {
          whereClause = '$whereClause AND ${column.id} LIKE ?';
          whereArgs = [...whereArgs!, '%${searchText.value}%'];
        } else {
          whereClause = '${column.id} LIKE ?';
          whereArgs = ['%${searchText.value}%'];
        }
      }

      final csvData = await _databaseManagementService.exportTableToCsv(
        currentTable.value!.id,
        whereClause: whereClause,
        whereArgs: whereArgs,
        orderBy: orderBy.value.isNotEmpty ? '${orderBy.value} ${orderDirection.value}' : null,
      );

      return csvData;
    } catch (e) {
      error.value = e.toString();
      debugPrint('Error exporting to CSV: $e');
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// استيراد بيانات من CSV
  Future<bool> importFromCsv(String csvData) async {
    if (currentTable.value == null) return false;

    isLoading.value = true;
    error.value = '';

    try {
      await _databaseManagementService.importTableFromCsv(
        currentTable.value!.id,
        csvData,
      );
      await loadTableData();
      return true;
    } catch (e) {
      error.value = e.toString();
      debugPrint('Error importing from CSV: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// الحصول على القيمة المقروءة للمفتاح الخارجي
  Future<String> getForeignKeyDisplayValue(
    String foreignTable,
    String foreignKeyColumn,
    String foreignKeyDisplayColumn,
    dynamic foreignKeyValue,
  ) async {
    try {
      return await _databaseManagementService.getForeignKeyDisplayValue(
        foreignTable,
        foreignKeyColumn,
        foreignKeyDisplayColumn,
        foreignKeyValue,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على قيمة المفتاح الخارجي: $e');
      return foreignKeyValue?.toString() ?? '-';
    }
  }

  /// الحصول على بيانات الجدول المرتبط
  Future<List<Map<String, dynamic>>> getForeignKeyOptions(
    String foreignTable,
    String foreignKeyColumn,
    String foreignKeyDisplayColumn,
  ) async {
    try {
      return await _databaseManagementService.getTableData(
        foreignTable,
        orderBy: '$foreignKeyDisplayColumn ASC',
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات الجدول المرتبط: $e');
      return [];
    }
  }
}
