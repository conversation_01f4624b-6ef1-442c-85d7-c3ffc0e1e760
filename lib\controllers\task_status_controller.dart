import 'package:get/get.dart';
import 'package:uuid/uuid.dart';
import '../models/task_status_model.dart';
import '../services/task_status_service.dart';

/// متحكم حالات المهام
///
/// يوفر وظائف للتعامل مع حالات المهام في واجهة المستخدم
class TaskStatusController extends GetxController {
  final TaskStatusService _taskStatusService = TaskStatusService();
  final Uuid _uuid = const Uuid();

  // قائمة حالات المهام
  final RxList<TaskStatus> taskStatuses = <TaskStatus>[].obs;
  
  // حالة التحميل
  final RxBool isLoading = false.obs;
  
  // رسالة الخطأ
  final RxString errorMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    loadTaskStatuses();
  }

  /// تحميل جميع حالات المهام
  Future<void> loadTaskStatuses({bool includeInactive = false}) async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final statuses = await _taskStatusService.getAllTaskStatuses(includeInactive: includeInactive);
      taskStatuses.assignAll(statuses);
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل حالات المهام: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// إنشاء حالة مهمة جديدة
  Future<bool> createTaskStatus({
    required String name,
    required String description,
    String? color,
    String? icon,
    bool isDefault = false,
    int sortOrder = 0,
  }) async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final newTaskStatus = TaskStatus(
        id: _uuid.v4(),
        name: name,
        description: description,
        color: color,
        icon: icon,
        isActive: true,
        isDefault: isDefault,
        sortOrder: sortOrder,
        createdAt: DateTime.now(),
      );
      
      final createdStatus = await _taskStatusService.createTaskStatus(newTaskStatus);
      
      // إذا كانت الحالة الجديدة هي الافتراضية، قم بتحديث القائمة بالكامل
      if (isDefault) {
        await loadTaskStatuses();
      } else {
        taskStatuses.add(createdStatus);
      }
      
      return true;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إنشاء حالة المهمة: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديث حالة مهمة
  Future<bool> updateTaskStatus(TaskStatus taskStatus) async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final updatedTaskStatus = taskStatus.copyWith(
        updatedAt: DateTime.now(),
      );
      
      final success = await _taskStatusService.updateTaskStatus(updatedTaskStatus);
      
      if (success) {
        // إذا كانت الحالة المحدثة هي الافتراضية، قم بتحديث القائمة بالكامل
        if (updatedTaskStatus.isDefault) {
          await loadTaskStatuses();
        } else {
          final index = taskStatuses.indexWhere((status) => status.id == updatedTaskStatus.id);
          if (index != -1) {
            taskStatuses[index] = updatedTaskStatus;
          }
        }
      }
      
      return success;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحديث حالة المهمة: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// حذف حالة مهمة
  Future<bool> deleteTaskStatus(String id) async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final success = await _taskStatusService.deleteTaskStatus(id);
      
      if (success) {
        taskStatuses.removeWhere((status) => status.id == id);
      }
      
      return success;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء حذف حالة المهمة: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// البحث عن حالات المهام
  Future<List<TaskStatus>> searchTaskStatuses(String query) async {
    if (query.isEmpty) {
      return taskStatuses;
    }
    
    try {
      return await _taskStatusService.searchTaskStatuses(query);
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء البحث عن حالات المهام: $e';
      return [];
    }
  }

  /// إعادة ترتيب حالات المهام
  Future<bool> reorderTaskStatuses(List<String> orderedIds) async {
    isLoading.value = true;
    errorMessage.value = '';
    
    try {
      final success = await _taskStatusService.reorderTaskStatuses(orderedIds);
      
      if (success) {
        await loadTaskStatuses();
      }
      
      return success;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إعادة ترتيب حالات المهام: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// تبديل حالة النشاط
  Future<bool> toggleTaskStatusActive(String id, bool isActive) async {
    final status = taskStatuses.firstWhere((s) => s.id == id);
    final updatedStatus = status.copyWith(isActive: isActive);
    return await updateTaskStatus(updatedStatus);
  }

  /// تعيين كحالة افتراضية
  Future<bool> setAsDefault(String id) async {
    final status = taskStatuses.firstWhere((s) => s.id == id);
    final updatedStatus = status.copyWith(isDefault: true);
    return await updateTaskStatus(updatedStatus);
  }
}
