import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/department_controller.dart';

/// مكون مخطط أداء الأقسام في مختلف المجالات
class DepartmentPerformanceChart extends StatefulWidget {
  const DepartmentPerformanceChart({super.key});

  @override
  State<DepartmentPerformanceChart> createState() => _DepartmentPerformanceChartState();
}

class _DepartmentPerformanceChartState extends State<DepartmentPerformanceChart> {
  // المتحكمات اللازمة
  final TaskController _taskController = Get.find<TaskController>();
  final DepartmentController _departmentController = Get.find<DepartmentController>();

  @override
  Widget build(BuildContext context) {
    final tasks = _taskController.tasks;

    // التحقق من وجود مهام
    if (tasks.isEmpty) {
      return const Center(
        child: Text('لا توجد مهام لعرضها'),
      );
    }

    // مجالات التقييم
    final List<String> evaluationAreas = [
      'نسبة الإكمال',
      'سرعة الإنجاز',
      'جودة العمل',
      'الالتزام بالمواعيد',
      'التعاون',
    ];

    // تجميع المهام حسب القسم
    final Map<String, Map<String, double>> departmentPerformance = {};

    // تجميع المهام حسب القسم
    for (final task in tasks) {
      final departmentId = task.departmentId;

      // إضافة القسم إذا لم يكن موجودًا
      if (!departmentPerformance.containsKey(departmentId)) {
        departmentPerformance[departmentId] = {};
        for (final area in evaluationAreas) {
          departmentPerformance[departmentId]![area] = 0;
        }
      }

      // حساب نسبة الإكمال
      departmentPerformance[departmentId]!['نسبة الإكمال'] =
          (departmentPerformance[departmentId]!['نسبة الإكمال']! + task.completionPercentage);

      // حساب سرعة الإنجاز (عكس المدة)
      if (task.completedAt != null && task.startDate != null) {
        final duration = task.completedAt!.difference(task.startDate!).inDays;
        // كلما كانت المدة أقل، كانت السرعة أعلى (مع حد أقصى 100)
        final speed = duration > 0 ? (100 / duration).clamp(0.0, 100.0) : 100.0;
        departmentPerformance[departmentId]!['سرعة الإنجاز'] =
            (departmentPerformance[departmentId]!['سرعة الإنجاز']! + speed);
      }

      // حساب جودة العمل (افتراضية - يمكن استبدالها بمقياس حقيقي)
      departmentPerformance[departmentId]!['جودة العمل'] =
          (departmentPerformance[departmentId]!['جودة العمل']! + 75);

      // حساب الالتزام بالمواعيد
      if (task.dueDate != null && task.completedAt != null) {
        final onTime = task.completedAt!.isBefore(task.dueDate!) ? 100.0 : 50.0;
        departmentPerformance[departmentId]!['الالتزام بالمواعيد'] =
            (departmentPerformance[departmentId]!['الالتزام بالمواعيد']! + onTime);
      }

      // حساب التعاون (افتراضية - يمكن استبدالها بمقياس حقيقي)
      departmentPerformance[departmentId]!['التعاون'] =
          (departmentPerformance[departmentId]!['التعاون']! + 80);
    }

    // حساب المتوسط لكل قسم ومجال
    final Map<String, int> departmentTaskCount = {};
    for (final task in tasks) {
      final departmentId = task.departmentId;
      departmentTaskCount[departmentId] = (departmentTaskCount[departmentId] ?? 0) + 1;
    }

    for (final departmentId in departmentPerformance.keys) {
      final taskCount = departmentTaskCount[departmentId] ?? 1;
      for (final area in evaluationAreas) {
        departmentPerformance[departmentId]![area] =
            departmentPerformance[departmentId]![area]! / taskCount;
      }
    }

    // اختيار أفضل 5 أقسام لعرضها في المخطط
    final List<String> topDepartmentIds = departmentPerformance.keys.toList()
      ..sort((a, b) {
        final aAvg = departmentPerformance[a]!.values.reduce((sum, value) => sum + value) / evaluationAreas.length;
        final bAvg = departmentPerformance[b]!.values.reduce((sum, value) => sum + value) / evaluationAreas.length;
        return bAvg.compareTo(aAvg); // ترتيب تنازلي
      });

    // اقتصار العدد على 5 أقسام كحد أقصى
    final selectedDepartmentIds = topDepartmentIds.take(5).toList();

    // ألوان للأقسام
    final List<Color> departmentColors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
    ];

    // استخدام FutureBuilder للحصول على أسماء الأقسام
    return FutureBuilder<Map<String, String>>(
      future: _getDepartmentNames(selectedDepartmentIds),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        final departmentNames = snapshot.data ?? {};

        // إنشاء بيانات المخطط الشريطي
        final List<Map<String, dynamic>> chartData = [];

        // إضافة بيانات لكل مجال تقييم
        for (final area in evaluationAreas) {
          final Map<String, dynamic> areaData = {
            'مجال': area,
          };

          // إضافة أداء كل قسم في هذا المجال
          for (int i = 0; i < selectedDepartmentIds.length; i++) {
            final departmentId = selectedDepartmentIds[i];
            final departmentName = departmentNames[departmentId] ?? 'قسم $departmentId';
            areaData[departmentName] = departmentPerformance[departmentId]![area]!;
          }

          chartData.add(areaData);
        }

        // إنشاء قائمة بأسماء الأقسام للمفتاح
        final List<Widget> legendItems = [];
        for (int i = 0; i < selectedDepartmentIds.length; i++) {
          final departmentId = selectedDepartmentIds[i];
          final departmentName = departmentNames[departmentId] ?? 'قسم $departmentId';
          final color = departmentColors[i % departmentColors.length];

          legendItems.add(
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(departmentName),
                ],
              ),
            ),
          );
        }

        return Column(
          children: [
            // المفتاح
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Wrap(
                alignment: WrapAlignment.center,
                spacing: 16,
                runSpacing: 8,
                children: legendItems,
              ),
            ),

            const SizedBox(height: 8),

            // المخطط الشريطي المجمع
            Expanded(
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: 100, // نسبة الأداء من 0 إلى 100
                  barGroups: List.generate(chartData.length, (index) {
                    final data = chartData[index];
                    final List<BarChartRodData> rods = [];

                    // إنشاء شريط لكل قسم
                    for (int i = 0; i < selectedDepartmentIds.length; i++) {
                      final departmentId = selectedDepartmentIds[i];
                      final departmentName = departmentNames[departmentId] ?? 'قسم $departmentId';
                      final color = departmentColors[i % departmentColors.length];

                      rods.add(
                        BarChartRodData(
                          toY: data[departmentName] ?? 0,
                          color: color,
                          width: 16,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(4),
                            topRight: Radius.circular(4),
                          ),
                        ),
                      );
                    }

                    return BarChartGroupData(
                      x: index,
                      barRods: rods,
                      barsSpace: 4,
                    );
                  }),
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          if (value < 0 || value >= chartData.length) {
                            return const SizedBox.shrink();
                          }

                          final data = chartData[value.toInt()];
                          final areaName = data['مجال'] as String;

                          return SideTitleWidget(
                            meta: meta,
                            child: Text(
                              areaName,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          );
                        },
                        reservedSize: 30,
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            value.toInt().toString(),
                            style: TextStyle(
                              color: Get.isDarkMode ? Colors.white : Colors.black,
                              fontSize: 12,
                            ),
                          );
                        },
                      ),
                      axisNameWidget: const Padding(
                        padding: EdgeInsets.only(bottom: 8.0),
                        child: Text(
                          'نسبة الأداء (%)',
                          style: TextStyle(fontSize: 12),
                        ),
                      ),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: false,
                    horizontalInterval: 20,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: Get.isDarkMode ? Colors.white10 : Colors.black12,
                        strokeWidth: 1,
                      );
                    },
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border(
                      bottom: BorderSide(
                        color: Get.isDarkMode ? Colors.white70 : Colors.black54,
                        width: 1,
                      ),
                      left: BorderSide(
                        color: Get.isDarkMode ? Colors.white70 : Colors.black54,
                        width: 1,
                      ),
                    ),
                  ),
                  barTouchData: BarTouchData(
                    enabled: true,
                    touchTooltipData: BarTouchTooltipData(
                      getTooltipColor: (_) => Get.isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
                      getTooltipItem: (group, groupIndex, rod, rodIndex) {
                        final data = chartData[group.x.toInt()];
                        final areaName = data['مجال'] as String;
                        final departmentId = selectedDepartmentIds[rodIndex];
                        final departmentName = departmentNames[departmentId] ?? 'قسم $departmentId';
                        final performance = rod.toY.toStringAsFixed(1);

                        return BarTooltipItem(
                          '$departmentName - $areaName\n',
                          TextStyle(
                            color: Get.isDarkMode ? Colors.white : Colors.black,
                            fontWeight: FontWeight.bold,
                          ),
                          children: [
                            TextSpan(
                              text: '$performance%',
                              style: TextStyle(
                                color: Get.isDarkMode ? Colors.white70 : Colors.black87,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// الحصول على أسماء الأقسام من معرفاتها
  Future<Map<String, String>> _getDepartmentNames(List<String> departmentIds) async {
    final Map<String, String> departmentNames = {};

    for (final departmentId in departmentIds) {
      try {
        final department = await _departmentController.getDepartmentById(departmentId);
        departmentNames[departmentId] = department?.name ?? 'قسم $departmentId';
      } catch (e) {
        // في حالة حدوث خطأ، استخدم اسمًا افتراضيًا
        departmentNames[departmentId] = 'قسم $departmentId';
        debugPrint('خطأ في الحصول على اسم القسم $departmentId: $e');
      }
    }

    return departmentNames;
  }
}
