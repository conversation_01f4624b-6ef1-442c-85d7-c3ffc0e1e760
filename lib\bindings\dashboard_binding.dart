import 'package:get/get.dart';
import '../controllers/dashboard_controller.dart';
import '../controllers/dashboard_layout_controller.dart';
import '../services/dashboard_service.dart';
import '../controllers/auth_controller.dart';
import '../services/chart_export_service.dart';

/// رابط لوحة المعلومات
///
/// يضمن تسجيل جميع الخدمات والمتحكمات اللازمة للوحة المعلومات
class DashboardBinding extends Bindings {
  @override
  void dependencies() {
    // تم إزالة خدمات قاعدة البيانات والمستودعات - سيتم استخدام API

    // التأكد من وجود متحكم المصادقة وتهيئته بشكل صحيح
    if (!Get.isRegistered<AuthController>()) {
      final authController = AuthController();
      Get.put(authController, permanent: true);
    } else {
      // إعادة تهيئة المتحكم إذا كان موجودًا بالفعل
      final authController = Get.find<AuthController>();
      if (authController.currentUser.value == null) {
        authController.onInit(); // إعادة تهيئة المتحكم
      }
    }

    // التأكد من وجود خدمة لوحة المعلومات
    if (!Get.isRegistered<DashboardService>()) {
      Get.lazyPut(() => DashboardService().init());
    }

    // التأكد من وجود متحكم لوحة المعلومات
    if (!Get.isRegistered<DashboardController>()) {
      Get.put(DashboardController());
    }

    // التأكد من وجود خدمة تصدير المخططات
    if (!Get.isRegistered<ChartExportService>()) {
      Get.put(ChartExportService(), permanent: true);
    }

    // التأكد من وجود متحكم تخطيط لوحة المعلومات
    if (!Get.isRegistered<DashboardLayoutController>()) {
      Get.put(DashboardLayoutController(), permanent: true);
    }
  }
}
