import 'package:flutter/foundation.dart';
import '../services/notification_service.dart';

class NotificationProvider with ChangeNotifier {
  final NotificationService _notificationService = NotificationService();

  List<Notification> _notifications = [];
  int _unreadCount = 0;
  bool _isLoading = false;
  String? _error;

  List<Notification> get notifications => _notifications;
  int get unreadCount => _unreadCount;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load notifications for a user
  Future<void> loadNotifications(String userId) async {
    _isLoading = true;
    notifyListeners();

    try {
      _notifications =
          await _notificationService.getNotificationsForUser(userId);
      await _updateUnreadCount(userId);
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update unread notification count
  Future<void> _updateUnreadCount(String userId) async {
    try {
      _unreadCount =
          await _notificationService.getUnreadNotificationCount(userId);
    } catch (e) {
      print('Error updating unread count: $e');
      _unreadCount = 0;
    }
  }

  // Mark notification as read
  Future<bool> markAsRead(String notificationId, String userId) async {
    try {
      final result =
          await _notificationService.markNotificationAsRead(notificationId);
      if (result) {
        // Update notification in list
        final index = _notifications.indexWhere((n) => n.id == notificationId);
        if (index >= 0) {
          final updatedNotification =
              _notifications[index].copyWith(isRead: true);
          _notifications[index] = updatedNotification;
        }

        // Update unread count
        await _updateUnreadCount(userId);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _error = e.toString();
      return false;
    }
  }

  // Mark all notifications as read
  Future<bool> markAllAsRead(String userId) async {
    _isLoading = true;
    notifyListeners();

    try {
      final result =
          await _notificationService.markAllNotificationsAsRead(userId);
      if (result) {
        // Update all notifications in list
        _notifications = _notifications.map((notification) {
          return notification.copyWith(isRead: true);
        }).toList();

        // Update unread count
        _unreadCount = 0;
        return true;
      } else {
        _error = 'Failed to mark all notifications as read';
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Delete notification
  Future<bool> deleteNotification(String notificationId, String userId) async {
    try {
      final result =
          await _notificationService.deleteNotification(notificationId);
      if (result) {
        // Remove notification from list
        _notifications.removeWhere((n) => n.id == notificationId);

        // Update unread count
        await _updateUnreadCount(userId);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _error = e.toString();
      return false;
    }
  }

  // Delete all notifications
  Future<bool> deleteAllNotifications(String userId) async {
    _isLoading = true;
    notifyListeners();

    try {
      final result =
          await _notificationService.deleteAllNotificationsForUser(userId);
      if (result) {
        _notifications = [];
        _unreadCount = 0;
        return true;
      } else {
        _error = 'Failed to delete all notifications';
        return false;
      }
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clean up old notifications
  Future<void> cleanupOldNotifications(String userId) async {
    try {
      await _notificationService.cleanupOldNotifications(userId);
      await loadNotifications(userId); // Reload notifications
    } catch (e) {
      print('Error cleaning up old notifications: $e');
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
