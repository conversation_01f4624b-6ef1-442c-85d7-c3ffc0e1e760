import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../constants/app_styles.dart';
import '../../controllers/admin_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/theme_controller.dart';
import '../../models/user_model.dart';

import 'user_management_tab.dart';
import 'backup_restore_tab.dart';
import 'system_settings_tab.dart';
import 'analytics_tab.dart';
import 'permissions_management_screen.dart';
import 'database_management_tab.dart';
import 'sync_settings_tab.dart';
import 'role_management_tab.dart';
import 'interface_permissions_tab.dart';
import '../../routes/app_routes.dart';

/// شاشة لوحة التحكم الإدارية
///
/// توفر واجهة للمسؤول للتحكم في جميع جوانب النظام
class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen>
    with SingleTickerProviderStateMixin, RouteAware {
  // مراقب المسارات للتتبع عند العودة إلى الصفحة
  late RouteObserver<PageRoute> _routeObserver;
  late TabController _tabController;
  final AuthController _authController = Get.find<AuthController>();
  final ThemeController _themeController = Get.find<ThemeController>();
  late AdminController _adminController;

  // المستخدم المحدد لإدارة الصلاحيات
  User? selectedPermissionUser;

  @override
  void initState() {
    super.initState();

    // التحقق من وجود المتحكم أو إنشاؤه
    if (!Get.isRegistered<AdminController>()) {
      Get.put(AdminController());
    }
    _adminController = Get.find<AdminController>();

    // إنشاء متحكم التبويبات
    _tabController = TabController(length: 9, vsync: this);

    // تسجيل متحكم التبويبات في GetX
    Get.put(_tabController, tag: 'admin_tab_controller');

    // الحصول على مراقب المسارات من GetX
    _routeObserver = Get.find<RouteObserver<PageRoute>>();

    // تهيئة البيانات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeData();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // تسجيل هذه الصفحة مع مراقب المسارات
    final route = ModalRoute.of(context);
    if (route != null && route is PageRoute) {
      _routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    // إلغاء تسجيل هذه الصفحة من مراقب المسارات
    _routeObserver.unsubscribe(this);
    _tabController.dispose();
    super.dispose();
  }

  /// يتم استدعاء هذه الدالة عند العودة إلى هذه الصفحة
  @override
  void didPopNext() {
    debugPrint('تم العودة إلى لوحة التحكم الإدارية');
    // إعادة تحميل البيانات عند العودة إلى الصفحة
    _initializeData();
  }

  /// الانتقال إلى تبويب إدارة الصلاحيات
  void selectPermissionsTab(User user) {
    setState(() {
      selectedPermissionUser = user;
      _tabController.animateTo(1); // الانتقال إلى تبويب الصلاحيات (الثاني)
    });
  }

  /// تهيئة البيانات الأولية
  Future<void> _initializeData() async {
    // التحقق من صلاحيات المستخدم
    final currentUser = _authController.currentUser.value;
    if (currentUser == null || currentUser.role != UserRole.admin) {
      Get.snackbar(
        'خطأ في الصلاحيات',
        'ليس لديك صلاحية للوصول إلى لوحة التحكم الإدارية',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      Get.back();
      return;
    }

    // تحميل البيانات
    await _adminController.loadAllUsers();
    await _adminController.loadSystemSettings();
    await _adminController.loadSystemLogs();
    await _adminController.loadBackups();
  }

  @override
  Widget build(BuildContext context) {
    // استخدام متغيرات مراقبة مباشرة
    final isArabic = Get.locale?.languageCode == 'ar';
    final isDarkMode = _themeController.isDarkMode.value;

    // التحقق مما إذا كانت الشاشة مضمنة داخل HomeScreen
    final isEmbedded = ModalRoute.of(context)?.settings.name != '/admin';

    // استخدام GetBuilder بدلاً من Obx لتجنب التحديثات المتكررة
    return GetBuilder<AdminController>(
      builder: (_) {
        return Directionality(
          textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
          child: Scaffold(
            // إظهار AppBar فقط إذا لم تكن الشاشة مضمنة داخل HomeScreen
            appBar: isEmbedded
                ? null
                : AppBar(
                    title: Text('adminDashboard'.tr),
                    centerTitle: true,
                    elevation: 2,
                    actions: [
                      // زر اختبار نظام الصلاحيات
                      IconButton(
                        icon: const Icon(Icons.security_outlined),
                        tooltip: 'اختبار نظام الصلاحيات',
                        onPressed: () {
                          Get.toNamed(AppRoutes.permissionTest);
                        },
                      ),
                      // زر تبديل الوضع الليلي (Dark Mode)
                      IconButton(
                        icon: Icon(
                          isDarkMode ? Icons.light_mode : Icons.dark_mode,
                        ),
                        tooltip: isDarkMode ? 'lightMode'.tr : 'darkMode'.tr,
                        onPressed: () {
                          _themeController.toggleTheme();
                        },
                      ),
                    ],
                    bottom: _buildTabBar(),
                  ),
            body: Column(
              children: [
                // إضافة شريط التبويبات إذا كانت الشاشة مضمنة داخل HomeScreen
                if (isEmbedded) _buildTabBar(),

                // محتوى الشاشة
                Expanded(child: _buildBody()),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء شريط التبويبات
  PreferredSizeWidget _buildTabBar() {
    // التحقق مما إذا كانت الشاشة مضمنة داخل HomeScreen
    final isEmbedded = ModalRoute.of(context)?.settings.name != '/admin';
    final isDarkMode = _themeController.isDarkMode.value;

    return TabBar(
      controller: _tabController,
      isScrollable: true,
      // استخدام ألوان مختلفة بناءً على ما إذا كانت الشاشة مضمنة أم لا
      labelColor:
          isEmbedded ? (isDarkMode ? Colors.white : Colors.blue) : Colors.white,
      unselectedLabelColor: isEmbedded
          ? (isDarkMode ? Colors.grey[300] : Colors.grey[700])
          : Colors.white70,
      indicatorColor:
          isEmbedded ? (isDarkMode ? Colors.white : Colors.blue) : Colors.white,
      tabs: const [
        Tab(
          icon: Icon(Icons.people),
          text: 'إدارة المستخدمين',
        ),
        Tab(
          icon: Icon(Icons.security),
          text: 'إدارة الصلاحيات',
        ),
        Tab(
          icon: Icon(Icons.admin_panel_settings),
          text: 'إدارة الأدوار',
        ),
        Tab(
          icon: Icon(Icons.web),
          text: 'صلاحيات الواجهات',
        ),
        Tab(
          icon: Icon(Icons.backup),
          text: 'النسخ الاحتياطية',
        ),
        Tab(
          icon: Icon(Icons.settings),
          text: 'إعدادات النظام',
        ),
        Tab(
          icon: Icon(Icons.analytics),
          text: 'التحليلات',
        ),
        Tab(
          icon: Icon(Icons.storage),
          text: 'إدارة قاعدة البيانات',
        ),
        Tab(
          icon: Icon(Icons.sync),
          text: 'إعدادات التزامن',
        ),
      ],
    );
  }

  /// بناء محتوى الشاشة
  Widget _buildBody() {
    // استخدام متغيرات مراقبة بدلاً من Obx متداخلة
    final isLoading = _adminController.isLoading.value;
    final error = _adminController.error.value;

    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (error.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: AppStyles.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: AppStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return TabBarView(
      controller: _tabController,
      children: const [
        UserManagementTab(),
        PermissionsManagementScreen(isTab: true), // تحديد أنها تستخدم كتبويب
        RoleManagementTab(),
        InterfacePermissionsTab(), // التبويب الجديد لإدارة صلاحيات الواجهات
        BackupRestoreTab(),
        SystemSettingsTab(),
        AnalyticsTab(),
        DatabaseManagementTab(),
        SyncSettingsTab(),
      ],
    );
  }
}
