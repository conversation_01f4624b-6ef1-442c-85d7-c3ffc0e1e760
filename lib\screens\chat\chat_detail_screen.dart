import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:geolocator/geolocator.dart';
// import 'package:url_launcher/url_launcher.dart'; // غير مستخدم حالياً - معلق لأن الدوال التي تستخدمه معلقة
import "package:file_picker/file_picker.dart";
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/message_controller.dart';
import '../../controllers/user_controller.dart';
import '../../models/chat_group_model.dart';
import '../../models/message_model.dart';
import '../../models/user_model.dart';
// import '../../models/message_attachment_model.dart'; // غير مستخدم حالياً - معلق لأن الدوال التي تستخدمه معلقة
import '../../widgets/chat/message_item_widget.dart';
import 'chat_info_screen.dart';
import 'add_members_screen.dart';
import 'search_conversation_screen.dart';
import 'mute_notifications_screen.dart';

/// @deprecated استخدم UnifiedChatDetailScreen بدلاً من ذلك
/// تم استبداله بشاشة المحادثة الموحدة
/// انظر: lib/screens/chat/unified_chat_detail_screen.dart
class ChatDetailScreen extends StatefulWidget {
  final ChatGroup chatGroup;

  const ChatDetailScreen({
    super.key,
    required this.chatGroup,
  });

  @override
  State<ChatDetailScreen> createState() => _ChatDetailScreenState();
}

class _ChatDetailScreenState extends State<ChatDetailScreen> {
  final _messageController = Get.find<MessageController>();
  final _authController = Get.find<AuthController>();
  final _userController = Get.find<UserController>();
  final _textController = TextEditingController();
  final _scrollController = ScrollController();

  // متغيرات للإشارة إلى المستخدمين
  final RxBool _showMentionsList = false.obs;
  final RxList<User> _filteredUsers = <User>[].obs;
  final RxList<String> _mentionedUserIds = <String>[].obs;

  // متغير للرد على رسالة
  final Rx<Message?> _replyToMessage = Rx<Message?>(null);

  // متغيرات لدعم الرموز التعبيرية
  final RxBool _showEmojiPicker = false.obs;
  final FocusNode _textFieldFocusNode = FocusNode();

  // متغير لتخزين مراقب التحديثات
  Worker? _messagesUpdateSubscription;

  // معرف الرسالة المراد التمرير إليها (إذا تم تمريره من شاشة البحث)
  String? _messageToScrollTo;

  // متغير لتتبع ما إذا كان التمرير قيد التنفيذ حاليًا
  bool _isScrolling = false;

  // متغير لتتبع آخر رسالة تم استلامها
  String? _lastReceivedMessageId;

  @override
  void initState() {
    super.initState();
    _loadMessages();

    // تحميل المستخدمين إذا لم يتم تحميلهم بالفعل
    if (_userController.users.isEmpty) {
      _userController.loadAllUsers();
    }

    // إضافة مستمع للتمرير للصفحات
    _scrollController.addListener(_scrollListener);

    // إضافة مستمع لحقل النص للكشف عن الإشارات
    _textController.addListener(_handleTextChange);

    // الاشتراك في إشعارات تحديث الرسائل
    _messagesUpdateSubscription =
        _messageController.subscribeToMessagesUpdates(() {
      // تحديث الرسائل عند استلام إشعار
      _refreshMessages();
    });

    // مراقبة قائمة الرسائل للتمرير التلقائي عند إضافة رسائل جديدة
    // لكن لا نقوم بالتمرير هنا لتجنب تكرار التمرير
    // التمرير يتم من خلال _handleNewMessage في message_controller.dart
    _messageController.messages.listen((_) {
      debugPrint(
          '🔄 تم تغيير قائمة الرسائل، عدد الرسائل: ${_messageController.messages.length}');
    });
  }

  /// معالجة تغيير النص للكشف عن الإشارات
  void _handleTextChange() {
    final text = _textController.text;
    final cursorPosition = _textController.selection.baseOffset;

    // التحقق من وجود علامة @ قبل موضع المؤشر
    if (cursorPosition > 0 && text.isNotEmpty) {
      // البحث عن آخر علامة @ قبل موضع المؤشر
      int lastAtSymbol = text.substring(0, cursorPosition).lastIndexOf('@');

      if (lastAtSymbol != -1) {
        // التحقق من أن علامة @ ليست جزءًا من كلمة (يجب أن تكون في بداية الكلمة)
        bool isValidMention = lastAtSymbol == 0 ||
            text[lastAtSymbol - 1] == ' ' ||
            text[lastAtSymbol - 1] == '\n';

        if (isValidMention) {
          // استخراج النص بعد علامة @ وقبل موضع المؤشر
          String query =
              text.substring(lastAtSymbol + 1, cursorPosition).toLowerCase();

          // البحث عن المستخدمين المطابقين
          _filterUsers(query);

          // عرض قائمة الإشارات
          _showMentionsList.value = true;
          return;
        }
      }
    }

    // إخفاء قائمة الإشارات إذا لم يتم العثور على علامة @ صالحة
    _showMentionsList.value = false;
  }

  /// تصفية المستخدمين بناءً على الاستعلام
  void _filterUsers(String query) {
    if (query.isEmpty) {
      // إذا كان الاستعلام فارغًا، عرض جميع المستخدمين في المجموعة
      _filteredUsers.value = _userController.users
          .where((user) => _messageController.groupMembers
              .any((member) => member.userId == user.id))
          .toList();
    } else {
      // تصفية المستخدمين بناءً على الاستعلام
      _filteredUsers.value = _userController.users
          .where((user) =>
              _messageController.groupMembers
                  .any((member) => member.userId == user.id) &&
              (user.name.toLowerCase().contains(query) ||
                  user.email.toLowerCase().contains(query)))
          .toList();
    }
  }

  /// إدراج إشارة إلى مستخدم في حقل النص
  void _insertMention(User user) {
    final text = _textController.text;
    final cursorPosition = _textController.selection.baseOffset;

    // البحث عن آخر علامة @ قبل موضع المؤشر
    int lastAtSymbol = text.substring(0, cursorPosition).lastIndexOf('@');

    if (lastAtSymbol != -1) {
      // استبدال النص من علامة @ إلى موضع المؤشر باسم المستخدم
      final newText =
          '${text.substring(0, lastAtSymbol)}@${user.name} ${text.substring(cursorPosition)}';

      // تحديث النص وموضع المؤشر
      _textController.value = TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(
          offset: lastAtSymbol + user.name.length + 2, // +2 for @ and space
        ),
      );

      // إضافة معرف المستخدم إلى قائمة المستخدمين المشار إليهم
      if (!_mentionedUserIds.contains(user.id)) {
        _mentionedUserIds.add(user.id);
      }
    }

    // إخفاء قائمة الإشارات
    _showMentionsList.value = false;
  }

  void _scrollListener() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _loadMoreMessages();
    }
  }

  Future<void> _loadMessages() async {
    final currentUser = _authController.currentUser.value;
    if (currentUser != null) {
      try {
        // عرض مؤشر التحميل
        _messageController.isLoadingMessages.value = true;

        // تحميل الرسائل
        await _messageController.loadMessages(
            widget.chatGroup.id, currentUser.id);

        // التمرير إلى الأسفل بعد تحميل الرسائل
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_messageToScrollTo != null) {
            _scrollToMessage(_messageToScrollTo!);
            _messageToScrollTo = null; // إعادة تعيين بعد التمرير
          } else {
            _scrollToBottomDelayed();
          }
        });
      } catch (e) {
        // عرض رسالة خطأ
        Get.snackbar(
          'خطأ في تحميل الرسائل',
          'حدث خطأ أثناء تحميل الرسائل. يرجى المحاولة مرة أخرى.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
          duration: const Duration(seconds: 3),
        );
      } finally {
        // إخفاء مؤشر التحميل
        _messageController.isLoadingMessages.value = false;
      }
    }
  }

  /// التمرير إلى رسالة محددة بواسطة معرفها
  void _scrollToMessage(String messageId) {
    // البحث عن الرسالة في القائمة
    final index =
        _messageController.messages.indexWhere((m) => m.id == messageId);
    if (index == -1) return; // الرسالة غير موجودة

    // حساب موضع الرسالة في القائمة
    double offset = 0;
    for (int i = 0; i <= index; i++) {
      // تقدير ارتفاع كل رسالة (يمكن تحسين هذا بقياس الارتفاع الفعلي)
      offset += 80; // ارتفاع تقريبي لكل رسالة
    }

    // التمرير إلى الرسالة مع تأثير بصري
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        offset.clamp(0, _scrollController.position.maxScrollExtent),
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );

      // إظهار تأثير بصري لتمييز الرسالة
      Get.snackbar(
        'تم العثور على الرسالة',
        'تم التمرير إلى الرسالة المطلوبة',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
    }
  }

  Future<void> _loadMoreMessages() async {
    final currentUser = _authController.currentUser.value;
    if (currentUser != null && !_messageController.isLoadingMessages.value) {
      try {
        // تحميل المزيد من الرسائل بدون عرض رسالة
        await _messageController.loadMoreMessages(
            widget.chatGroup.id, currentUser.id);
      } catch (e) {
        // عرض رسالة خطأ فقط في حالة الفشل
        Get.snackbar(
          'خطأ في تحميل المزيد من الرسائل',
          'حدث خطأ أثناء تحميل المزيد من الرسائل. يرجى المحاولة مرة أخرى.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
          duration: const Duration(seconds: 3),
        );
      }
    }
  }

  void _sendMessage() {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return;

    final content = _textController.text.trim();
    if (content.isEmpty) return;

    // إنشاء قائمة بمعرفات المستخدمين المشار إليهم
    final mentionedUserIds = <String>[];

    // البحث عن الإشارات في النص
    final mentionRegex = RegExp(r'@([^\s]+)');
    final matches = mentionRegex.allMatches(content);

    for (final match in matches) {
      final mentionedName = match.group(1);
      if (mentionedName != null) {
        // البحث عن المستخدم بالاسم
        final mentionedUser = _userController.users.firstWhereOrNull(
            (user) => user.name.toLowerCase() == mentionedName.toLowerCase());

        if (mentionedUser != null &&
            !mentionedUserIds.contains(mentionedUser.id)) {
          mentionedUserIds.add(mentionedUser.id);
        }
      }
    }

    // إرسال الرسالة مع الإشارات
    _messageController
        .sendMessage(
      widget.chatGroup.id,
      currentUser.id,
      content,
      mentionedUserIds: mentionedUserIds,
      replyToMessageId: _replyToMessage.value?.id,
    )
        .then((message) {
      if (message != null) {
        // مسح النص وإعادة تعيين الرد
        _textController.clear();
        _replyToMessage.value = null;
        _mentionedUserIds.clear();

        // التمرير إلى الأسفل بعد الإرسال
        _scrollToBottomDelayed();
      }
    });
  }

  /// تمرير القائمة إلى الأسفل بعد تأخير قصير
  void _scrollToBottomDelayed() {
    // تجنب تكرار التمرير إذا كان قيد التنفيذ بالفعل
    if (_isScrolling) {
      return;
    }

    // تعيين حالة التمرير إلى قيد التنفيذ
    _isScrolling = true;

    // تأخير قصير لضمان اكتمال تحديث القائمة
    Future.delayed(const Duration(milliseconds: 100), () {
      _scrollToBottom();

      // إعادة تعيين حالة التمرير بعد انتهاء التمرير
      Future.delayed(const Duration(milliseconds: 300), () {
        _isScrolling = false;
      });
    });
  }

  /// تمرير القائمة إلى الأسفل بشكل بسيط ومباشر
  void _scrollToBottom() {
    if (!_scrollController.hasClients) {
      return;
    }

    try {
      // التمرير إلى الأسفل بشكل مباشر
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    } catch (e) {
      // محاولة بديلة باستخدام jumpTo
      try {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      } catch (e2) {
        // تجاهل الخطأ
      }
    }
  }

  /// تحديث الرسائل
  Future<void> _refreshMessages() async {
    final currentUser = _authController.currentUser.value;
    if (currentUser != null && !_messageController.isLoadingMessages.value) {
      try {
        // تحميل الرسائل الجديدة
        await _messageController.loadMessages(
            widget.chatGroup.id, currentUser.id);

        // التمرير إلى الأسفل دائماً عند استلام رسائل جديدة
        _scrollToBottomDelayed();
      } catch (e) {
        // لا نعرض رسالة خطأ هنا لتجنب إزعاج المستخدم
        debugPrint('خطأ في تحديث الرسائل: $e');
      }
    }
  }

  @override
  void dispose() {
    _textController.removeListener(_handleTextChange);
    _textController.dispose();
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _textFieldFocusNode.dispose();

    // إلغاء الاشتراك في إشعارات تحديث الرسائل
    _messagesUpdateSubscription?.dispose();

    super.dispose();
  }

  /// تبديل عرض منتقي الرموز التعبيرية
  void _toggleEmojiPicker() {
    _showEmojiPicker.value = !_showEmojiPicker.value;
    if (_showEmojiPicker.value) {
      // إخفاء لوحة المفاتيح عند عرض منتقي الرموز التعبيرية
      _textFieldFocusNode.unfocus();
    } else {
      // عرض لوحة المفاتيح عند إخفاء منتقي الرموز التعبيرية
      _textFieldFocusNode.requestFocus();
    }
  }

  /// إضافة رمز تعبيري إلى النص
  void _onEmojiSelected(Category? category, Emoji emoji) {
    final text = _textController.text;
    final cursorPosition = _textController.selection.baseOffset;

    // إضافة الرمز التعبيري في موضع المؤشر
    final newText = cursorPosition >= 0
        ? '${text.substring(0, cursorPosition)}${emoji.emoji}${text.substring(cursorPosition)}'
        : text + emoji.emoji;

    // تحديث النص وموضع المؤشر
    _textController.value = TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(
        offset: cursorPosition >= 0
            ? cursorPosition + emoji.emoji.length
            : newText.length,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) {
      return const Scaffold(
        body: Center(child: Text('User not logged in')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: GestureDetector(
          onTap: () {
            Get.to(() => ChatInfoScreen(chatGroup: widget.chatGroup));
          },
          child: Row(
            children: [
              CircleAvatar(
                backgroundColor: widget.chatGroup.isDirectMessage
                    ? AppColors.accent
                    : AppColors.primary,
                radius: 16,
                child: widget.chatGroup.avatarUrl != null
                    ? Image.network(widget.chatGroup.avatarUrl!)
                    : Text(
                        widget.chatGroup.name.substring(0, 1).toUpperCase(),
                        style:
                            const TextStyle(color: Colors.white, fontSize: 14),
                      ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.chatGroup.name,
                      style: const TextStyle(fontSize: 16),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Obx(() {
                      if (_messageController.isLoadingMessages.value) {
                        return const Text(
                          'Loading...',
                          style: TextStyle(fontSize: 12, color: Colors.white70),
                        );
                      }

                      final memberCount =
                          _messageController.groupMembers.length;
                      return Text(
                        widget.chatGroup.isDirectMessage
                            ? 'Online'
                            : '$memberCount members',
                        style: const TextStyle(
                            fontSize: 12, color: Colors.white70),
                      );
                    }),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              _showChatOptions(context);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Messages list
          Expanded(
            child: Obx(() {
              if (_messageController.isLoadingMessages.value &&
                  _messageController.messages.isEmpty) {
                return const Center(child: CircularProgressIndicator());
              }

              if (_messageController.error.value.isNotEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Error loading messages',
                        style: AppStyles.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _messageController.error.value,
                        style: AppStyles.bodySmall,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadMessages,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                );
              }

              if (_messageController.messages.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.chat_bubble_outline,
                        size: 64,
                        color: Colors.grey.shade300,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No messages yet',
                        style: AppStyles.titleMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Start the conversation by sending a message',
                        style: AppStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                );
              }

              // Show loading indicator at the top when loading more messages
              return Stack(
                children: [
                  ListView.builder(
                    key: ValueKey(
                        'messages-list-${_messageController.messages.length}'),
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    reverse: false,
                    itemCount: _messageController.messages.length,
                    // استخدام addAutomaticKeepAlives لضمان الاحتفاظ بحالة التمرير
                    addAutomaticKeepAlives: false,
                    // استخدام cacheExtent أكبر لتحسين الأداء
                    cacheExtent: 1000,
                    itemBuilder: (context, index) {
                      // لا نقوم بالتمرير التلقائي هنا لتجنب تكرار التمرير
                      // التمرير يتم من خلال _scrollToBottomDelayed() فقط

                      // إظهار مؤشر التحميل في أعلى القائمة عند تحميل المزيد من الرسائل
                      if (index == 0 &&
                          _messageController.isLoadingMessages.value) {
                        return Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              child: const Center(
                                child: SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                ),
                              ),
                            ),
                            _buildMessageItem(
                                _messageController.messages[index],
                                _messageController.messages[index].senderId ==
                                    currentUser.id,
                                true),
                          ],
                        );
                      }

                      final message = _messageController.messages[index];
                      final isCurrentUser = message.senderId == currentUser.id;
                      final showAvatar = index == 0 ||
                          _messageController.messages[index - 1].senderId !=
                              message.senderId;

                      return _buildMessageItem(
                          message, isCurrentUser, showAvatar);
                    },
                  ),

                  // إظهار مؤشر التحميل في أسفل القائمة عند إرسال رسالة
                  if (_messageController.isSendingMessage.value)
                    Positioned(
                      bottom: 0,
                      right: 0,
                      left: 0,
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        color: Colors.white.withAlpha(200),
                        child: const Center(
                          child: SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                            ),
                          ),
                        ),
                      ),
                    ),
                  if (_messageController.isLoadingMessages.value)
                    Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        alignment: Alignment.center,
                        child: const CircularProgressIndicator(
                          strokeWidth: 2,
                        ),
                      ),
                    ),
                ],
              );
            }),
          ),

          // Message input
          Column(
            children: [
              // عرض الرسالة المراد الرد عليها
              Obx(() => _replyToMessage.value != null
                  ? Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        border: Border(
                          top: BorderSide(
                              color: Colors.grey.shade300, width: 0.5),
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 4,
                            height: 40,
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'الرد على ${_messageController.getUserName(_replyToMessage.value!.senderId)}',
                                  style: AppStyles.labelMedium.copyWith(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _replyToMessage.value!.content,
                                  style: AppStyles.bodySmall,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close, size: 16),
                            onPressed: () {
                              _replyToMessage.value = null;
                            },
                          ),
                        ],
                      ),
                    )
                  : const SizedBox.shrink()),

              // عرض قائمة الإشارات
              Obx(() => _showMentionsList.value
                  ? Container(
                      constraints: const BoxConstraints(maxHeight: 200),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(color: Colors.grey.shade300),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(13),
                            blurRadius: 4,
                            offset: const Offset(0, -2),
                          ),
                        ],
                      ),
                      child: _filteredUsers.isEmpty
                          ? const ListTile(
                              title: Text('لا يوجد مستخدمين مطابقين'),
                            )
                          : ListView.builder(
                              shrinkWrap: true,
                              itemCount: _filteredUsers.length,
                              itemBuilder: (context, index) {
                                final user = _filteredUsers[index];
                                return ListTile(
                                  leading: CircleAvatar(
                                    backgroundColor: AppColors.primary,
                                    child: Text(
                                      user.name.substring(0, 1).toUpperCase(),
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ),
                                  ),
                                  title: Text(user.name),
                                  subtitle: Text(user.email),
                                  onTap: () => _insertMention(user),
                                );
                              },
                            ),
                    )
                  : const SizedBox.shrink()),

              // عرض منتقي الرموز التعبيرية
              Obx(() => _showEmojiPicker.value
                  ? SizedBox(
                      height: 250,
                      child: EmojiPicker(
                        onEmojiSelected: _onEmojiSelected,
                        textEditingController: _textController,
                      ),
                    )
                  : const SizedBox.shrink()),

              // حقل إدخال الرسالة
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(13),
                      blurRadius: 5,
                      offset: const Offset(0, -1),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // زر المرفقات
                    IconButton(
                      icon: Obx(() => _messageController.isAttachingFile.value
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.attach_file)),
                      tooltip: 'إرفاق ملف',
                      onPressed: _messageController.isAttachingFile.value
                          ? null
                          : _showAttachmentOptions,
                    ),

                    // حقل إدخال الرسالة
                    Expanded(
                      child: TextField(
                        controller: _textController,
                        focusNode: _textFieldFocusNode,
                        decoration: InputDecoration(
                          hintText: 'اكتب رسالة...',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(24),
                            borderSide: BorderSide.none,
                          ),
                          filled: true,
                          fillColor: Colors.grey.shade100,
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          suffixIcon: IconButton(
                            icon: Obx(() => Icon(
                                  _showEmojiPicker.value
                                      ? Icons.keyboard
                                      : Icons.emoji_emotions_outlined,
                                )),
                            tooltip: _showEmojiPicker.value
                                ? 'عرض لوحة المفاتيح'
                                : 'إضافة رموز تعبيرية',
                            onPressed: _toggleEmojiPicker,
                          ),
                        ),
                        textInputAction: TextInputAction.send,
                        onSubmitted: (_) => _sendMessage(),
                        textDirection: TextDirection.rtl, // دعم اللغة العربية
                        onTap: () {
                          if (_showEmojiPicker.value) {
                            _showEmojiPicker.value = false;
                          }
                        },
                      ),
                    ),

                    // زر الإرسال
                    const SizedBox(width: 8),
                    Obx(() => IconButton(
                          icon: _messageController.isSendingMessage.value
                              ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child:
                                      CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Icon(Icons.send),
                          tooltip: 'إرسال',
                          onPressed: _messageController.isSendingMessage.value
                              ? null
                              : _sendMessage,
                          color: AppColors.primary,
                        )),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMessageItem(
      Message message, bool isCurrentUser, bool showAvatar) {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) {
      return const SizedBox.shrink();
    }

    return MessageItemWidget(
      message: message,
      isCurrentUser: isCurrentUser,
      showAvatar: showAvatar,
      currentUserId: currentUser.id,
      isAdmin: currentUser.role == UserRole.admin,
      onReply: (message) {
        _replyToMessage.value = message;
      },
      onPin: (message) {
        _messageController
            .pinMessage(message.id, currentUser.id)
            .then((success) {
          if (success) {
            Get.snackbar(
              'تم التثبيت',
              'تم تثبيت الرسالة بنجاح',
              snackPosition: SnackPosition.BOTTOM,
            );
          } else {
            Get.snackbar(
              'خطأ',
              'فشل في تثبيت الرسالة',
              snackPosition: SnackPosition.BOTTOM,
            );
          }
        });
      },
      onUnpin: (message) {
        _messageController
            .unpinMessage(message.id, currentUser.id)
            .then((success) {
          if (success) {
            Get.snackbar(
              'تم إلغاء التثبيت',
              'تم إلغاء تثبيت الرسالة بنجاح',
              snackPosition: SnackPosition.BOTTOM,
            );
          } else {
            Get.snackbar(
              'خطأ',
              'فشل في إلغاء تثبيت الرسالة',
              snackPosition: SnackPosition.BOTTOM,
            );
          }
        });
      },
      onMarkForFollowUp: (message) {
        // عرض مربع حوار لاختيار وقت المتابعة
        _showFollowUpDatePicker(message);
      },
      onEdit: (message) {
        _showEditMessageDialog(message);
      },
      onDelete: (message) {
        _confirmDeleteMessage(message);
      },
    );
  }

  /// عرض منتقي التاريخ للمتابعة
  void _showFollowUpDatePicker(Message message) async {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return;

    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );

      if (time != null && mounted) {
        final followUpAt = DateTime(
          date.year,
          date.month,
          date.day,
          time.hour,
          time.minute,
        );

        final success = await _messageController.markMessageForFollowUp(
            message.id, currentUser.id,
            followUpAt: followUpAt);

        if (success) {
          Get.snackbar(
            'تم التعليم للمتابعة',
            'تم تعليم الرسالة للمتابعة بنجاح',
            snackPosition: SnackPosition.BOTTOM,
          );
        } else {
          Get.snackbar(
            'خطأ',
            'فشل في تعليم الرسالة للمتابعة',
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      }
    }
  }

  /// تأكيد حذف الرسالة
  void _confirmDeleteMessage(Message message) {
    Get.defaultDialog(
      title: 'حذف الرسالة',
      middleText: 'هل أنت متأكد من حذف هذه الرسالة؟',
      textConfirm: 'حذف',
      textCancel: 'إلغاء',
      confirmTextColor: Colors.white,
      onConfirm: () {
        _messageController.deleteMessage(message.id).then((success) {
          if (success) {
            Get.snackbar(
              'تم الحذف',
              'تم حذف الرسالة بنجاح',
              snackPosition: SnackPosition.BOTTOM,
            );
          } else {
            Get.snackbar(
              'خطأ',
              'فشل في حذف الرسالة: ${_messageController.error.value}',
              snackPosition: SnackPosition.BOTTOM,
            );
          }
        });
        Get.back();
      },
    );
  }

  /// عرض مربع حوار تعديل الرسالة
  void _showEditMessageDialog(Message message) {
    final editController = TextEditingController(text: message.content);

    Get.defaultDialog(
      title: 'تعديل الرسالة',
      content: TextField(
        controller: editController,
        decoration: const InputDecoration(
          hintText: 'أدخل النص الجديد للرسالة',
          border: OutlineInputBorder(),
        ),
        maxLines: 3,
        textDirection: TextDirection.rtl,
      ),
      textConfirm: 'حفظ',
      textCancel: 'إلغاء',
      confirmTextColor: Colors.white,
      onConfirm: () {
        final newContent = editController.text.trim();
        if (newContent.isEmpty) {
          Get.snackbar(
            'خطأ',
            'لا يمكن أن يكون محتوى الرسالة فارغًا',
            snackPosition: SnackPosition.BOTTOM,
          );
          return;
        }

        // البحث عن الإشارات في النص الجديد
        final mentionedUserIds = <String>[];
        final mentionRegex = RegExp(r'@([^\s]+)');
        final matches = mentionRegex.allMatches(newContent);

        for (final match in matches) {
          final mentionedName = match.group(1);
          if (mentionedName != null) {
            // البحث عن المستخدم بالاسم
            final mentionedUser = _userController.users.firstWhereOrNull(
                (user) =>
                    user.name.toLowerCase() == mentionedName.toLowerCase());

            if (mentionedUser != null &&
                !mentionedUserIds.contains(mentionedUser.id)) {
              mentionedUserIds.add(mentionedUser.id);
            }
          }
        }

        // تحديث الرسالة في قاعدة البيانات
        final updatedMessage = message.copyWith(
          content: newContent,
          mentionedUserIds: mentionedUserIds,
          updatedAt: DateTime.now(),
        );

        // تحديث الرسالة في الواجهة
        final index =
            _messageController.messages.indexWhere((m) => m.id == message.id);
        if (index >= 0) {
          _messageController.messages[index] = updatedMessage;
        }

        Get.back();
        Get.snackbar(
          'تم التعديل',
          'تم تعديل الرسالة بنجاح',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
    );
  }

  void _showChatOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.info_outline),
                title: const Text('View group info'),
                onTap: () {
                  Navigator.pop(context);
                  Get.to(() => ChatInfoScreen(chatGroup: widget.chatGroup));
                },
              ),
              if (!widget.chatGroup.isDirectMessage)
                ListTile(
                  leading: const Icon(Icons.person_add),
                  title: const Text('Add members'),
                  onTap: () {
                    Navigator.pop(context);
                    Get.to(() => AddMembersScreen(chatGroup: widget.chatGroup));
                  },
                ),
              ListTile(
                leading: const Icon(Icons.search),
                title: const Text('Search in conversation'),
                onTap: () async {
                  Navigator.pop(context);
                  final result = await Get.to<String>(() =>
                      SearchConversationScreen(chatGroup: widget.chatGroup));
                  if (result != null) {
                    // تم العثور على رسالة، التمرير إليها
                    _scrollToMessage(result);
                  }
                },
              ),
              ListTile(
                leading: const Icon(Icons.notifications_off_outlined),
                title: const Text('Mute notifications'),
                onTap: () {
                  Navigator.pop(context);
                  Get.to(() =>
                      MuteNotificationsScreen(chatGroup: widget.chatGroup));
                },
              ),
              ListTile(
                leading: const Icon(Icons.exit_to_app, color: Colors.red),
                title: const Text('Leave conversation',
                    style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.pop(context);
                  _confirmLeaveGroup();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _confirmLeaveGroup() {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return;

    Get.defaultDialog(
      title: 'Leave Conversation',
      middleText:
          'Are you sure you want to leave this conversation? You will no longer receive messages from this group.',
      textConfirm: 'Leave',
      textCancel: 'Cancel',
      confirmTextColor: Colors.white,
      onConfirm: () {
        _messageController
            .leaveGroup(widget.chatGroup.id, currentUser.id)
            .then((success) {
          if (success) {
            Get.back();
            Get.back();
            Get.snackbar(
              'Success',
              'You have left the conversation',
              snackPosition: SnackPosition.BOTTOM,
            );
          } else {
            Get.back();
            Get.snackbar(
              'Error',
              'Failed to leave the conversation: ${_messageController.error.value}',
              snackPosition: SnackPosition.BOTTOM,
            );
          }
        });
      },
    );
  }

  /// التقاط صورة
  Future<void> _pickImage(ImageSource source) async {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return;

    try {
      final pickedFile = await ImagePicker().pickImage(
        source: source,
        imageQuality: 70,
      );

      if (pickedFile != null) {
        final imageFile = File(pickedFile.path);

        // إرسال الصورة مباشرة بدون طلب تعليق
        _messageController
            .sendImageMessage(
          widget.chatGroup.id,
          currentUser.id,
          imageFile,
          caption: null, // لا تعليق في المحادثات
          mentionedUserIds:
              _mentionedUserIds.isNotEmpty ? _mentionedUserIds.toList() : null,
          replyToMessageId: _replyToMessage.value?.id,
        )
            .then((_) {
          // إعادة تعيين الرد
          _replyToMessage.value = null;
          _mentionedUserIds.clear();
        });
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء التقاط الصورة: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// اختيار ملف
  Future<void> _pickFile() async {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return;

    try {
      final result = await FilePicker.platform.pickFiles();

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        final fileName = result.files.first.name;

        // إرسال الملف
        _messageController
            .sendFileMessage(
          widget.chatGroup.id,
          currentUser.id,
          file,
          fileName: fileName,
          mentionedUserIds:
              _mentionedUserIds.isNotEmpty ? _mentionedUserIds.toList() : null,
          replyToMessageId: _replyToMessage.value?.id,
        )
            .then((_) {
          // إعادة تعيين الرد
          _replyToMessage.value = null;
          _mentionedUserIds.clear();
        });
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء اختيار الملف: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // مشاركة الموقع الحالي
  Future<void> _shareLocation() async {
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) return;

    try {
      // التحقق من صلاحيات الوصول إلى الموقع
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          Get.snackbar(
            'خطأ',
            'تم رفض صلاحية الوصول إلى الموقع',
            snackPosition: SnackPosition.BOTTOM,
          );
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        Get.snackbar(
          'خطأ',
          'صلاحيات الموقع مرفوضة بشكل دائم، يرجى تمكينها من إعدادات الجهاز',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      // الحصول على الموقع الحالي
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // إنشاء رابط خرائط جوجل
      final googleMapsUrl =
          'https://www.google.com/maps/search/?api=1&query=${position.latitude},${position.longitude}';

      // إرسال الموقع كرسالة
      final message = await _messageController.sendMessage(
        widget.chatGroup.id,
        currentUser.id,
        'شارك موقعه الحالي: $googleMapsUrl',
        contentType: MessageContentType.location,
      );

      if (message != null) {
        Get.snackbar(
          'نجاح',
          'تم مشاركة الموقع بنجاح',
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في مشاركة الموقع: ${_messageController.error.value}',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في مشاركة الموقع: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  /// عرض خيارات المرفقات
  void _showAttachmentOptions() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.blue,
                child: Icon(Icons.photo, color: Colors.white),
              ),
              title: const Text('صورة'),
              subtitle: const Text('إرفاق صورة من المعرض'),
              onTap: () {
                Get.back();
                _pickImage(ImageSource.gallery);
              },
            ),
            ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.green,
                child: Icon(Icons.camera_alt, color: Colors.white),
              ),
              title: const Text('كاميرا'),
              subtitle: const Text('التقاط صورة جديدة'),
              onTap: () {
                Get.back();
                _pickImage(ImageSource.camera);
              },
            ),
            ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.orange,
                child: Icon(Icons.insert_drive_file, color: Colors.white),
              ),
              title: const Text('ملف'),
              subtitle: const Text('إرفاق ملف من الجهاز'),
              onTap: () {
                Get.back();
                _pickFile();
              },
            ),
            ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.red,
                child: Icon(Icons.location_on, color: Colors.white),
              ),
              title: const Text('موقع'),
              subtitle: const Text('مشاركة موقعك الحالي'),
              onTap: () {
                Get.back();
                _shareLocation();
              },
            ),
          ],
        ),
      ),
    );
  }

  // Build a preview for file attachments - غير مستخدمة حالياً
  // TODO: استخدام هذه الدالة في عرض معاينة المرفقات في واجهة المستخدم
  /*
  Widget _buildAttachmentPreview(MessageAttachment attachment) {
    final isImage = attachment.fileType.startsWith('image/');

    if (isImage) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.file(
          File(attachment.filePath),
          height: 150,
          width: 150,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              height: 150,
              width: 150,
              color: Colors.grey.shade200,
              child: const Icon(Icons.image_not_supported, size: 48, color: Colors.grey),
            );
          },
        ),
      );
    } else {
      // For non-image files, show an icon based on file type
      IconData iconData;
      Color iconColor;

      if (attachment.fileType.contains('pdf')) {
        iconData = Icons.picture_as_pdf;
        iconColor = Colors.red;
      } else if (attachment.fileType.contains('word') ||
                attachment.fileType.contains('document')) {
        iconData = Icons.description;
        iconColor = Colors.blue;
      } else if (attachment.fileType.contains('spreadsheet') ||
                attachment.fileType.contains('excel')) {
        iconData = Icons.table_chart;
        iconColor = Colors.green;
      } else if (attachment.fileType.contains('audio')) {
        iconData = Icons.audio_file;
        iconColor = Colors.orange;
      } else if (attachment.fileType.contains('video')) {
        iconData = Icons.video_file;
        iconColor = Colors.purple;
      } else {
        iconData = Icons.insert_drive_file;
        iconColor = Colors.grey;
      }

      return Container(
        height: 100,
        width: 100,
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(iconData, size: 48, color: iconColor),
            const SizedBox(height: 8),
            Text(
              attachment.fileName.length > 15
                ? '${attachment.fileName.substring(0, 12)}...'
                : attachment.fileName,
              style: AppStyles.bodySmall,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      );
    }
  }
  */

  /// فتح مرفقات الرسالة - غير مستخدمة حالياً
  // TODO: استخدام هذه الدالة في فتح مرفقات الرسائل من واجهة المستخدم
  /*
  Future<void> _openMessageAttachments(Message message) async {
    if (message.attachmentIds == null || message.attachmentIds!.isEmpty) {
      Get.snackbar(
        'خطأ',
        'لا توجد مرفقات لهذه الرسالة',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    try {
      // البحث عن المرفقات في الذاكرة
      final attachments = _messageController.messageAttachments
          .where((attachment) => message.attachmentIds!.contains(attachment.id))
          .toList();

      if (attachments.isEmpty) {
        // إذا لم يتم العثور على المرفقات في الذاكرة، قم بتحميلها
        await _messageController.loadAttachmentsForMessage(message.id);

        // البحث مرة أخرى بعد التحميل
        final refreshedAttachments = _messageController.messageAttachments
            .where((attachment) => message.attachmentIds!.contains(attachment.id))
            .toList();

        if (refreshedAttachments.isEmpty) {
          Get.snackbar(
            'خطأ',
            'لم يتم العثور على المرفقات',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red.shade100,
            colorText: Colors.red.shade800,
          );
          return;
        }

        // فتح المرفق الأول (يمكن تعديله لعرض قائمة إذا كان هناك أكثر من مرفق)
        _openAttachment(refreshedAttachments.first);
      } else {
        // فتح المرفق الأول (يمكن تعديله لعرض قائمة إذا كان هناك أكثر من مرفق)
        _openAttachment(attachments.first);
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في فتح المرفق: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }
  */

  /// فتح المرفق - غير مستخدمة حالياً
  /// @param attachment المرفق المراد فتحه
  // TODO: استخدام هذه الدالة في فتح المرفقات من واجهة المستخدم
  /*
  void _openAttachment(MessageAttachment attachment) {
    // تحديد نوع المرفق
    final isImage = attachment.fileType.startsWith('image/');

    if (isImage) {
      // عرض الصورة في نافذة منبثقة
      Get.dialog(
        Dialog(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AppBar(
                title: Text(attachment.fileName),
                leading: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.download),
                    onPressed: () {
                      _downloadAttachment(attachment);
                    },
                    tooltip: 'تنزيل',
                  ),
                ],
              ),
              Flexible(
                child: InteractiveViewer(
                  minScale: 0.5,
                  maxScale: 3.0,
                  child: Image.file(
                    File(attachment.filePath),
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      return Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.image_not_supported,
                              color: Colors.grey,
                              size: 48,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'فشل تحميل الصورة',
                              style: AppStyles.bodyMedium,
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      // فتح الملف باستخدام url_launcher
      if (attachment.filePath.isNotEmpty) {
        launchUrl(Uri.file(attachment.filePath));
      } else {
        Get.snackbar(
          'خطأ',
          'لا يمكن فتح الملف',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.shade100,
          colorText: Colors.red.shade800,
        );
      }
    }
  }

  /// تنزيل المرفق - غير مستخدمة حالياً
  /// @param attachment المرفق المراد تنزيله
  // TODO: استخدام هذه الدالة في تنزيل المرفقات من واجهة المستخدم
  void _downloadAttachment(MessageAttachment attachment) {
    // تنزيل الملف
    if (attachment.filePath.isNotEmpty) {
      launchUrl(Uri.file(attachment.filePath));
    } else {
      Get.snackbar(
        'خطأ',
        'لا يمكن تنزيل الملف',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
      );
    }
  }
  */
}
