import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../models/dashboard_item.dart';
import '../models/chart_type_enum.dart';
import '../models/advanced_filter_options.dart';

import '../controllers/auth_controller.dart';

/// وحدة تحكم تخطيط لوحة المعلومات
///
/// تدير هذه الوحدة حالة تخطيط لوحة المعلومات وتخزينها
class DashboardLayoutController extends GetxController {
  // مفتاح تخزين تخطيط لوحة المعلومات
  static const String _storageKey = 'dashboard_layout';

  // مفتاح تخزين وضع عرض الشبكة
  static const String _gridLayoutKey = 'dashboard_grid_layout';

  // حالة التحميل
  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  // رسالة الخطأ
  final _errorMessage = RxnString();
  String? get errorMessage => _errorMessage.value;

  // قائمة عناصر لوحة المعلومات
  final _dashboardItems = <DashboardItem>[].obs;
  List<DashboardItem> get dashboardItems => _dashboardItems;

  // حالة عرض الشبكة المنظمة
  final useGridLayout = true.obs;

  // مخزن البيانات
  final _storage = GetStorage();

  @override
  void onInit() {
    super.onInit();
    // تحميل تخطيط لوحة المعلومات المحفوظ
    loadSavedLayout();
    // تحميل تفضيل عرض الشبكة
    loadGridLayoutPreference();
  }

  /// تحميل تفضيل عرض الشبكة
  void loadGridLayoutPreference() {
    try {
      final savedPreference = _storage.read(_gridLayoutKey);
      if (savedPreference != null) {
        useGridLayout.value = savedPreference as bool;
      }
    } catch (e) {
      debugPrint('خطأ في تحميل تفضيل عرض الشبكة: $e');
    }
  }

  /// حفظ تفضيل عرض الشبكة
  Future<void> saveGridLayoutPreference(bool value) async {
    try {
      await _storage.write(_gridLayoutKey, value);
      useGridLayout.value = value;
    } catch (e) {
      debugPrint('خطأ في حفظ تفضيل عرض الشبكة: $e');
    }
  }

  /// تحميل تخطيط لوحة المعلومات المحفوظ
  Future<void> loadSavedLayout() async {
    _isLoading.value = true;
    _errorMessage.value = null;

    try {
      // قراءة البيانات المخزنة
      final savedLayoutJson = _storage.read<String>(_storageKey);

      if (savedLayoutJson != null) {
        // تحويل البيانات المخزنة إلى قائمة عناصر
        final savedLayout = jsonDecode(savedLayoutJson) as List<dynamic>;

        // تحديث قائمة العناصر
        // ملاحظة: هنا نفترض أن محتوى العناصر سيتم توفيره من الخارج
        // لأن محتوى Widget لا يمكن تخزينه
        _dashboardItems.value = savedLayout.map((item) {
          // استخراج نوع المخطط إذا كان موجودًا
          ChartType? chartType;
          if (item['chartType'] != null) {
            final chartTypeIndex = item['chartType'] as int;
            if (chartTypeIndex >= 0 &&
                chartTypeIndex < ChartType.values.length) {
              chartType = ChartType.values[chartTypeIndex];
            }
          }

          // استخراج خيارات التصفية إذا كانت موجودة
          AdvancedFilterOptions? filterOptions;
          if (item['filterOptions'] != null) {
            filterOptions =
                AdvancedFilterOptions.fromJson(item['filterOptions']);
          }

          // استخراج حجم العنصر في وضع الشبكة إذا كان موجودًا
          int gridSize = 1; // القيمة الافتراضية
          if (item['gridSize'] != null) {
            gridSize = item['gridSize'] as int;
          }

          return DashboardItem(
            id: item['id'],
            title: item['title'],
            content: Container(), // سيتم استبداله لاحقًا
            position: Offset(
              item['position']['dx'].toDouble(),
              item['position']['dy'].toDouble(),
            ),
            size: Size(
              item['size']['width'].toDouble(),
              item['size']['height'].toDouble(),
            ),
            chartType: chartType,
            filterOptions: filterOptions,
            gridSize: gridSize,
          );
        }).toList();
      }

      _isLoading.value = false;
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء تحميل تخطيط لوحة المعلومات: $e';
      _isLoading.value = false;
    }
  }

  /// حفظ تخطيط لوحة المعلومات
  Future<void> saveLayout(List<DashboardItem> items) async {
    _isLoading.value = true;
    _errorMessage.value = null;

    try {
      // تحويل قائمة العناصر إلى صيغة قابلة للتخزين
      final layoutToSave = items.map((item) {
        return {
          'id': item.id,
          'title': item.title,
          'position': {
            'dx': item.position.dx,
            'dy': item.position.dy,
          },
          'size': {
            'width': item.size.width,
            'height': item.size.height,
          },
          'chartType': item.chartType?.index,
          'filterOptions': item.filterOptions?.toJson(),
        };
      }).toList();

      // تخزين البيانات
      await _storage.write(_storageKey, jsonEncode(layoutToSave));

      // تحديث قائمة العناصر
      _dashboardItems.value = List.from(items);

      _isLoading.value = false;
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء حفظ تخطيط لوحة المعلومات: $e';
      _isLoading.value = false;
    }
  }

  /// تحديث محتوى العناصر
  ///
  /// يستخدم هذا الأسلوب لتحديث محتوى العناصر بعد تحميلها من التخزين
  /// لأن محتوى Widget لا يمكن تخزينه
  void updateItemsContent(Map<String, Widget> contentMap) {
    final updatedItems = _dashboardItems.map((item) {
      if (contentMap.containsKey(item.id)) {
        return DashboardItem(
          id: item.id,
          title: item.title,
          content: contentMap[item.id]!,
          position: item.position,
          size: item.size,
          chartType: item.chartType,
          filterOptions: item.filterOptions,
        );
      }
      return item;
    }).toList();

    _dashboardItems.value = updatedItems;
  }

  /// إضافة عنصر جديد
  void addItem(DashboardItem item) {
    _dashboardItems.add(item);
    saveLayout(_dashboardItems);
  }

  /// حذف عنصر
  void removeItem(String id) {
    _dashboardItems.removeWhere((item) => item.id == id);
    saveLayout(_dashboardItems);
  }

  /// تحديث موقع عنصر
  void updateItemPosition(String id, Offset position) {
    final index = _dashboardItems.indexWhere((item) => item.id == id);
    if (index != -1) {
      final item = _dashboardItems[index];
      _dashboardItems[index] = DashboardItem(
        id: item.id,
        title: item.title,
        content: item.content,
        position: position,
        size: item.size,
        chartType: item.chartType,
        filterOptions: item.filterOptions,
      );
      saveLayout(_dashboardItems);
    }
  }

  /// تحديث حجم عنصر
  void updateItemSize(String id, Size size) {
    final index = _dashboardItems.indexWhere((item) => item.id == id);
    if (index != -1) {
      final item = _dashboardItems[index];
      _dashboardItems[index] = DashboardItem(
        id: item.id,
        title: item.title,
        content: item.content,
        position: item.position,
        size: size,
        chartType: item.chartType,
        filterOptions: item.filterOptions,
      );
      saveLayout(_dashboardItems);
    }
  }

  /// إعادة ضبط تخطيط لوحة المعلومات
  void resetLayout() {
    _dashboardItems.clear();
    _storage.remove(_storageKey);
  }

  /// حفظ تخطيط لوحة المعلومات في قاعدة البيانات
  Future<void> saveDashboardLayout(List<Map<String, dynamic>> layout) async {
    _isLoading.value = true;
    _errorMessage.value = null;

    try {
      // حفظ التخطيط محليًا
      await _storage.write(_storageKey, jsonEncode(layout));

      // حفظ التخطيط في قاعدة البيانات إذا كان المستخدم مسجل الدخول
      final authController = Get.find<AuthController>();
      final databaseHelper = Get.find<DatabaseHelper>();

      if (authController.currentUser.value != null) {
        final userId = authController.currentUser.value!.id;
        await databaseHelper.saveDashboardLayout(userId, layout);
      }

      _isLoading.value = false;
    } catch (e) {
      _errorMessage.value = 'حدث خطأ أثناء حفظ تخطيط لوحة المعلومات: $e';
      _isLoading.value = false;
    }
  }
}
