import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../models/custom_role_model.dart';
import '../../models/interface_model.dart';
import '../../models/permission_model.dart';
import '../../database/custom_role_repository.dart';
import '../../database/custom_permission_repository.dart';

/// تبويب إدارة صلاحيات الواجهات
///
/// يوفر واجهة شاملة لإدارة صلاحيات الوصول للواجهات المختلفة
class InterfacePermissionsTab extends StatefulWidget {
  const InterfacePermissionsTab({super.key});

  @override
  State<InterfacePermissionsTab> createState() =>
      _InterfacePermissionsTabState();
}

class _InterfacePermissionsTabState extends State<InterfacePermissionsTab> {
  final CustomRoleRepository _roleRepository = Get.find<CustomRoleRepository>();
  final CustomPermissionRepository _permissionRepository =
      Get.find<CustomPermissionRepository>();

  List<CustomRole> _roles = [];
  List<InterfaceModel> _interfaces = [];
  final Map<String, Map<String, bool>> _permissions =
      {}; // roleId -> interfaceName -> hasAccess
  bool _isLoading = true;
  String? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// تحميل البيانات
  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل الأدوار
      _roles = await _roleRepository.getAllRoles();

      // تحميل الواجهات
      _interfaces = InterfaceService.getAllInterfaces();

      // تحميل الصلاحيات الحالية
      await _loadPermissions();
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
      Get.snackbar('خطأ', 'فشل في تحميل البيانات');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// تحميل الصلاحيات الحالية
  Future<void> _loadPermissions() async {
    _permissions.clear();

    for (final role in _roles) {
      _permissions[role.id] = {};

      for (final interface in _interfaces) {
        final hasAccess = await _permissionRepository.hasCustomRolePermission(
          role.id,
          PermissionType.view,
          PermissionScope.interfaces,
          description: interface.name,
        );
        _permissions[role.id]![interface.name] = hasAccess;
      }
    }
  }

  /// تحديث صلاحية واجهة
  Future<void> _updateInterfacePermission(
    String roleId,
    String interfaceName,
    bool hasAccess,
  ) async {
    try {
      if (hasAccess) {
        await _permissionRepository.grantCustomRolePermission(
          roleId,
          PermissionType.view,
          PermissionScope.interfaces,
          description: interfaceName,
        );
      } else {
        await _permissionRepository.revokeCustomRolePermission(
          roleId,
          PermissionType.view,
          PermissionScope.interfaces,
          description: interfaceName,
        );
      }

      setState(() {
        _permissions[roleId]![interfaceName] = hasAccess;
      });

      Get.snackbar(
        'نجح',
        hasAccess ? 'تم منح الصلاحية بنجاح' : 'تم إلغاء الصلاحية بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      debugPrint('خطأ في تحديث الصلاحية: $e');
      Get.snackbar('خطأ', 'فشل في تحديث الصلاحية');
    }
  }

  /// منح جميع صلاحيات الفئة للدور
  Future<void> _grantCategoryPermissions(String roleId, String category) async {
    final interfaces =
        _interfaces.where((i) => i.category == category).toList();

    for (final interface in interfaces) {
      await _updateInterfacePermission(roleId, interface.name, true);
    }
  }

  /// إلغاء جميع صلاحيات الفئة للدور
  Future<void> _revokeCategoryPermissions(
      String roleId, String category) async {
    final interfaces =
        _interfaces.where((i) => i.category == category).toList();

    for (final interface in interfaces) {
      await _updateInterfacePermission(roleId, interface.name, false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // شريط الأدوات
        _buildToolbar(),

        const SizedBox(height: 16),

        // فلتر الفئات
        _buildCategoryFilter(),

        const SizedBox(height: 16),

        // جدول الصلاحيات
        Expanded(child: _buildPermissionsTable()),
      ],
    );
  }

  /// بناء شريط الأدوات
  Widget _buildToolbar() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(Icons.security, size: 24),
            const SizedBox(width: 8),
            const Text(
              'إدارة صلاحيات الواجهات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Spacer(),
            ElevatedButton.icon(
              onPressed: _loadData,
              icon: const Icon(Icons.refresh),
              label: const Text('تحديث'),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء فلتر الفئات
  Widget _buildCategoryFilter() {
    final categories =
        InterfaceService.getInterfacesGroupedByCategory().keys.toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Text('فلترة حسب الفئة: '),
            const SizedBox(width: 16),
            DropdownButton<String?>(
              value: _selectedCategory,
              hint: const Text('جميع الفئات'),
              items: [
                const DropdownMenuItem<String?>(
                  value: null,
                  child: Text('جميع الفئات'),
                ),
                ...categories.map((category) => DropdownMenuItem<String>(
                      value: category,
                      child: Text(
                          InterfaceService.getCategoryDisplayName(category)),
                    )),
              ],
              onChanged: (value) {
                setState(() => _selectedCategory = value);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء جدول الصلاحيات
  Widget _buildPermissionsTable() {
    final filteredInterfaces = _selectedCategory == null
        ? _interfaces
        : _interfaces.where((i) => i.category == _selectedCategory).toList();

    if (filteredInterfaces.isEmpty) {
      return const Center(
        child: Text('لا توجد واجهات في هذه الفئة'),
      );
    }

    return Card(
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: SingleChildScrollView(
          child: DataTable(
            columnSpacing: 16,
            columns: [
              const DataColumn(
                label: Text('الواجهة',
                    style: TextStyle(fontWeight: FontWeight.bold)),
              ),
              ..._roles.map((role) => DataColumn(
                    label: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          role.name,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        if (_selectedCategory != null) ...[
                          const SizedBox(height: 4),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              TextButton(
                                onPressed: () => _grantCategoryPermissions(
                                    role.id, _selectedCategory!),
                                child: const Text('منح الكل',
                                    style: TextStyle(fontSize: 10)),
                              ),
                              TextButton(
                                onPressed: () => _revokeCategoryPermissions(
                                    role.id, _selectedCategory!),
                                child: const Text('إلغاء الكل',
                                    style: TextStyle(fontSize: 10)),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  )),
            ],
            rows: filteredInterfaces.map((interface) {
              return DataRow(
                cells: [
                  DataCell(
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          interface.displayName,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        if (interface.description != null)
                          Text(
                            interface.description!,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        Text(
                          InterfaceService.getCategoryDisplayName(
                              interface.category),
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.blue[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  ..._roles.map((role) {
                    final hasAccess =
                        _permissions[role.id]?[interface.name] ?? false;
                    return DataCell(
                      Checkbox(
                        value: hasAccess,
                        onChanged: (value) {
                          if (value != null) {
                            _updateInterfacePermission(
                                role.id, interface.name, value);
                          }
                        },
                      ),
                    );
                  }),
                ],
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}
