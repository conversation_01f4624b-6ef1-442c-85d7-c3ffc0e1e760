import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../models/text_document_model.dart';
import '../services/text_document_service.dart';
import '../utils/app_utils.dart';

/// وحدة تحكم المستندات النصية
///
/// توفر وظائف لإدارة المستندات النصية في واجهة المستخدم
class TextDocumentController extends GetxController {
  final TextDocumentService _textDocumentService =
      Get.find<TextDocumentService>();

  // قائمة المستندات النصية
  final RxList<TextDocument> documents = <TextDocument>[].obs;

  // المستند الحالي
  final Rx<TextDocument?> currentDocument = Rx<TextDocument?>(null);

  // محرر النصوص
  final Rx<TextEditingController?> textController =
      Rx<TextEditingController?>(null);

  // حالة التحميل
  final RxBool isLoading = false.obs;

  // رسالة الخطأ
  final RxString error = ''.obs;

  @override
  void onInit() {
    super.onInit();
    // تهيئة محرر النصوص
    textController.value = TextEditingController();
  }

  /// تحميل جميع المستندات النصية
  Future<void> loadAllDocuments() async {
    isLoading.value = true;
    error.value = '';

    try {
      final result = await _textDocumentService.getAllTextDocuments();
      documents.assignAll(result);
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحميل المستندات: ${e.toString()}';
      debugPrint('خطأ في تحميل المستندات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل المستندات النصية للمستخدم الحالي
  Future<void> loadMyDocuments() async {
    isLoading.value = true;
    error.value = '';

    try {
      final result = await _textDocumentService.getMyTextDocuments();
      documents.assignAll(result);
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحميل المستندات: ${e.toString()}';
      debugPrint('خطأ في تحميل المستندات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل المستندات النصية المرتبطة بمهمة
  Future<void> loadDocumentsByTaskId(String taskId) async {
    isLoading.value = true;
    error.value = '';

    try {
      final result =
          await _textDocumentService.getTextDocumentsByTaskId(taskId);
      documents.assignAll(result);
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحميل المستندات: ${e.toString()}';
      debugPrint('خطأ في تحميل المستندات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل مستند نصي بواسطة المعرف
  Future<void> loadDocumentById(String id) async {
    // تعيين حالة التحميل
    isLoading.value = true;
    error.value = '';

    TextDocument? document;

    try {
      // تحميل المستند من الخدمة
      document = await _textDocumentService.getTextDocumentById(id);

      if (document == null) {
        error.value = 'المستند غير موجود';
        isLoading.value = false;
        return;
      }
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحميل المستند: ${e.toString()}';
      debugPrint('خطأ في تحميل المستند: $e');
      isLoading.value = false;
      return;
    }

    // تحميل محتوى المستند في محرر النصوص
    // استخدام الطريقة الجديدة للحصول على النص
    final plainText = document.getDocumentText();

    // تحديث الحالة بعد اكتمال جميع العمليات
    final newTextController = TextEditingController(text: plainText);

    // تحديث القيم بعد اكتمال جميع العمليات
    currentDocument.value = document;
    textController.value = newTextController;
    isLoading.value = false;
  }

  /// إنشاء مستند نصي جديد
  Future<TextDocument?> createDocument({
    required String title,
    required TextDocumentType type,
    String? taskId,
    bool isShared = false,
    List<String>? accessUserIds,
    String? content,
  }) async {
    isLoading.value = true;
    error.value = '';

    try {
      // تحويل محتوى المحرر إلى JSON
      final documentContent =
          content ?? TextDocument.textToJson(textController.value!.text);

      // إنشاء المستند
      final document = await _textDocumentService.createTextDocument(
        title: title,
        content: documentContent,
        type: type,
        taskId: taskId,
        isShared: isShared,
        accessUserIds: accessUserIds,
      );

      if (document != null) {
        // إضافة المستند إلى القائمة
        documents.add(document);
        currentDocument.value = document;

        // عرض رسالة نجاح
        AppUtils.showSuccessSnackbar(
          'تم إنشاء المستند',
          'تم إنشاء المستند بنجاح',
        );

        return document;
      } else {
        error.value = 'فشل في إنشاء المستند';
        AppUtils.showErrorSnackbar(
          'خطأ',
          'فشل في إنشاء المستند',
        );
        return null;
      }
    } catch (e) {
      error.value = 'حدث خطأ أثناء إنشاء المستند: ${e.toString()}';
      debugPrint('خطأ في إنشاء المستند: $e');
      AppUtils.showErrorSnackbar(
        'خطأ',
        'حدث خطأ أثناء إنشاء المستند: ${e.toString()}',
      );
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديث مستند نصي
  Future<bool> updateDocument({
    required String id,
    String? title,
    TextDocumentType? type,
    String? taskId,
    bool? isShared,
    List<String>? accessUserIds,
    String? content,
  }) async {
    isLoading.value = true;
    error.value = '';

    try {
      // تحويل محتوى المحرر إلى JSON
      final documentContent =
          content ?? TextDocument.textToJson(textController.value!.text);

      // تحديث المستند
      final success = await _textDocumentService.updateTextDocument(
        id: id,
        title: title,
        content: documentContent,
        type: type,
        taskId: taskId,
        isShared: isShared,
        accessUserIds: accessUserIds,
      );

      if (success) {
        // تحديث المستند في القائمة
        await loadDocumentById(id);

        // عرض رسالة نجاح
        AppUtils.showSuccessSnackbar(
          'تم التحديث',
          'تم تحديث المستند بنجاح',
        );

        return true;
      } else {
        error.value = 'فشل في تحديث المستند';
        AppUtils.showErrorSnackbar(
          'خطأ',
          'فشل في تحديث المستند',
        );
        return false;
      }
    } catch (e) {
      error.value = 'حدث خطأ أثناء تحديث المستند: ${e.toString()}';
      debugPrint('خطأ في تحديث المستند: $e');
      AppUtils.showErrorSnackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث المستند: ${e.toString()}',
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// حذف مستند نصي
  Future<bool> deleteDocument(String id) async {
    isLoading.value = true;
    error.value = '';

    try {
      final success = await _textDocumentService.deleteTextDocument(id);

      if (success) {
        // حذف المستند من القائمة
        documents.removeWhere((doc) => doc.id == id);

        // إذا كان المستند المحذوف هو المستند الحالي، نقوم بإعادة تعيينه
        if (currentDocument.value?.id == id) {
          currentDocument.value = null;
          textController.value = TextEditingController();
        }

        // عرض رسالة نجاح
        AppUtils.showSuccessSnackbar(
          'تم الحذف',
          'تم حذف المستند بنجاح',
        );

        return true;
      } else {
        error.value = 'فشل في حذف المستند';
        AppUtils.showErrorSnackbar(
          'خطأ',
          'فشل في حذف المستند',
        );
        return false;
      }
    } catch (e) {
      error.value = 'حدث خطأ أثناء حذف المستند: ${e.toString()}';
      debugPrint('خطأ في حذف المستند: $e');
      AppUtils.showErrorSnackbar(
        'خطأ',
        'حدث خطأ أثناء حذف المستند: ${e.toString()}',
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// البحث عن مستندات نصية
  Future<void> searchDocuments(String query) async {
    if (query.isEmpty) {
      await loadAllDocuments();
      return;
    }

    isLoading.value = true;
    error.value = '';

    try {
      final result = await _textDocumentService.searchTextDocuments(query);
      documents.assignAll(result);
    } catch (e) {
      error.value = 'حدث خطأ أثناء البحث عن المستندات: ${e.toString()}';
      debugPrint('خطأ في البحث عن المستندات: $e');
    } finally {
      isLoading.value = false;
    }
  }
}
