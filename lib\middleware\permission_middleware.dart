import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/auth_controller.dart';

import '../models/user_model.dart';
import '../routes/app_routes.dart';

/// وسيط التحقق من الصلاحيات
///
/// يستخدم للتحقق من صلاحيات المستخدم قبل الانتقال إلى الشاشات المختلفة
class PermissionMiddleware extends GetMiddleware {
  final AuthController _authController = Get.find<AuthController>();

  @override
  RouteSettings? redirect(String? route) {
    // إذا كان المسار هو صفحة تسجيل الدخول أو التسجيل أو الصفحة الرئيسية، نسمح بالوصول
    if (route == AppRoutes.login ||
        route == AppRoutes.register ||
        route == AppRoutes.home) {
      return null;
    }

    // التحقق من تسجيل الدخول
    final currentUser = _authController.currentUser.value;
    if (currentUser == null) {
      // إذا لم يكن المستخدم مسجل الدخول، نوجهه إلى صفحة تسجيل الدخول
      return const RouteSettings(name: AppRoutes.login);
    }

    // المدير لديه وصول لجميع الواجهات
    if (currentUser.role == UserRole.admin) {
      return null;
    }

    // المسارات الخاصة بالمدير
    if (route == AppRoutes.admin) {
      Get.snackbar(
        'خطأ في الصلاحيات',
        'ليس لديك صلاحية للوصول إلى لوحة التحكم الإدارية',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return const RouteSettings(name: AppRoutes.home);
    }

    // المسارات الخاصة بمدير القسم
    if (route?.startsWith('/department') == true) {
      if (currentUser.role != UserRole.departmentManager) {
        Get.snackbar(
          'خطأ في الصلاحيات',
          'ليس لديك صلاحية للوصول إلى لوحة تحكم القسم',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return const RouteSettings(name: AppRoutes.home);
      }
      return null;
    }

    // نسمح بالوصول مؤقتًا، ثم نتحقق من الصلاحية في الشاشة نفسها
    // سيتم استخدام checkInterfaceAccess في الشاشة للتحقق من الصلاحية
    debugPrint(
        'السماح بالوصول المؤقت إلى: $route، سيتم التحقق من الصلاحية في الشاشة');
    return null;
  }
}

/// دالة مساعدة للتحقق من صلاحية الوصول إلى واجهة معينة
///
/// تستخدم في الشاشات التي لا يمكن التحقق من صلاحياتها عن طريق المسار
Future<bool> checkInterfaceAccess(String interfaceName) async {
  final permissionService = Get.find<UnifiedPermissionService>();
  final authController = Get.find<AuthController>();

  // إذا كان المستخدم مدير النظام، فلديه وصول إلى جميع الواجهات
  if (authController.isAdmin) {
    return true;
  }

  final currentUser = authController.currentUser.value;
  if (currentUser == null) {
    Get.offNamed('/login');
    return false;
  }

  // التحقق من صلاحية الوصول إلى الواجهة
  final hasAccess = await permissionService.hasInterfaceAccess(
      currentUser.id, interfaceName, PermissionType.interfaceAccess);

  if (!hasAccess) {
    Get.snackbar(
      'خطأ في الصلاحيات',
      'ليس لديك صلاحية للوصول إلى هذه الصفحة',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
    Get.offNamed('/home');
    return false;
  }

  return true;
}

/// دالة مساعدة للتحقق من صلاحية معينة
///
/// تستخدم للتحقق من صلاحيات المهام والبيانات
Future<bool> checkPermission(PermissionType type, PermissionScope scope) async {
  final permissionService = UnifiedPermissionService();

  // تحديد الواجهة المناسبة للمجال
  String interfaceName;
  switch (scope) {
    case PermissionScope.tasks:
      interfaceName = 'tasks';
      break;
    case PermissionScope.users:
      interfaceName = 'users';
      break;
    case PermissionScope.departments:
      interfaceName = 'departments';
      break;
    case PermissionScope.messages:
      interfaceName = 'messages';
      break;
    case PermissionScope.reports:
      interfaceName = 'reports';
      break;
    default:
      interfaceName = '';
  }

  return await permissionService.checkPermission(
    interfaceName: interfaceName,
    type: type,
    scope: scope,
  );
}

/// دالة مساعدة للتحقق من صلاحية مع عرض رسالة خطأ
///
/// تستخدم للتحقق من الصلاحيات وعرض رسالة خطأ إذا لم تكن متوفرة
Future<bool> checkPermissionWithMessage(
    PermissionType type, PermissionScope scope, String errorMessage) async {
  final permissionService = UnifiedPermissionService();

  // تحديد الواجهة المناسبة للمجال
  String interfaceName;
  switch (scope) {
    case PermissionScope.tasks:
      interfaceName = 'tasks';
      break;
    case PermissionScope.users:
      interfaceName = 'users';
      break;
    case PermissionScope.departments:
      interfaceName = 'departments';
      break;
    case PermissionScope.messages:
      interfaceName = 'messages';
      break;
    case PermissionScope.reports:
      interfaceName = 'reports';
      break;
    default:
      interfaceName = '';
  }

  final hasPermission = await permissionService.checkPermission(
    interfaceName: interfaceName,
    type: type,
    scope: scope,
  );

  if (!hasPermission) {
    permissionService.showPermissionError(errorMessage);
  }

  return hasPermission;
}
