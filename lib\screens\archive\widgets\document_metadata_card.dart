import 'package:flutter/material.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';

/// بطاقة بيانات وصفية للوثيقة
///
/// تعرض مجموعة من البيانات الوصفية للوثيقة في شكل بطاقة
class DocumentMetadataCard extends StatelessWidget {
  /// عنوان البطاقة
  final String title;
  
  /// أيقونة البطاقة
  final IconData icon;
  
  /// قائمة العناصر (مفتاح-قيمة)
  final List<Map<String, String>> items;

  const DocumentMetadataCard({
    super.key,
    required this.title,
    required this.icon,
    required this.items,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان البطاقة
            Row(
              children: [
                Icon(
                  icon,
                  color: AppColors.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: AppStyles.subtitle1.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            const Divider(height: 24),
            
            // عناصر البيانات الوصفية
            ...items.map((item) => _buildMetadataItem(item)),
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataItem(Map<String, String> item) {
    final entry = item.entries.first;
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '${entry.key}:',
              style: AppStyles.body2.copyWith(
                color: Colors.grey.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              entry.value,
              style: AppStyles.body1,
            ),
          ),
        ],
      ),
    );
  }
}
